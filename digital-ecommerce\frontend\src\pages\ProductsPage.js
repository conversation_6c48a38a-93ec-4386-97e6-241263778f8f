import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSearchParams, Link } from 'react-router-dom';
import {
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  StarIcon,
  HeartIcon,
  ShoppingBagIcon,
  AdjustmentsHorizontalIcon,
  ChevronRightIcon,
  HomeIcon,
  // TagIcon,
  ClockIcon,
  TruckIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid, HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { categories, products, getProductsByCategory } from '../data/products';
import { useCart } from '../components/ShoppingCart';
import { useUser } from '../contexts/UserContext';
import toast, { Toaster } from 'react-hot-toast';

const ProductsPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');
  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');
  const [productType, setProductType] = useState('all'); // all, physical, digital
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [selectedRating, setSelectedRating] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist, isAuthenticated } = useUser();

  const handleAddToCart = (product) => {
    addToCart(product);
    toast.success(`${product.name} added to cart!`, {
      duration: 3000,
      position: 'top-right',
    });
  };

  const handleWishlistToggle = (product) => {
    if (!isAuthenticated) {
      toast.error('Please sign in to add items to your wishlist');
      return;
    }

    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
      toast.success('Removed from wishlist');
    } else {
      addToWishlist(product.id);
      toast.success('Added to wishlist');
    }
  };

  // Get current category data
  const currentCategory = categories.find(cat => cat.id === selectedCategory);
  const subcategories = currentCategory ? [
    { id: 'all', name: 'All ' + currentCategory.name, count: getProductsByCategory(selectedCategory).length },
    ...currentCategory.subcategories.map(sub => ({
      id: sub,
      name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
      count: products.filter(p => p.subcategory === sub).length
    }))
  ] : [];

  const productTypeOptions = [
    { id: 'all', name: 'All Products', count: products.length },
    { id: 'physical', name: 'Physical Products', count: products.filter(p => p.type === 'physical').length },
    { id: 'digital', name: 'Digital Products', count: products.filter(p => p.type === 'digital').length }
  ];

  const sortOptions = [
    { value: 'featured', label: 'Featured' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'newest', label: 'Newest First' },
    { value: 'name', label: 'Name: A to Z' },
    { value: 'popularity', label: 'Most Popular' }
  ];

  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;
      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;
      const typeMatch = productType === 'all' || product.type === productType;
      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];
      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;

      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;
    });

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => b.id.localeCompare(a.id));
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default:
        // Featured - keep original order
        break;
    }

    return filtered;
  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);

  const ProductCard = ({ product, index }) => (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className={`bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 ${
        viewMode === 'list' ? 'flex' : ''
      }`}
    >
      <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>
        <img
          src={product.images ? product.images[0] : product.image}
          alt={product.name}
          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${
            viewMode === 'list' ? 'h-48' : 'h-64'
          }`}
        />
        {product.badge && (
          <div className="absolute top-4 left-4">
            <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${
              product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'
            }`}>
              {product.badge}
            </span>
          </div>
        )}
        {product.type === 'digital' && (
          <div className="absolute top-4 left-4 mt-8">
            <span className="bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
              Digital
            </span>
          </div>
        )}
        <div className="absolute top-4 right-4">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => handleWishlistToggle(product)}
            className="bg-white bg-opacity-90 p-2 rounded-full shadow-lg hover:bg-opacity-100 transition-all"
          >
            {isInWishlist(product.id) ? (
              <HeartIconSolid className="w-5 h-5 text-red-500" />
            ) : (
              <HeartIcon className="w-5 h-5 text-gray-600 hover:text-red-500" />
            )}
          </motion.button>
        </div>
        {!product.inStock && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <span className="text-white font-semibold">Out of Stock</span>
          </div>
        )}
      </div>

      <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>
        <div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${
            viewMode === 'list' ? 'text-xl' : 'text-lg'
          }`}>
            {product.name}
          </h3>

          <div className="flex items-center mb-3">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                i < Math.floor(product.rating) ? (
                  <StarIconSolid key={i} className="w-4 h-4 text-yellow-400" />
                ) : (
                  <StarIcon key={i} className="w-4 h-4 text-gray-300" />
                )
              ))}
            </div>
            <span className="text-sm text-gray-600 ml-2">
              {product.rating} ({product.reviews})
            </span>
          </div>

          <div className="flex items-center space-x-2 mb-3">
            <span className="text-2xl font-bold text-light-orange-600">
              ${product.price}
            </span>
            {product.originalPrice && product.originalPrice > product.price && (
              <span className="text-lg text-gray-500 line-through">
                ${product.originalPrice}
              </span>
            )}
          </div>

          {/* Product Type Specific Info */}
          {product.type === 'digital' ? (
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <ClockIcon className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">Instant Delivery</span>
              </div>
              {product.platforms && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Platforms:</span>
                  <span className="text-sm text-gray-800">{product.platforms.join(', ')}</span>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* Color Options for Physical Products */}
              {product.colors && (
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-sm text-gray-600">Colors:</span>
                  {product.colors.slice(0, 3).map((color, index) => (
                    <div
                      key={index}
                      className={`w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${
                        color === 'black' || color === 'Black' ? 'bg-black' :
                        color === 'white' || color === 'White' ? 'bg-white' :
                        color === 'blue' || color === 'Blue' ? 'bg-blue-500' :
                        color === 'red' || color === 'Red' ? 'bg-red-500' :
                        color === 'silver' || color === 'Silver' ? 'bg-gray-400' :
                        color === 'gold' || color === 'Gold' ? 'bg-yellow-400' :
                        'bg-gray-300'
                      }`}
                    />
                  ))}
                  {product.colors.length > 3 && (
                    <span className="text-sm text-gray-500">+{product.colors.length - 3}</span>
                  )}
                </div>
              )}
              {/* Shipping Info */}
              <div className="flex items-center space-x-2 mb-2">
                <TruckIcon className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-600">
                  {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}
                </span>
              </div>
            </>
          )}

          {/* Stock Status */}
          <div className="flex items-center space-x-2 mb-4">
            <CheckCircleIcon className={`w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`} />
            <span className={`text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
              {product.inStock ? 'In Stock' : 'Out of Stock'}
              {product.stockCount && product.inStock && ` (${product.stockCount} available)`}
            </span>
          </div>
        </div>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => handleAddToCart(product)}
          disabled={!product.inStock}
          className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
            product.inStock
              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <ShoppingBagIcon className="w-5 h-5" />
          <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="flex items-center text-gray-600 hover:text-light-orange-600 transition-colors">
              <HomeIcon className="w-4 h-4 mr-1" />
              Home
            </Link>
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">Products</span>
            {selectedCategory !== 'all' && (
              <>
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                <span className="text-light-orange-600 font-medium">
                  {currentCategory?.name}
                </span>
              </>
            )}
            {selectedSubcategory !== 'all' && (
              <>
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                <span className="text-light-orange-600 font-medium">
                  {subcategories.find(sub => sub.id === selectedSubcategory)?.name}
                </span>
              </>
            )}
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
              {selectedCategory === 'all' ? 'All Products' : currentCategory?.name || 'Products'}
            </h1>
            <p className="text-xl text-light-orange-100 max-w-2xl mx-auto">
              {selectedCategory === 'all'
                ? 'Discover our amazing collection of premium products'
                : currentCategory?.description || 'Explore our curated selection'
              }
            </p>
          </motion.div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-4 justify-center">
            {[{ id: 'all', name: 'All Products', icon: '🛍️' }, ...categories].map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  setSelectedCategory(category.id);
                  setSelectedSubcategory('all');
                  setSearchParams({ category: category.id });
                }}
                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-light-orange-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden p-2 text-gray-600"
                >
                  <AdjustmentsHorizontalIcon className="w-5 h-5" />
                </button>
              </div>

              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                {/* Product Type */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Product Type</h4>
                  <div className="space-y-2">
                    {productTypeOptions.map(type => (
                      <button
                        key={type.id}
                        onClick={() => setProductType(type.id)}
                        className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                          productType === type.id
                            ? 'bg-light-orange-100 text-light-orange-700'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex justify-between">
                          <span>{type.name}</span>
                          <span className="text-sm">({type.count})</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Subcategories */}
                {selectedCategory !== 'all' && subcategories.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">
                      {currentCategory?.name} Categories
                    </h4>
                    <div className="space-y-2">
                      {subcategories.map(subcategory => (
                        <button
                          key={subcategory.id}
                          onClick={() => setSelectedSubcategory(subcategory.id)}
                          className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                            selectedSubcategory === subcategory.id
                              ? 'bg-light-orange-100 text-light-orange-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <div className="flex justify-between">
                            <span>{subcategory.name}</span>
                            <span className="text-sm">({subcategory.count})</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Price Range */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">
                    Price Range: ${priceRange[0]} - ${priceRange[1]}
                  </h4>
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                    className="w-full"
                  />
                </div>

                {/* Rating Filter */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Minimum Rating</h4>
                  <div className="space-y-2">
                    {[4, 3, 2, 1, 0].map(rating => (
                      <button
                        key={rating}
                        onClick={() => setSelectedRating(rating)}
                        className={`flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${
                          selectedRating === rating
                            ? 'bg-light-orange-100 text-light-orange-700'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <StarIconSolid
                              key={i}
                              className={`w-4 h-4 ${
                                i < rating ? 'text-yellow-400' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span>{rating > 0 ? `${rating}+ Stars` : 'All Ratings'}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <p className="text-gray-600">
                    Showing {filteredAndSortedProducts.length} of {products.length} products
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Sort Dropdown */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300"
                  >
                    {sortOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>

                  {/* View Mode Toggle */}
                  <div className="flex bg-gray-100 rounded-lg p-1">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 rounded-md transition-colors ${
                        viewMode === 'grid'
                          ? 'bg-white text-light-orange-600 shadow-sm'
                          : 'text-gray-600'
                      }`}
                    >
                      <Squares2X2Icon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 rounded-md transition-colors ${
                        viewMode === 'list'
                          ? 'bg-white text-light-orange-600 shadow-sm'
                          : 'text-gray-600'
                      }`}
                    >
                      <ListBulletIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <AnimatePresence mode="wait">
              <motion.div
                key={`${viewMode}-${selectedCategory}-${sortBy}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={`${
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8'
                    : 'space-y-6'
                }`}
              >
                {filteredAndSortedProducts.map((product, index) => (
                  <ProductCard key={product.id} product={product} index={index} />
                ))}
              </motion.div>
            </AnimatePresence>

            {filteredAndSortedProducts.length === 0 && (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <FunnelIcon className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600">Try adjusting your filters to see more results.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
