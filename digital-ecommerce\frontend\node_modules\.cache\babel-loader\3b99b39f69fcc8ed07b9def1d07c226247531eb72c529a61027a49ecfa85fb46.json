{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n  return function createMotionComponent(Component) {\n    let {\n      forwardMotionProps\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      forwardMotionProps: false\n    };\n    const baseConfig = isSVGComponent(Component) ? svgMotionConfig : htmlMotionConfig;\n    const config = _objectSpread(_objectSpread({}, baseConfig), {}, {\n      preloadedFeatures,\n      useRender: createUseRender(forwardMotionProps),\n      createVisualElement,\n      Component\n    });\n    return createRendererMotionComponent(config);\n  };\n}\nexport { createMotionComponentFactory };", "map": {"version": 3, "names": ["createRendererMotionComponent", "createUseRender", "isSVGComponent", "htmlMotionConfig", "svgMotionConfig", "createMotionComponentFactory", "preloadedFeatures", "createVisualElement", "createMotionComponent", "Component", "forwardMotionProps", "arguments", "length", "undefined", "baseConfig", "config", "_objectSpread", "useRender"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/components/create-factory.mjs"], "sourcesContent": ["import { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\n\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n    return function createMotionComponent(Component, { forwardMotionProps } = { forwardMotionProps: false }) {\n        const baseConfig = isSVGComponent(Component)\n            ? svgMotionConfig\n            : htmlMotionConfig;\n        const config = {\n            ...baseConfig,\n            preloadedFeatures,\n            useRender: createUseRender(forwardMotionProps),\n            createVisualElement,\n            Component,\n        };\n        return createRendererMotionComponent(config);\n    };\n}\n\nexport { createMotionComponentFactory };\n"], "mappings": ";AAAA,SAASA,6BAA6B,QAAQ,wBAAwB;AACtE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,4BAA4BA,CAACC,iBAAiB,EAAEC,mBAAmB,EAAE;EAC1E,OAAO,SAASC,qBAAqBA,CAACC,SAAS,EAA0D;IAAA,IAAxD;MAAEC;IAAmB,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;MAAED,kBAAkB,EAAE;IAAM,CAAC;IACnG,MAAMI,UAAU,GAAGZ,cAAc,CAACO,SAAS,CAAC,GACtCL,eAAe,GACfD,gBAAgB;IACtB,MAAMY,MAAM,GAAAC,aAAA,CAAAA,aAAA,KACLF,UAAU;MACbR,iBAAiB;MACjBW,SAAS,EAAEhB,eAAe,CAACS,kBAAkB,CAAC;MAC9CH,mBAAmB;MACnBE;IAAS,EACZ;IACD,OAAOT,6BAA6B,CAACe,MAAM,CAAC;EAChD,CAAC;AACL;AAEA,SAASV,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}