import React, { forwardRef } from 'react';

const Input = forwardRef(({
  label,
  error,
  helperText,
  required = false,
  className = '',
  containerClassName = '',
  labelClassName = '',
  type = 'text',
  ...props
}, ref) => {
  const baseInputClasses = 'w-full px-4 py-3 border rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400';
  
  const inputClasses = error
    ? `${baseInputClasses} border-red-300 text-red-900 placeholder-red-300 focus:ring-red-300 focus:border-red-400`
    : `${baseInputClasses} border-gray-300 text-gray-900 placeholder-gray-500 ${className}`;

  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        type={type}
        className={inputClasses}
        {...props}
      />
      
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
