{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/postcss7-compat": "^2.2.17", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "web-vitals": "^1.1.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}