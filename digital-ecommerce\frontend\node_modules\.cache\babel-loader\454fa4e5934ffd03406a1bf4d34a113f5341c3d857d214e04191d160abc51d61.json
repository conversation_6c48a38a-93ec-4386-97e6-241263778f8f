{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Input.js\";\nimport React, { forwardRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Input = /*#__PURE__*/forwardRef(_c = ({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  labelClassName = '',\n  type = 'text',\n  ...props\n}, ref) => {\n  const baseInputClasses = 'w-full px-4 py-3 border rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400';\n  const inputClasses = error ? `${baseInputClasses} border-red-300 text-red-900 placeholder-red-300 focus:ring-red-300 focus:border-red-400` : `${baseInputClasses} border-gray-300 text-gray-900 placeholder-gray-500 ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-2 ${containerClassName}`,\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      className: `block text-sm font-medium text-gray-700 ${labelClassName}`,\n      children: [label, required && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-500 ml-1\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 24\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: ref,\n      type: type,\n      className: inputClasses,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-red-600 flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-4 h-4 mr-1\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          fillRule: \"evenodd\",\n          d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n          clipRule: \"evenodd\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this), helperText && !error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-gray-500\",\n      children: helperText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Input;\nInput.displayName = 'Input';\nexport default Input;\nvar _c, _c2;\n$RefreshReg$(_c, \"Input$forwardRef\");\n$RefreshReg$(_c2, \"Input\");", "map": {"version": 3, "names": ["React", "forwardRef", "jsxDEV", "_jsxDEV", "Input", "_c", "label", "error", "helperText", "required", "className", "containerClassName", "labelClassName", "type", "props", "ref", "baseInputClasses", "inputClasses", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "fillRule", "d", "clipRule", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Input.js"], "sourcesContent": ["import React, { forwardRef } from 'react';\n\nconst Input = forwardRef(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  labelClassName = '',\n  type = 'text',\n  ...props\n}, ref) => {\n  const baseInputClasses = 'w-full px-4 py-3 border rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400';\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 text-red-900 placeholder-red-300 focus:ring-red-300 focus:border-red-400`\n    : `${baseInputClasses} border-gray-300 text-gray-900 placeholder-gray-500 ${className}`;\n\n  return (\n    <div className={`space-y-2 ${containerClassName}`}>\n      {label && (\n        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        type={type}\n        className={inputClasses}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 flex items-center\">\n          <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500\">{helperText}</p>\n      )}\n    </div>\n  );\n});\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,KAAK,gBAAGH,UAAU,CAAAI,EAAA,GAACA,CAAC;EACxBC,KAAK;EACLC,KAAK;EACLC,UAAU;EACVC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,EAAE;EACdC,kBAAkB,GAAG,EAAE;EACvBC,cAAc,GAAG,EAAE;EACnBC,IAAI,GAAG,MAAM;EACb,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,gBAAgB,GAAG,6JAA6J;EAEtL,MAAMC,YAAY,GAAGV,KAAK,GACtB,GAAGS,gBAAgB,0FAA0F,GAC7G,GAAGA,gBAAgB,uDAAuDN,SAAS,EAAE;EAEzF,oBACEP,OAAA;IAAKO,SAAS,EAAE,aAAaC,kBAAkB,EAAG;IAAAO,QAAA,GAC/CZ,KAAK,iBACJH,OAAA;MAAOO,SAAS,EAAE,2CAA2CE,cAAc,EAAG;MAAAM,QAAA,GAC3EZ,KAAK,EACLG,QAAQ,iBAAIN,OAAA;QAAMO,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACR,eAEDnB,OAAA;MACEY,GAAG,EAAEA,GAAI;MACTF,IAAI,EAAEA,IAAK;MACXH,SAAS,EAAEO,YAAa;MAAA,GACpBH;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEDf,KAAK,iBACJJ,OAAA;MAAGO,SAAS,EAAC,wCAAwC;MAAAQ,QAAA,gBACnDf,OAAA;QAAKO,SAAS,EAAC,cAAc;QAACa,IAAI,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAN,QAAA,eACnEf,OAAA;UAAMsB,QAAQ,EAAC,SAAS;UAACC,CAAC,EAAC,mHAAmH;UAACC,QAAQ,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,EACLf,KAAK;IAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ,EAEAd,UAAU,IAAI,CAACD,KAAK,iBACnBJ,OAAA;MAAGO,SAAS,EAAC,uBAAuB;MAAAQ,QAAA,EAAEV;IAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CACrD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,CAAC;AAACM,GAAA,GA/CGxB,KAAK;AAiDXA,KAAK,CAACyB,WAAW,GAAG,OAAO;AAE3B,eAAezB,KAAK;AAAC,IAAAC,EAAA,EAAAuB,GAAA;AAAAE,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}