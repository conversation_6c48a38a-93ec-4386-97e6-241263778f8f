{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{motion}from'framer-motion';import{Link}from'react-router-dom';import{ShoppingBagIcon,CreditCardIcon,TruckIcon,ShieldCheckIcon,ArrowLeftIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{useCart}from'../components/ShoppingCart';import Button from'../components/Button';import Input from'../components/Input';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CheckoutPage=()=>{const{cartItems,totalPrice,clearCart}=useCart();const[step,setStep]=useState(1);// 1: Shipping, 2: Payment, 3: Review, 4: Complete\nconst[isProcessing,setIsProcessing]=useState(false);const[shippingInfo,setShippingInfo]=useState({firstName:'',lastName:'',email:'',phone:'',address:'',city:'',state:'',zipCode:'',country:'United States'});const[paymentInfo,setPaymentInfo]=useState({cardNumber:'',expiryDate:'',cvv:'',nameOnCard:''});const handleInputChange=(section,field,value)=>{if(section==='shipping'){setShippingInfo(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));}else if(section==='payment'){setPaymentInfo(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));}};const handlePlaceOrder=async()=>{setIsProcessing(true);// Simulate order processing\nawait new Promise(resolve=>setTimeout(resolve,3000));setStep(4);clearCart();setIsProcessing(false);toast.success('Order placed successfully!');};const shippingCost=cartItems.some(item=>item.type==='physical')?9.99:0;const tax=totalPrice*0.08;// 8% tax\nconst finalTotal=totalPrice+shippingCost+tax;if(cartItems.length===0&&step!==4){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-16 h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-2\",children:\"Your cart is empty\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Add some products to your cart to proceed with checkout.\"}),/*#__PURE__*/_jsx(Link,{to:\"/products\",children:/*#__PURE__*/_jsx(Button,{children:\"Continue Shopping\"})})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4\",children:/*#__PURE__*/_jsxs(Link,{to:\"/products\",className:\"flex items-center text-gray-600 hover:text-light-orange-600\",children:[/*#__PURE__*/_jsx(ArrowLeftIcon,{className:\"w-5 h-5 mr-2\"}),\"Continue Shopping\"]})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Checkout\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-32\"}),\" \"]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center space-x-8\",children:[{number:1,title:'Shipping',icon:TruckIcon},{number:2,title:'Payment',icon:CreditCardIcon},{number:3,title:'Review',icon:ShieldCheckIcon},{number:4,title:'Complete',icon:CheckCircleIcon}].map(stepItem=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center w-10 h-10 rounded-full \".concat(step>=stepItem.number?'bg-light-orange-500 text-white':'bg-gray-200 text-gray-500'),children:step>stepItem.number?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-6 h-6\"}):/*#__PURE__*/_jsx(stepItem.icon,{className:\"w-6 h-6\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-sm font-medium \".concat(step>=stepItem.number?'text-light-orange-600':'text-gray-500'),children:stepItem.title}),stepItem.number<4&&/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-0.5 ml-4 \".concat(step>stepItem.number?'bg-light-orange-500':'bg-gray-200')})]},stepItem.number))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-2\",children:[step===1&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:\"bg-white rounded-2xl shadow-lg p-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Shipping Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsx(Input,{label:\"First Name\",value:shippingInfo.firstName,onChange:e=>handleInputChange('shipping','firstName',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"Last Name\",value:shippingInfo.lastName,onChange:e=>handleInputChange('shipping','lastName',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"Email\",type:\"email\",value:shippingInfo.email,onChange:e=>handleInputChange('shipping','email',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"Phone\",value:shippingInfo.phone,onChange:e=>handleInputChange('shipping','phone',e.target.value),required:true}),/*#__PURE__*/_jsx(\"div\",{className:\"md:col-span-2\",children:/*#__PURE__*/_jsx(Input,{label:\"Address\",value:shippingInfo.address,onChange:e=>handleInputChange('shipping','address',e.target.value),required:true})}),/*#__PURE__*/_jsx(Input,{label:\"City\",value:shippingInfo.city,onChange:e=>handleInputChange('shipping','city',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"State\",value:shippingInfo.state,onChange:e=>handleInputChange('shipping','state',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"ZIP Code\",value:shippingInfo.zipCode,onChange:e=>handleInputChange('shipping','zipCode',e.target.value),required:true})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8\",children:/*#__PURE__*/_jsx(Button,{onClick:()=>setStep(2),disabled:!shippingInfo.firstName||!shippingInfo.lastName||!shippingInfo.email,fullWidth:true,children:\"Continue to Payment\"})})]}),step===2&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:\"bg-white rounded-2xl shadow-lg p-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Payment Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(Input,{label:\"Name on Card\",value:paymentInfo.nameOnCard,onChange:e=>handleInputChange('payment','nameOnCard',e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{label:\"Card Number\",value:paymentInfo.cardNumber,onChange:e=>handleInputChange('payment','cardNumber',e.target.value),placeholder:\"1234 5678 9012 3456\",required:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsx(Input,{label:\"Expiry Date\",value:paymentInfo.expiryDate,onChange:e=>handleInputChange('payment','expiryDate',e.target.value),placeholder:\"MM/YY\",required:true}),/*#__PURE__*/_jsx(Input,{label:\"CVV\",value:paymentInfo.cvv,onChange:e=>handleInputChange('payment','cvv',e.target.value),placeholder:\"123\",required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8 flex space-x-4\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>setStep(1),fullWidth:true,children:\"Back\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>setStep(3),disabled:!paymentInfo.nameOnCard||!paymentInfo.cardNumber,fullWidth:true,children:\"Review Order\"})]})]}),step===3&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:\"bg-white rounded-2xl shadow-lg p-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Review Your Order\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4 mb-8\",children:cartItems.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"img\",{src:item.image,alt:item.name,className:\"w-16 h-16 object-cover rounded-lg\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:item.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Quantity: \",item.quantity]}),item.type==='digital'&&/*#__PURE__*/_jsx(\"span\",{className:\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\",children:\"Digital Product\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-right\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"font-semibold text-gray-900\",children:[\"$\",(item.price*item.quantity).toFixed(2)]})})]},item.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8 flex space-x-4\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",onClick:()=>setStep(2),fullWidth:true,children:\"Back\"}),/*#__PURE__*/_jsx(Button,{onClick:handlePlaceOrder,loading:isProcessing,fullWidth:true,children:\"Place Order\"})]})]}),step===4&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},className:\"bg-white rounded-2xl shadow-lg p-8 text-center\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-16 h-16 text-green-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:\"Order Complete!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-8\",children:\"Thank you for your purchase. You'll receive a confirmation email shortly.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/products\",children:/*#__PURE__*/_jsx(Button,{fullWidth:true,children:\"Continue Shopping\"})}),/*#__PURE__*/_jsx(Link,{to:\"/orders\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline\",fullWidth:true,children:\"View Order History\"})})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Order Summary\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Subtotal\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold\",children:[\"$\",totalPrice.toFixed(2)]})]}),shippingCost>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Shipping\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold\",children:[\"$\",shippingCost.toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Tax\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold\",children:[\"$\",tax.toFixed(2)]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"border-t pt-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-semibold text-gray-900\",children:\"Total\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg font-bold text-light-orange-600\",children:[\"$\",finalTotal.toFixed(2)]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 text-sm text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-4 h-4 mr-2 text-green-500\"}),\"Secure checkout\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(TruckIcon,{className:\"w-4 h-4 mr-2 text-blue-500\"}),\"Free returns within 30 days\"]})]})]})})]})})]});};export default CheckoutPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "ShoppingBagIcon", "CreditCardIcon", "TruckIcon", "ShieldCheckIcon", "ArrowLeftIcon", "CheckCircleIcon", "useCart", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "CheckoutPage", "cartItems", "totalPrice", "clearCart", "step", "setStep", "isProcessing", "setIsProcessing", "shippingInfo", "setShippingInfo", "firstName", "lastName", "email", "phone", "address", "city", "state", "zipCode", "country", "paymentInfo", "setPaymentInfo", "cardNumber", "expiryDate", "cvv", "nameOnCard", "handleInputChange", "section", "field", "value", "prev", "_objectSpread", "handlePlaceOrder", "Promise", "resolve", "setTimeout", "success", "shippingCost", "some", "item", "type", "tax", "finalTotal", "length", "className", "children", "to", "position", "number", "title", "icon", "map", "stepItem", "concat", "div", "initial", "opacity", "x", "animate", "label", "onChange", "e", "target", "required", "onClick", "disabled", "fullWidth", "placeholder", "variant", "src", "image", "alt", "name", "quantity", "price", "toFixed", "id", "loading", "scale"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/CheckoutPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { \n  ShoppingBagIcon,\n  CreditCardIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  ArrowLeftIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { useCart } from '../components/ShoppingCart';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst CheckoutPage = () => {\n  const { cartItems, totalPrice, clearCart } = useCart();\n  const [step, setStep] = useState(1); // 1: Shipping, 2: Payment, 3: Review, 4: Complete\n  const [isProcessing, setIsProcessing] = useState(false);\n  \n  const [shippingInfo, setShippingInfo] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    country: 'United States'\n  });\n\n  const [paymentInfo, setPaymentInfo] = useState({\n    cardNumber: '',\n    expiryDate: '',\n    cvv: '',\n    nameOnCard: ''\n  });\n\n  const handleInputChange = (section, field, value) => {\n    if (section === 'shipping') {\n      setShippingInfo(prev => ({ ...prev, [field]: value }));\n    } else if (section === 'payment') {\n      setPaymentInfo(prev => ({ ...prev, [field]: value }));\n    }\n  };\n\n  const handlePlaceOrder = async () => {\n    setIsProcessing(true);\n    \n    // Simulate order processing\n    await new Promise(resolve => setTimeout(resolve, 3000));\n    \n    setStep(4);\n    clearCart();\n    setIsProcessing(false);\n    \n    toast.success('Order placed successfully!');\n  };\n\n  const shippingCost = cartItems.some(item => item.type === 'physical') ? 9.99 : 0;\n  const tax = totalPrice * 0.08; // 8% tax\n  const finalTotal = totalPrice + shippingCost + tax;\n\n  if (cartItems.length === 0 && step !== 4) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <ShoppingBagIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Your cart is empty</h2>\n          <p className=\"text-gray-600 mb-6\">Add some products to your cart to proceed with checkout.</p>\n          <Link to=\"/products\">\n            <Button>Continue Shopping</Button>\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      \n      {/* Header */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Link to=\"/products\" className=\"flex items-center text-gray-600 hover:text-light-orange-600\">\n                <ArrowLeftIcon className=\"w-5 h-5 mr-2\" />\n                Continue Shopping\n              </Link>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Checkout</h1>\n            <div className=\"w-32\"></div> {/* Spacer for centering */}\n          </div>\n        </div>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-center space-x-8\">\n            {[\n              { number: 1, title: 'Shipping', icon: TruckIcon },\n              { number: 2, title: 'Payment', icon: CreditCardIcon },\n              { number: 3, title: 'Review', icon: ShieldCheckIcon },\n              { number: 4, title: 'Complete', icon: CheckCircleIcon }\n            ].map((stepItem) => (\n              <div key={stepItem.number} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${\n                  step >= stepItem.number \n                    ? 'bg-light-orange-500 text-white' \n                    : 'bg-gray-200 text-gray-500'\n                }`}>\n                  {step > stepItem.number ? (\n                    <CheckCircleIcon className=\"w-6 h-6\" />\n                  ) : (\n                    <stepItem.icon className=\"w-6 h-6\" />\n                  )}\n                </div>\n                <span className={`ml-2 text-sm font-medium ${\n                  step >= stepItem.number ? 'text-light-orange-600' : 'text-gray-500'\n                }`}>\n                  {stepItem.title}\n                </span>\n                {stepItem.number < 4 && (\n                  <div className={`w-16 h-0.5 ml-4 ${\n                    step > stepItem.number ? 'bg-light-orange-500' : 'bg-gray-200'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {step === 1 && (\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"bg-white rounded-2xl shadow-lg p-8\"\n              >\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Shipping Information</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <Input\n                    label=\"First Name\"\n                    value={shippingInfo.firstName}\n                    onChange={(e) => handleInputChange('shipping', 'firstName', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"Last Name\"\n                    value={shippingInfo.lastName}\n                    onChange={(e) => handleInputChange('shipping', 'lastName', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"Email\"\n                    type=\"email\"\n                    value={shippingInfo.email}\n                    onChange={(e) => handleInputChange('shipping', 'email', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"Phone\"\n                    value={shippingInfo.phone}\n                    onChange={(e) => handleInputChange('shipping', 'phone', e.target.value)}\n                    required\n                  />\n                  <div className=\"md:col-span-2\">\n                    <Input\n                      label=\"Address\"\n                      value={shippingInfo.address}\n                      onChange={(e) => handleInputChange('shipping', 'address', e.target.value)}\n                      required\n                    />\n                  </div>\n                  <Input\n                    label=\"City\"\n                    value={shippingInfo.city}\n                    onChange={(e) => handleInputChange('shipping', 'city', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"State\"\n                    value={shippingInfo.state}\n                    onChange={(e) => handleInputChange('shipping', 'state', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"ZIP Code\"\n                    value={shippingInfo.zipCode}\n                    onChange={(e) => handleInputChange('shipping', 'zipCode', e.target.value)}\n                    required\n                  />\n                </div>\n                <div className=\"mt-8\">\n                  <Button \n                    onClick={() => setStep(2)}\n                    disabled={!shippingInfo.firstName || !shippingInfo.lastName || !shippingInfo.email}\n                    fullWidth\n                  >\n                    Continue to Payment\n                  </Button>\n                </div>\n              </motion.div>\n            )}\n\n            {step === 2 && (\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"bg-white rounded-2xl shadow-lg p-8\"\n              >\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Payment Information</h2>\n                <div className=\"space-y-6\">\n                  <Input\n                    label=\"Name on Card\"\n                    value={paymentInfo.nameOnCard}\n                    onChange={(e) => handleInputChange('payment', 'nameOnCard', e.target.value)}\n                    required\n                  />\n                  <Input\n                    label=\"Card Number\"\n                    value={paymentInfo.cardNumber}\n                    onChange={(e) => handleInputChange('payment', 'cardNumber', e.target.value)}\n                    placeholder=\"1234 5678 9012 3456\"\n                    required\n                  />\n                  <div className=\"grid grid-cols-2 gap-6\">\n                    <Input\n                      label=\"Expiry Date\"\n                      value={paymentInfo.expiryDate}\n                      onChange={(e) => handleInputChange('payment', 'expiryDate', e.target.value)}\n                      placeholder=\"MM/YY\"\n                      required\n                    />\n                    <Input\n                      label=\"CVV\"\n                      value={paymentInfo.cvv}\n                      onChange={(e) => handleInputChange('payment', 'cvv', e.target.value)}\n                      placeholder=\"123\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-8 flex space-x-4\">\n                  <Button variant=\"outline\" onClick={() => setStep(1)} fullWidth>\n                    Back\n                  </Button>\n                  <Button \n                    onClick={() => setStep(3)}\n                    disabled={!paymentInfo.nameOnCard || !paymentInfo.cardNumber}\n                    fullWidth\n                  >\n                    Review Order\n                  </Button>\n                </div>\n              </motion.div>\n            )}\n\n            {step === 3 && (\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"bg-white rounded-2xl shadow-lg p-8\"\n              >\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Review Your Order</h2>\n                \n                {/* Order Items */}\n                <div className=\"space-y-4 mb-8\">\n                  {cartItems.map((item) => (\n                    <div key={item.id} className=\"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg\">\n                      <img\n                        src={item.image}\n                        alt={item.name}\n                        className=\"w-16 h-16 object-cover rounded-lg\"\n                      />\n                      <div className=\"flex-1\">\n                        <h3 className=\"font-semibold text-gray-900\">{item.name}</h3>\n                        <p className=\"text-gray-600\">Quantity: {item.quantity}</p>\n                        {item.type === 'digital' && (\n                          <span className=\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\">\n                            Digital Product\n                          </span>\n                        )}\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"font-semibold text-gray-900\">\n                          ${(item.price * item.quantity).toFixed(2)}\n                        </p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"mt-8 flex space-x-4\">\n                  <Button variant=\"outline\" onClick={() => setStep(2)} fullWidth>\n                    Back\n                  </Button>\n                  <Button \n                    onClick={handlePlaceOrder}\n                    loading={isProcessing}\n                    fullWidth\n                  >\n                    Place Order\n                  </Button>\n                </div>\n              </motion.div>\n            )}\n\n            {step === 4 && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"bg-white rounded-2xl shadow-lg p-8 text-center\"\n              >\n                <CheckCircleIcon className=\"w-16 h-16 text-green-500 mx-auto mb-6\" />\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Order Complete!</h2>\n                <p className=\"text-gray-600 mb-8\">\n                  Thank you for your purchase. You'll receive a confirmation email shortly.\n                </p>\n                <div className=\"space-y-4\">\n                  <Link to=\"/products\">\n                    <Button fullWidth>Continue Shopping</Button>\n                  </Link>\n                  <Link to=\"/orders\">\n                    <Button variant=\"outline\" fullWidth>View Order History</Button>\n                  </Link>\n                </div>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Order Summary</h3>\n              \n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Subtotal</span>\n                  <span className=\"font-semibold\">${totalPrice.toFixed(2)}</span>\n                </div>\n                {shippingCost > 0 && (\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-gray-600\">Shipping</span>\n                    <span className=\"font-semibold\">${shippingCost.toFixed(2)}</span>\n                  </div>\n                )}\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Tax</span>\n                  <span className=\"font-semibold\">${tax.toFixed(2)}</span>\n                </div>\n                <div className=\"border-t pt-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-lg font-semibold text-gray-900\">Total</span>\n                    <span className=\"text-lg font-bold text-light-orange-600\">\n                      ${finalTotal.toFixed(2)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-3 text-sm text-gray-600\">\n                <div className=\"flex items-center\">\n                  <ShieldCheckIcon className=\"w-4 h-4 mr-2 text-green-500\" />\n                  Secure checkout\n                </div>\n                <div className=\"flex items-center\">\n                  <TruckIcon className=\"w-4 h-4 mr-2 text-blue-500\" />\n                  Free returns within 30 days\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CheckoutPage;\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,eAAe,CACfC,cAAc,CACdC,SAAS,CACTC,eAAe,CACfC,aAAa,CACbC,eAAe,KACV,6BAA6B,CACpC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,SAAS,CAAEC,UAAU,CAAEC,SAAU,CAAC,CAAGZ,OAAO,CAAC,CAAC,CACtD,KAAM,CAACa,IAAI,CAAEC,OAAO,CAAC,CAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAE;AACrC,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,CAC/C4B,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,EAAE,CACRC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,eACX,CAAC,CAAC,CAEF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,CAC7CuC,UAAU,CAAE,EAAE,CACdC,UAAU,CAAE,EAAE,CACdC,GAAG,CAAE,EAAE,CACPC,UAAU,CAAE,EACd,CAAC,CAAC,CAEF,KAAM,CAAAC,iBAAiB,CAAGA,CAACC,OAAO,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACnD,GAAIF,OAAO,GAAK,UAAU,CAAE,CAC1BjB,eAAe,CAACoB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CACxD,CAAC,IAAM,IAAIF,OAAO,GAAK,SAAS,CAAE,CAChCN,cAAc,CAACS,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CACvD,CACF,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCxB,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,KAAM,IAAI,CAAAyB,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD5B,OAAO,CAAC,CAAC,CAAC,CACVF,SAAS,CAAC,CAAC,CACXI,eAAe,CAAC,KAAK,CAAC,CAEtBb,KAAK,CAACyC,OAAO,CAAC,4BAA4B,CAAC,CAC7C,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGnC,SAAS,CAACoC,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,IAAI,GAAK,UAAU,CAAC,CAAG,IAAI,CAAG,CAAC,CAChF,KAAM,CAAAC,GAAG,CAAGtC,UAAU,CAAG,IAAI,CAAE;AAC/B,KAAM,CAAAuC,UAAU,CAAGvC,UAAU,CAAGkC,YAAY,CAAGI,GAAG,CAElD,GAAIvC,SAAS,CAACyC,MAAM,GAAK,CAAC,EAAItC,IAAI,GAAK,CAAC,CAAE,CACxC,mBACEP,IAAA,QAAK8C,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvE7C,KAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/C,IAAA,CAACZ,eAAe,EAAC0D,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACpE9C,IAAA,OAAI8C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC7E/C,IAAA,MAAG8C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,0DAAwD,CAAG,CAAC,cAC9F/C,IAAA,CAACb,IAAI,EAAC6D,EAAE,CAAC,WAAW,CAAAD,QAAA,cAClB/C,IAAA,CAACL,MAAM,EAAAoD,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CAC9B,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAEA,mBACE7C,KAAA,QAAK4C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC/C,IAAA,CAACF,OAAO,EAACmD,QAAQ,CAAC,WAAW,CAAE,CAAC,cAGhCjD,IAAA,QAAK8C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC/C,IAAA,QAAK8C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D7C,KAAA,QAAK4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD/C,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C7C,KAAA,CAACf,IAAI,EAAC6D,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAC1F/C,IAAA,CAACR,aAAa,EAACsD,SAAS,CAAC,cAAc,CAAE,CAAC,oBAE5C,EAAM,CAAC,CACJ,CAAC,cACN9C,IAAA,OAAI8C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC9D/C,IAAA,QAAK8C,SAAS,CAAC,MAAM,CAAM,CAAC,IAAC,EAC1B,CAAC,CACH,CAAC,CACH,CAAC,cAGN9C,IAAA,QAAK8C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC/C,IAAA,QAAK8C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D/C,IAAA,QAAK8C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACxD,CACC,CAAEG,MAAM,CAAE,CAAC,CAAEC,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAE9D,SAAU,CAAC,CACjD,CAAE4D,MAAM,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE/D,cAAe,CAAC,CACrD,CAAE6D,MAAM,CAAE,CAAC,CAAEC,KAAK,CAAE,QAAQ,CAAEC,IAAI,CAAE7D,eAAgB,CAAC,CACrD,CAAE2D,MAAM,CAAE,CAAC,CAAEC,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAE3D,eAAgB,CAAC,CACxD,CAAC4D,GAAG,CAAEC,QAAQ,eACbpD,KAAA,QAA2B4C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACtD/C,IAAA,QAAK8C,SAAS,4DAAAS,MAAA,CACZhD,IAAI,EAAI+C,QAAQ,CAACJ,MAAM,CACnB,gCAAgC,CAChC,2BAA2B,CAC9B,CAAAH,QAAA,CACAxC,IAAI,CAAG+C,QAAQ,CAACJ,MAAM,cACrBlD,IAAA,CAACP,eAAe,EAACqD,SAAS,CAAC,SAAS,CAAE,CAAC,cAEvC9C,IAAA,CAACsD,QAAQ,CAACF,IAAI,EAACN,SAAS,CAAC,SAAS,CAAE,CACrC,CACE,CAAC,cACN9C,IAAA,SAAM8C,SAAS,6BAAAS,MAAA,CACbhD,IAAI,EAAI+C,QAAQ,CAACJ,MAAM,CAAG,uBAAuB,CAAG,eAAe,CAClE,CAAAH,QAAA,CACAO,QAAQ,CAACH,KAAK,CACX,CAAC,CACNG,QAAQ,CAACJ,MAAM,CAAG,CAAC,eAClBlD,IAAA,QAAK8C,SAAS,oBAAAS,MAAA,CACZhD,IAAI,CAAG+C,QAAQ,CAACJ,MAAM,CAAG,qBAAqB,CAAG,aAAa,CAC7D,CAAE,CACN,GArBOI,QAAQ,CAACJ,MAsBd,CACN,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENlD,IAAA,QAAK8C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D7C,KAAA,QAAK4C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD7C,KAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BxC,IAAI,GAAK,CAAC,eACTL,KAAA,CAAChB,MAAM,CAACsE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAE9C/C,IAAA,OAAI8C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC/E7C,KAAA,QAAK4C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD/C,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,YAAY,CAClB9B,KAAK,CAAEpB,YAAY,CAACE,SAAU,CAC9BiD,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,WAAW,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC5EkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,WAAW,CACjB9B,KAAK,CAAEpB,YAAY,CAACG,QAAS,CAC7BgD,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,UAAU,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC3EkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,OAAO,CACbnB,IAAI,CAAC,OAAO,CACZX,KAAK,CAAEpB,YAAY,CAACI,KAAM,CAC1B+C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,OAAO,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACxEkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,OAAO,CACb9B,KAAK,CAAEpB,YAAY,CAACK,KAAM,CAC1B8C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,OAAO,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACxEkC,QAAQ,MACT,CAAC,cACFjE,IAAA,QAAK8C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/C,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,SAAS,CACf9B,KAAK,CAAEpB,YAAY,CAACM,OAAQ,CAC5B6C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,SAAS,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC1EkC,QAAQ,MACT,CAAC,CACC,CAAC,cACNjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,MAAM,CACZ9B,KAAK,CAAEpB,YAAY,CAACO,IAAK,CACzB4C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,MAAM,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACvEkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,OAAO,CACb9B,KAAK,CAAEpB,YAAY,CAACQ,KAAM,CAC1B2C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,OAAO,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACxEkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,UAAU,CAChB9B,KAAK,CAAEpB,YAAY,CAACS,OAAQ,CAC5B0C,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,UAAU,CAAE,SAAS,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC1EkC,QAAQ,MACT,CAAC,EACC,CAAC,cACNjE,IAAA,QAAK8C,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB/C,IAAA,CAACL,MAAM,EACLuE,OAAO,CAAEA,CAAA,GAAM1D,OAAO,CAAC,CAAC,CAAE,CAC1B2D,QAAQ,CAAE,CAACxD,YAAY,CAACE,SAAS,EAAI,CAACF,YAAY,CAACG,QAAQ,EAAI,CAACH,YAAY,CAACI,KAAM,CACnFqD,SAAS,MAAArB,QAAA,CACV,qBAED,CAAQ,CAAC,CACN,CAAC,EACI,CACb,CAEAxC,IAAI,GAAK,CAAC,eACTL,KAAA,CAAChB,MAAM,CAACsE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAE9C/C,IAAA,OAAI8C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC9E7C,KAAA,QAAK4C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB/C,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,cAAc,CACpB9B,KAAK,CAAET,WAAW,CAACK,UAAW,CAC9BmC,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,SAAS,CAAE,YAAY,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC5EkC,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,aAAa,CACnB9B,KAAK,CAAET,WAAW,CAACE,UAAW,CAC9BsC,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,SAAS,CAAE,YAAY,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC5EsC,WAAW,CAAC,qBAAqB,CACjCJ,QAAQ,MACT,CAAC,cACF/D,KAAA,QAAK4C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC/C,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,aAAa,CACnB9B,KAAK,CAAET,WAAW,CAACG,UAAW,CAC9BqC,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,SAAS,CAAE,YAAY,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CAC5EsC,WAAW,CAAC,OAAO,CACnBJ,QAAQ,MACT,CAAC,cACFjE,IAAA,CAACJ,KAAK,EACJiE,KAAK,CAAC,KAAK,CACX9B,KAAK,CAAET,WAAW,CAACI,GAAI,CACvBoC,QAAQ,CAAGC,CAAC,EAAKnC,iBAAiB,CAAC,SAAS,CAAE,KAAK,CAAEmC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE,CACrEsC,WAAW,CAAC,KAAK,CACjBJ,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,cACN/D,KAAA,QAAK4C,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/C,IAAA,CAACL,MAAM,EAAC2E,OAAO,CAAC,SAAS,CAACJ,OAAO,CAAEA,CAAA,GAAM1D,OAAO,CAAC,CAAC,CAAE,CAAC4D,SAAS,MAAArB,QAAA,CAAC,MAE/D,CAAQ,CAAC,cACT/C,IAAA,CAACL,MAAM,EACLuE,OAAO,CAAEA,CAAA,GAAM1D,OAAO,CAAC,CAAC,CAAE,CAC1B2D,QAAQ,CAAE,CAAC7C,WAAW,CAACK,UAAU,EAAI,CAACL,WAAW,CAACE,UAAW,CAC7D4C,SAAS,MAAArB,QAAA,CACV,cAED,CAAQ,CAAC,EACN,CAAC,EACI,CACb,CAEAxC,IAAI,GAAK,CAAC,eACTL,KAAA,CAAChB,MAAM,CAACsE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9Bb,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAE9C/C,IAAA,OAAI8C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAG5E/C,IAAA,QAAK8C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B3C,SAAS,CAACiD,GAAG,CAAEZ,IAAI,eAClBvC,KAAA,QAAmB4C,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAClF/C,IAAA,QACEuE,GAAG,CAAE9B,IAAI,CAAC+B,KAAM,CAChBC,GAAG,CAAEhC,IAAI,CAACiC,IAAK,CACf5B,SAAS,CAAC,mCAAmC,CAC9C,CAAC,cACF5C,KAAA,QAAK4C,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB/C,IAAA,OAAI8C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEN,IAAI,CAACiC,IAAI,CAAK,CAAC,cAC5DxE,KAAA,MAAG4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,YAAU,CAACN,IAAI,CAACkC,QAAQ,EAAI,CAAC,CACzDlC,IAAI,CAACC,IAAI,GAAK,SAAS,eACtB1C,IAAA,SAAM8C,SAAS,CAAC,uEAAuE,CAAAC,QAAA,CAAC,iBAExF,CAAM,CACP,EACE,CAAC,cACN/C,IAAA,QAAK8C,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzB7C,KAAA,MAAG4C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,GACxC,CAAC,CAACN,IAAI,CAACmC,KAAK,CAAGnC,IAAI,CAACkC,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC,EACxC,CAAC,CACD,CAAC,GAnBEpC,IAAI,CAACqC,EAoBV,CACN,CAAC,CACC,CAAC,cAEN5E,KAAA,QAAK4C,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/C,IAAA,CAACL,MAAM,EAAC2E,OAAO,CAAC,SAAS,CAACJ,OAAO,CAAEA,CAAA,GAAM1D,OAAO,CAAC,CAAC,CAAE,CAAC4D,SAAS,MAAArB,QAAA,CAAC,MAE/D,CAAQ,CAAC,cACT/C,IAAA,CAACL,MAAM,EACLuE,OAAO,CAAEhC,gBAAiB,CAC1B6C,OAAO,CAAEtE,YAAa,CACtB2D,SAAS,MAAArB,QAAA,CACV,aAED,CAAQ,CAAC,EACN,CAAC,EACI,CACb,CAEAxC,IAAI,GAAK,CAAC,eACTL,KAAA,CAAChB,MAAM,CAACsE,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEsB,KAAK,CAAE,GAAI,CAAE,CACpCpB,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEsB,KAAK,CAAE,CAAE,CAAE,CAClClC,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAE1D/C,IAAA,CAACP,eAAe,EAACqD,SAAS,CAAC,uCAAuC,CAAE,CAAC,cACrE9C,IAAA,OAAI8C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1E/C,IAAA,MAAG8C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2EAElC,CAAG,CAAC,cACJ7C,KAAA,QAAK4C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB/C,IAAA,CAACb,IAAI,EAAC6D,EAAE,CAAC,WAAW,CAAAD,QAAA,cAClB/C,IAAA,CAACL,MAAM,EAACyE,SAAS,MAAArB,QAAA,CAAC,mBAAiB,CAAQ,CAAC,CACxC,CAAC,cACP/C,IAAA,CAACb,IAAI,EAAC6D,EAAE,CAAC,SAAS,CAAAD,QAAA,cAChB/C,IAAA,CAACL,MAAM,EAAC2E,OAAO,CAAC,SAAS,CAACF,SAAS,MAAArB,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC3D,CAAC,EACJ,CAAC,EACI,CACb,EACE,CAAC,cAGN/C,IAAA,QAAK8C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,KAAA,QAAK4C,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/D/C,IAAA,OAAI8C,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAE3E7C,KAAA,QAAK4C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B7C,KAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/C,IAAA,SAAM8C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC/C7C,KAAA,SAAM4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,GAAC,CAAC1C,UAAU,CAACwE,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAC5D,CAAC,CACLtC,YAAY,CAAG,CAAC,eACfrC,KAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/C,IAAA,SAAM8C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,cAC/C7C,KAAA,SAAM4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,GAAC,CAACR,YAAY,CAACsC,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAC9D,CACN,cACD3E,KAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/C,IAAA,SAAM8C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,cAC1C7C,KAAA,SAAM4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,GAAC,CAACJ,GAAG,CAACkC,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EACrD,CAAC,cACN7E,IAAA,QAAK8C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,KAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/C,IAAA,SAAM8C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cAClE7C,KAAA,SAAM4C,SAAS,CAAC,yCAAyC,CAAAC,QAAA,EAAC,GACvD,CAACH,UAAU,CAACiC,OAAO,CAAC,CAAC,CAAC,EACnB,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,cAEN3E,KAAA,QAAK4C,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C7C,KAAA,QAAK4C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/C,IAAA,CAACT,eAAe,EAACuD,SAAS,CAAC,6BAA6B,CAAE,CAAC,kBAE7D,EAAK,CAAC,cACN5C,KAAA,QAAK4C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/C,IAAA,CAACV,SAAS,EAACwD,SAAS,CAAC,4BAA4B,CAAE,CAAC,8BAEtD,EAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}