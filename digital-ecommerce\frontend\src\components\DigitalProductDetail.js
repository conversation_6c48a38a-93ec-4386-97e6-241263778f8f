import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowDownTrayIcon as CloudDownloadIcon,
  ShieldCheckIcon,
  ClockIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const DigitalProductDetail = ({ product }) => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'requirements', name: 'System Requirements' },
    { id: 'installation', name: 'Installation Guide' },
    { id: 'support', name: 'Support' }
  ];

  const renderSystemRequirements = () => {
    if (!product.systemRequirements) return null;

    return (
      <div className="space-y-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">System Requirements</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Object.entries(product.systemRequirements).map(([key, value]) => (
            <div key={key} className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <span className="font-medium text-gray-900">{key}:</span>
                <span className="text-gray-700 text-right ml-4">{value}</span>
              </div>
            </div>
          ))}
        </div>
        
        {/* Platform Compatibility */}
        {product.platforms && (
          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Compatible Platforms</h4>
            <div className="flex flex-wrap gap-3">
              {product.platforms.map((platform, index) => (
                <div key={index} className="flex items-center space-x-2 bg-blue-100 text-blue-800 px-3 py-2 rounded-lg">
                  {platform.includes('Windows') && <ComputerDesktopIcon className="w-4 h-4" />}
                  {platform.includes('macOS') && <ComputerDesktopIcon className="w-4 h-4" />}
                  {platform.includes('iOS') && <DevicePhoneMobileIcon className="w-4 h-4" />}
                  {platform.includes('Android') && <DevicePhoneMobileIcon className="w-4 h-4" />}
                  <span className="text-sm font-medium">{platform}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderInstallationGuide = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">Installation & Activation</h3>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-2">Digital Delivery Process</h4>
            <ol className="list-decimal list-inside space-y-2 text-blue-800">
              <li>Complete your purchase securely</li>
              <li>Receive license key via email within 5 minutes</li>
              <li>Download software from official source</li>
              <li>Enter your license key during installation</li>
              <li>Enjoy your activated software!</li>
            </ol>
          </div>
        </div>
      </div>

      {product.instructions && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="font-medium text-gray-900 mb-3">Special Instructions</h4>
          <p className="text-gray-700">{product.instructions}</p>
        </div>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-yellow-900 mb-2">Important Notes</h4>
            <ul className="list-disc list-inside space-y-1 text-yellow-800 text-sm">
              <li>License keys are non-refundable once delivered</li>
              <li>Each license is for single-user activation only</li>
              <li>Keep your license key safe for future reinstallations</li>
              <li>Contact support if you encounter activation issues</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSupport = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">Support & Warranty</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-3">
            <CheckCircleIcon className="w-6 h-6 text-green-600" />
            <h4 className="font-medium text-green-900">What's Included</h4>
          </div>
          <ul className="space-y-2 text-green-800 text-sm">
            <li>• Genuine license activation</li>
            <li>• Email delivery within 5 minutes</li>
            <li>• Installation support</li>
            <li>• Activation troubleshooting</li>
            <li>• License replacement if needed</li>
          </ul>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-3">
            <ShieldCheckIcon className="w-6 h-6 text-blue-600" />
            <h4 className="font-medium text-blue-900">Guarantee</h4>
          </div>
          <ul className="space-y-2 text-blue-800 text-sm">
            <li>• 100% authentic licenses</li>
            <li>• Money-back guarantee</li>
            <li>• Secure payment processing</li>
            <li>• 24/7 customer support</li>
            <li>• Lifetime license validity</li>
          </ul>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-medium text-gray-900 mb-3">Need Help?</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h5 className="font-medium text-gray-900 mb-1">Email Support</h5>
              <p className="text-sm text-gray-600"><EMAIL></p>
              <p className="text-xs text-gray-500 mt-1">Response within 2 hours</p>
            </div>
          </div>
          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h5 className="font-medium text-gray-900 mb-1">Live Chat</h5>
              <p className="text-sm text-gray-600">Available 24/7</p>
              <p className="text-xs text-gray-500 mt-1">Instant assistance</p>
            </div>
          </div>
          <div className="text-center">
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h5 className="font-medium text-gray-900 mb-1">Phone Support</h5>
              <p className="text-sm text-gray-600">+****************</p>
              <p className="text-xs text-gray-500 mt-1">Mon-Fri 9AM-6PM EST</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      {/* Digital Product Header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <CloudDownloadIcon className="w-8 h-8 text-white" />
              <span className="bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm font-semibold">
                Digital Product
              </span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">{product.name}</h2>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  i < Math.floor(product.rating) ? (
                    <StarIconSolid key={i} className="w-5 h-5 text-yellow-300" />
                  ) : (
                    <StarIcon key={i} className="w-5 h-5 text-white text-opacity-50" />
                  )
                ))}
              </div>
              <span className="text-white text-opacity-90">
                {product.rating} ({product.reviews} reviews)
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-white mb-1">
              ${product.price}
            </div>
            {product.originalPrice && product.originalPrice > product.price && (
              <div className="text-lg text-white text-opacity-70 line-through">
                ${product.originalPrice}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Key Features */}
      <div className="px-6 py-6 bg-gray-50 border-b">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <ClockIcon className="w-6 h-6 text-green-600" />
            <div>
              <p className="font-medium text-gray-900">Instant Delivery</p>
              <p className="text-sm text-gray-600">Email within 5 minutes</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <ShieldCheckIcon className="w-6 h-6 text-blue-600" />
            <div>
              <p className="font-medium text-gray-900">{product.licenseType || 'Genuine License'}</p>
              <p className="text-sm text-gray-600">{product.validityPeriod || 'Lifetime validity'}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircleIcon className="w-6 h-6 text-purple-600" />
            <div>
              <p className="font-medium text-gray-900">24/7 Support</p>
              <p className="text-sm text-gray-600">Installation help included</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="px-6 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Product Description</h3>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                {product.description}
              </p>
            </div>

            {product.features && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Key Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircleIcon className="w-5 h-5 text-green-600 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'requirements' && renderSystemRequirements()}
        {activeTab === 'installation' && renderInstallationGuide()}
        {activeTab === 'support' && renderSupport()}
      </div>
    </div>
  );
};

export default DigitalProductDetail;
