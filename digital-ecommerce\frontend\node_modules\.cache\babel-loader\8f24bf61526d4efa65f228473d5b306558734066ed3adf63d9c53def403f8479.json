{"ast": null, "code": "import React,{useState,useMemo}from'react';import{motion,AnimatePresence}from'framer-motion';import{useSearchParams,Link}from'react-router-dom';import{FunnelIcon,Squares2X2Icon,ListBulletIcon,StarIcon,HeartIcon,ShoppingBagIcon,AdjustmentsHorizontalIcon,ChevronRightIcon,HomeIcon,TagIcon,ClockIcon,TruckIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid}from'@heroicons/react/24/solid';import{categories,products,getProductsByCategory}from'../data/products';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProductsPage=()=>{var _subcategories$find;const[searchParams,setSearchParams]=useSearchParams();const[viewMode,setViewMode]=useState('grid');const[sortBy,setSortBy]=useState('featured');const[selectedCategory,setSelectedCategory]=useState(searchParams.get('category')||'all');const[selectedSubcategory,setSelectedSubcategory]=useState(searchParams.get('subcategory')||'all');const[productType,setProductType]=useState('all');// all, physical, digital\nconst[priceRange,setPriceRange]=useState([0,1000]);const[selectedRating,setSelectedRating]=useState(0);const[showFilters,setShowFilters]=useState(false);// Get current category data\nconst currentCategory=categories.find(cat=>cat.id===selectedCategory);const subcategories=currentCategory?[{id:'all',name:'All '+currentCategory.name,count:getProductsByCategory(selectedCategory).length},...currentCategory.subcategories.map(sub=>({id:sub,name:sub.split('-').map(word=>word.charAt(0).toUpperCase()+word.slice(1)).join(' '),count:products.filter(p=>p.subcategory===sub).length}))]:[];const productTypeOptions=[{id:'all',name:'All Products',count:products.length},{id:'physical',name:'Physical Products',count:products.filter(p=>p.type==='physical').length},{id:'digital',name:'Digital Products',count:products.filter(p=>p.type==='digital').length}];const sortOptions=[{value:'featured',label:'Featured'},{value:'price-low',label:'Price: Low to High'},{value:'price-high',label:'Price: High to Low'},{value:'rating',label:'Highest Rated'},{value:'newest',label:'Newest First'},{value:'name',label:'Name: A to Z'},{value:'popularity',label:'Most Popular'}];const filteredAndSortedProducts=useMemo(()=>{let filtered=products.filter(product=>{const categoryMatch=selectedCategory==='all'||product.category===selectedCategory;const subcategoryMatch=selectedSubcategory==='all'||product.subcategory===selectedSubcategory;const typeMatch=productType==='all'||product.type===productType;const priceMatch=product.price>=priceRange[0]&&product.price<=priceRange[1];const ratingMatch=selectedRating===0||product.rating>=selectedRating;return categoryMatch&&subcategoryMatch&&typeMatch&&priceMatch&&ratingMatch;});// Sort products\nswitch(sortBy){case'price-low':filtered.sort((a,b)=>a.price-b.price);break;case'price-high':filtered.sort((a,b)=>b.price-a.price);break;case'rating':filtered.sort((a,b)=>b.rating-a.rating);break;case'newest':filtered.sort((a,b)=>b.id.localeCompare(a.id));break;case'name':filtered.sort((a,b)=>a.name.localeCompare(b.name));break;default:// Featured - keep original order\nbreak;}return filtered;},[selectedCategory,selectedSubcategory,productType,priceRange,selectedRating,sortBy]);const ProductCard=_ref=>{var _product$shipping;let{product,index}=_ref;return/*#__PURE__*/_jsxs(motion.div,{layout:true,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3,delay:index*0.05},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 \".concat(viewMode==='list'?'flex':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative \".concat(viewMode==='list'?'w-48 flex-shrink-0':''),children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:product.image,alt:product.name,className:\"w-full object-cover group-hover:scale-105 transition-transform duration-300 \".concat(viewMode==='list'?'h-48':'h-64')}),product.badge&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-1 rounded-full text-sm font-semibold text-white \".concat(product.type==='digital'?'bg-blue-500':'bg-light-orange-500'),children:product.badge})}),product.type==='digital'&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4 mt-8\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",children:\"Digital\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},className:\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",children:/*#__PURE__*/_jsx(HeartIcon,{className:\"w-5 h-5 text-gray-600\"})})}),!product.inStock&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-semibold\",children:\"Out of Stock\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 \".concat(viewMode==='list'?'flex-1 flex flex-col justify-between':''),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2 \".concat(viewMode==='list'?'text-xl':'text-lg'),children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\")\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),product.type==='digital'?/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-green-600 font-medium\",children:\"Instant Delivery\"})]}),product.platforms&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Platforms:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-800\",children:product.platforms.join(', ')})]})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[product.colors&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Colors:\"}),product.colors.slice(0,3).map((color,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer \".concat(color==='black'||color==='Black'?'bg-black':color==='white'||color==='White'?'bg-white':color==='blue'||color==='Blue'?'bg-blue-500':color==='red'||color==='Red'?'bg-red-500':color==='silver'||color==='Silver'?'bg-gray-400':color==='gold'||color==='Gold'?'bg-yellow-400':'bg-gray-300')},index)),product.colors.length>3&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[\"+\",product.colors.length-3]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(TruckIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-blue-600\",children:(_product$shipping=product.shipping)!==null&&_product$shipping!==void 0&&_product$shipping.free?'Free Shipping':'Shipping Available'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-4 h-4 \".concat(product.inStock?'text-green-600':'text-red-600')}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm \".concat(product.inStock?'text-green-600':'text-red-600'),children:[product.inStock?'In Stock':'Out of Stock',product.stockCount&&product.inStock&&\" (\".concat(product.stockCount,\" available)\")]})]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},className:\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Add to Cart\"})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",children:/*#__PURE__*/_jsxs(\"nav\",{className:\"flex items-center space-x-2 text-sm\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\",children:[/*#__PURE__*/_jsx(HomeIcon,{className:\"w-4 h-4 mr-1\"}),\"Home\"]}),/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Products\"}),selectedCategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:currentCategory===null||currentCategory===void 0?void 0:currentCategory.name})]}),selectedSubcategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:(_subcategories$find=subcategories.find(sub=>sub.id===selectedSubcategory))===null||_subcategories$find===void 0?void 0:_subcategories$find.name})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl lg:text-5xl font-bold text-white mb-4\",children:selectedCategory==='all'?'All Products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.name)||'Products'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-light-orange-100 max-w-2xl mx-auto\",children:selectedCategory==='all'?'Discover our amazing collection of premium products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.description)||'Explore our curated selection'})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-4 justify-center\",children:[{id:'all',name:'All Products',icon:'🛍️'},...categories].map(category=>/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>{setSelectedCategory(category.id);setSelectedSubcategory('all');setSearchParams({category:category.id});},className:\"flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all \".concat(selectedCategory===category.id?'bg-light-orange-500 text-white shadow-lg':'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:category.icon}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]},category.id))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:w-64 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Filters\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowFilters(!showFilters),className:\"lg:hidden p-2 text-gray-600\",children:/*#__PURE__*/_jsx(AdjustmentsHorizontalIcon,{className:\"w-5 h-5\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6 \".concat(showFilters?'block':'hidden lg:block'),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Product Type\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:productTypeOptions.map(type=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setProductType(type.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(productType===type.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:type.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",type.count,\")\"]})]})},type.id))})]}),selectedCategory!=='all'&&subcategories.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[currentCategory===null||currentCategory===void 0?void 0:currentCategory.name,\" Categories\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:subcategories.map(subcategory=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedSubcategory(subcategory.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(selectedSubcategory===subcategory.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:subcategory.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",subcategory.count,\")\"]})]})},subcategory.id))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[\"Price Range: $\",priceRange[0],\" - $\",priceRange[1]]}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:\"1000\",value:priceRange[1],onChange:e=>setPriceRange([priceRange[0],parseInt(e.target.value)]),className:\"w-full\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Minimum Rating\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:[4,3,2,1,0].map(rating=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setSelectedRating(rating),className:\"flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors \".concat(selectedRating===rating?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 \".concat(i<rating?'text-yellow-400':'text-gray-300')},i))}),/*#__PURE__*/_jsx(\"span\",{children:rating>0?\"\".concat(rating,\"+ Stars\"):'All Ratings'})]},rating))})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Showing \",filteredAndSortedProducts.length,\" of \",products.length,\" products\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"select\",{value:sortBy,onChange:e=>setSortBy(e.target.value),className:\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\",children:sortOptions.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('grid'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='grid'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(Squares2X2Icon,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('list'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='list'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(ListBulletIcon,{className:\"w-5 h-5\"})})]})]})]})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"\".concat(viewMode==='grid'?'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8':'space-y-6'),children:filteredAndSortedProducts.map((product,index)=>/*#__PURE__*/_jsx(ProductCard,{product:product,index:index},product.id))},\"\".concat(viewMode,\"-\").concat(selectedCategory,\"-\").concat(sortBy))}),filteredAndSortedProducts.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-4\",children:/*#__PURE__*/_jsx(FunnelIcon,{className:\"w-16 h-16 mx-auto\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:\"No products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Try adjusting your filters to see more results.\"})]})]})]})})]});};export default ProductsPage;", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "useSearchParams", "Link", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "StarIcon", "HeartIcon", "ShoppingBagIcon", "AdjustmentsHorizontalIcon", "ChevronRightIcon", "HomeIcon", "TagIcon", "ClockIcon", "TruckIcon", "CheckCircleIcon", "StarIconSolid", "categories", "products", "getProductsByCategory", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProductsPage", "_subcategories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "sortBy", "setSortBy", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "get", "selectedSubcategory", "setSelectedSubcategory", "productType", "setProductType", "priceRange", "setPriceRange", "selectedRating", "setSelectedRating", "showFilters", "setShowFilters", "currentCategory", "find", "cat", "id", "subcategories", "name", "count", "length", "map", "sub", "split", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "filter", "p", "subcategory", "productTypeOptions", "type", "sortOptions", "value", "label", "filteredAndSortedProducts", "filtered", "product", "categoryMatch", "category", "subcategoryMatch", "typeMatch", "priceMatch", "price", "ratingMatch", "rating", "sort", "a", "b", "localeCompare", "ProductCard", "_ref", "_product$shipping", "index", "div", "layout", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "delay", "className", "concat", "children", "src", "images", "image", "alt", "badge", "button", "whileHover", "scale", "whileTap", "inStock", "Array", "_", "i", "Math", "floor", "reviews", "originalPrice", "platforms", "colors", "color", "shipping", "free", "stockCount", "to", "description", "icon", "onClick", "min", "max", "onChange", "e", "parseInt", "target", "option", "mode"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport {\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon,\n  StarIcon,\n  HeartIcon,\n  ShoppingBagIcon,\n  AdjustmentsHorizontalIcon,\n  ChevronRightIcon,\n  HomeIcon,\n  TagIcon,\n  ClockIcon,\n  TruckIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { categories, products, getProductsByCategory } from '../data/products';\n\nconst ProductsPage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortBy, setSortBy] = useState('featured');\n  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');\n  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');\n  const [productType, setProductType] = useState('all'); // all, physical, digital\n  const [priceRange, setPriceRange] = useState([0, 1000]);\n  const [selectedRating, setSelectedRating] = useState(0);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Get current category data\n  const currentCategory = categories.find(cat => cat.id === selectedCategory);\n  const subcategories = currentCategory ? [\n    { id: 'all', name: 'All ' + currentCategory.name, count: getProductsByCategory(selectedCategory).length },\n    ...currentCategory.subcategories.map(sub => ({\n      id: sub,\n      name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),\n      count: products.filter(p => p.subcategory === sub).length\n    }))\n  ] : [];\n\n  const productTypeOptions = [\n    { id: 'all', name: 'All Products', count: products.length },\n    { id: 'physical', name: 'Physical Products', count: products.filter(p => p.type === 'physical').length },\n    { id: 'digital', name: 'Digital Products', count: products.filter(p => p.type === 'digital').length }\n  ];\n\n  const sortOptions = [\n    { value: 'featured', label: 'Featured' },\n    { value: 'price-low', label: 'Price: Low to High' },\n    { value: 'price-high', label: 'Price: High to Low' },\n    { value: 'rating', label: 'Highest Rated' },\n    { value: 'newest', label: 'Newest First' },\n    { value: 'name', label: 'Name: A to Z' },\n    { value: 'popularity', label: 'Most Popular' }\n  ];\n\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;\n      const typeMatch = productType === 'all' || product.type === productType;\n      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];\n      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;\n\n      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'newest':\n        filtered.sort((a, b) => b.id.localeCompare(a.id));\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n\n    return filtered;\n  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);\n\n  const ProductCard = ({ product, index }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3, delay: index * 0.05 }}\n      className={`bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 ${\n        viewMode === 'list' ? 'flex' : ''\n      }`}\n    >\n      <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n        <img\n          src={product.images ? product.images[0] : product.image}\n          alt={product.name}\n          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${\n            viewMode === 'list' ? 'h-48' : 'h-64'\n          }`}\n        />\n        {product.badge && (\n          <div className=\"absolute top-4 left-4\">\n            <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${\n              product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'\n            }`}>\n              {product.badge}\n            </span>\n          </div>\n        )}\n        {product.type === 'digital' && (\n          <div className=\"absolute top-4 left-4 mt-8\">\n            <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n              Digital\n            </span>\n          </div>\n        )}\n        <div className=\"absolute top-4 right-4\">\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n          >\n            <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n          </motion.button>\n        </div>\n        {!product.inStock && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-semibold\">Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>\n        <div>\n          <h3 className={`font-semibold text-gray-900 mb-2 ${\n            viewMode === 'list' ? 'text-xl' : 'text-lg'\n          }`}>\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex\">\n              {[...Array(5)].map((_, i) => (\n                i < Math.floor(product.rating) ? (\n                  <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                ) : (\n                  <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                )\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-600 ml-2\">\n              {product.rating} ({product.reviews})\n            </span>\n          </div>\n\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <span className=\"text-2xl font-bold text-light-orange-600\">\n              ${product.price}\n            </span>\n            {product.originalPrice && product.originalPrice > product.price && (\n              <span className=\"text-lg text-gray-500 line-through\">\n                ${product.originalPrice}\n              </span>\n            )}\n          </div>\n\n          {/* Product Type Specific Info */}\n          {product.type === 'digital' ? (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <ClockIcon className=\"w-4 h-4 text-green-600\" />\n                <span className=\"text-sm text-green-600 font-medium\">Instant Delivery</span>\n              </div>\n              {product.platforms && (\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-600\">Platforms:</span>\n                  <span className=\"text-sm text-gray-800\">{product.platforms.join(', ')}</span>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Color Options for Physical Products */}\n              {product.colors && (\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <span className=\"text-sm text-gray-600\">Colors:</span>\n                  {product.colors.slice(0, 3).map((color, index) => (\n                    <div\n                      key={index}\n                      className={`w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${\n                        color === 'black' || color === 'Black' ? 'bg-black' :\n                        color === 'white' || color === 'White' ? 'bg-white' :\n                        color === 'blue' || color === 'Blue' ? 'bg-blue-500' :\n                        color === 'red' || color === 'Red' ? 'bg-red-500' :\n                        color === 'silver' || color === 'Silver' ? 'bg-gray-400' :\n                        color === 'gold' || color === 'Gold' ? 'bg-yellow-400' :\n                        'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                  {product.colors.length > 3 && (\n                    <span className=\"text-sm text-gray-500\">+{product.colors.length - 3}</span>\n                  )}\n                </div>\n              )}\n              {/* Shipping Info */}\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TruckIcon className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm text-blue-600\">\n                  {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}\n                </span>\n              </div>\n            </>\n          )}\n\n          {/* Stock Status */}\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <CheckCircleIcon className={`w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`} />\n            <span className={`text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>\n              {product.inStock ? 'In Stock' : 'Out of Stock'}\n              {product.stockCount && product.inStock && ` (${product.stockCount} available)`}\n            </span>\n          </div>\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n        >\n          <ShoppingBagIcon className=\"w-5 h-5\" />\n          <span>Add to Cart</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Breadcrumb */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex items-center space-x-2 text-sm\">\n            <Link to=\"/\" className=\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\">\n              <HomeIcon className=\"w-4 h-4 mr-1\" />\n              Home\n            </Link>\n            <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n            <span className=\"text-gray-600\">Products</span>\n            {selectedCategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {currentCategory?.name}\n                </span>\n              </>\n            )}\n            {selectedSubcategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {subcategories.find(sub => sub.id === selectedSubcategory)?.name}\n                </span>\n              </>\n            )}\n          </nav>\n        </div>\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\n              {selectedCategory === 'all' ? 'All Products' : currentCategory?.name || 'Products'}\n            </h1>\n            <p className=\"text-xl text-light-orange-100 max-w-2xl mx-auto\">\n              {selectedCategory === 'all'\n                ? 'Discover our amazing collection of premium products'\n                : currentCategory?.description || 'Explore our curated selection'\n              }\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {[{ id: 'all', name: 'All Products', icon: '🛍️' }, ...categories].map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => {\n                  setSelectedCategory(category.id);\n                  setSelectedSubcategory('all');\n                  setSearchParams({ category: category.id });\n                }}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-light-orange-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar Filters */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n                <button\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"lg:hidden p-2 text-gray-600\"\n                >\n                  <AdjustmentsHorizontalIcon className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                {/* Product Type */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Product Type</h4>\n                  <div className=\"space-y-2\">\n                    {productTypeOptions.map(type => (\n                      <button\n                        key={type.id}\n                        onClick={() => setProductType(type.id)}\n                        className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                          productType === type.id\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex justify-between\">\n                          <span>{type.name}</span>\n                          <span className=\"text-sm\">({type.count})</span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Subcategories */}\n                {selectedCategory !== 'all' && subcategories.length > 0 && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">\n                      {currentCategory?.name} Categories\n                    </h4>\n                    <div className=\"space-y-2\">\n                      {subcategories.map(subcategory => (\n                        <button\n                          key={subcategory.id}\n                          onClick={() => setSelectedSubcategory(subcategory.id)}\n                          className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                            selectedSubcategory === subcategory.id\n                              ? 'bg-light-orange-100 text-light-orange-700'\n                              : 'text-gray-600 hover:bg-gray-100'\n                          }`}\n                        >\n                          <div className=\"flex justify-between\">\n                            <span>{subcategory.name}</span>\n                            <span className=\"text-sm\">({subcategory.count})</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Price Range */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">\n                    Price Range: ${priceRange[0]} - ${priceRange[1]}\n                  </h4>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1000\"\n                    value={priceRange[1]}\n                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}\n                    className=\"w-full\"\n                  />\n                </div>\n\n                {/* Rating Filter */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Minimum Rating</h4>\n                  <div className=\"space-y-2\">\n                    {[4, 3, 2, 1, 0].map(rating => (\n                      <button\n                        key={rating}\n                        onClick={() => setSelectedRating(rating)}\n                        className={`flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${\n                          selectedRating === rating\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex\">\n                          {[...Array(5)].map((_, i) => (\n                            <StarIconSolid\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < rating ? 'text-yellow-400' : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                        <span>{rating > 0 ? `${rating}+ Stars` : 'All Ratings'}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {/* Toolbar */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8\">\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n                <div>\n                  <p className=\"text-gray-600\">\n                    Showing {filteredAndSortedProducts.length} of {products.length} products\n                  </p>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  {/* Sort Dropdown */}\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\"\n                  >\n                    {sortOptions.map(option => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n\n                  {/* View Mode Toggle */}\n                  <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                    <button\n                      onClick={() => setViewMode('grid')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'grid'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <Squares2X2Icon className=\"w-5 h-5\" />\n                    </button>\n                    <button\n                      onClick={() => setViewMode('list')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'list'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <ListBulletIcon className=\"w-5 h-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Products Grid */}\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={`${viewMode}-${selectedCategory}-${sortBy}`}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className={`${\n                  viewMode === 'grid'\n                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8'\n                    : 'space-y-6'\n                }`}\n              >\n                {filteredAndSortedProducts.map((product, index) => (\n                  <ProductCard key={product.id} product={product} index={index} />\n                ))}\n              </motion.div>\n            </AnimatePresence>\n\n            {filteredAndSortedProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <FunnelIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,OAAO,KAAQ,OAAO,CAChD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,eAAe,CAAEC,IAAI,KAAQ,kBAAkB,CACxD,OACEC,UAAU,CACVC,cAAc,CACdC,cAAc,CACdC,QAAQ,CACRC,SAAS,CACTC,eAAe,CACfC,yBAAyB,CACzBC,gBAAgB,CAChBC,QAAQ,CACRC,OAAO,CACPC,SAAS,CACTC,SAAS,CACTC,eAAe,KACV,6BAA6B,CACpC,OAAST,QAAQ,GAAI,CAAAU,aAAa,KAAQ,2BAA2B,CACrE,OAASC,UAAU,CAAEC,QAAQ,CAAEC,qBAAqB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/E,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CACzB,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5B,eAAe,CAAC,CAAC,CACzD,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAGlC,QAAQ,CAAC,MAAM,CAAC,CAChD,KAAM,CAACmC,MAAM,CAAEC,SAAS,CAAC,CAAGpC,QAAQ,CAAC,UAAU,CAAC,CAChD,KAAM,CAACqC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtC,QAAQ,CAAC+B,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAI,KAAK,CAAC,CAC/F,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzC,QAAQ,CAAC+B,YAAY,CAACQ,GAAG,CAAC,aAAa,CAAC,EAAI,KAAK,CAAC,CACxG,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAAE;AACvD,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAC,CACvD,KAAM,CAAC8C,cAAc,CAAEC,iBAAiB,CAAC,CAAG/C,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACgD,WAAW,CAAEC,cAAc,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAErD;AACA,KAAM,CAAAkD,eAAe,CAAG9B,UAAU,CAAC+B,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACC,EAAE,GAAKhB,gBAAgB,CAAC,CAC3E,KAAM,CAAAiB,aAAa,CAAGJ,eAAe,CAAG,CACtC,CAAEG,EAAE,CAAE,KAAK,CAAEE,IAAI,CAAE,MAAM,CAAGL,eAAe,CAACK,IAAI,CAAEC,KAAK,CAAElC,qBAAqB,CAACe,gBAAgB,CAAC,CAACoB,MAAO,CAAC,CACzG,GAAGP,eAAe,CAACI,aAAa,CAACI,GAAG,CAACC,GAAG,GAAK,CAC3CN,EAAE,CAAEM,GAAG,CACPJ,IAAI,CAAEI,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,GAAG,CAACG,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACxFT,KAAK,CAAEnC,QAAQ,CAAC6C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,WAAW,GAAKT,GAAG,CAAC,CAACF,MACrD,CAAC,CAAC,CAAC,CACJ,CAAG,EAAE,CAEN,KAAM,CAAAY,kBAAkB,CAAG,CACzB,CAAEhB,EAAE,CAAE,KAAK,CAAEE,IAAI,CAAE,cAAc,CAAEC,KAAK,CAAEnC,QAAQ,CAACoC,MAAO,CAAC,CAC3D,CAAEJ,EAAE,CAAE,UAAU,CAAEE,IAAI,CAAE,mBAAmB,CAAEC,KAAK,CAAEnC,QAAQ,CAAC6C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,UAAU,CAAC,CAACb,MAAO,CAAC,CACxG,CAAEJ,EAAE,CAAE,SAAS,CAAEE,IAAI,CAAE,kBAAkB,CAAEC,KAAK,CAAEnC,QAAQ,CAAC6C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,SAAS,CAAC,CAACb,MAAO,CAAC,CACtG,CAED,KAAM,CAAAc,WAAW,CAAG,CAClB,CAAEC,KAAK,CAAE,UAAU,CAAEC,KAAK,CAAE,UAAW,CAAC,CACxC,CAAED,KAAK,CAAE,WAAW,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACnD,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACpD,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC3C,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC1C,CAAED,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,cAAe,CAAC,CACxC,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC/C,CAED,KAAM,CAAAC,yBAAyB,CAAGzE,OAAO,CAAC,IAAM,CAC9C,GAAI,CAAA0E,QAAQ,CAAGtD,QAAQ,CAAC6C,MAAM,CAACU,OAAO,EAAI,CACxC,KAAM,CAAAC,aAAa,CAAGxC,gBAAgB,GAAK,KAAK,EAAIuC,OAAO,CAACE,QAAQ,GAAKzC,gBAAgB,CACzF,KAAM,CAAA0C,gBAAgB,CAAGvC,mBAAmB,GAAK,KAAK,EAAIoC,OAAO,CAACR,WAAW,GAAK5B,mBAAmB,CACrG,KAAM,CAAAwC,SAAS,CAAGtC,WAAW,GAAK,KAAK,EAAIkC,OAAO,CAACN,IAAI,GAAK5B,WAAW,CACvE,KAAM,CAAAuC,UAAU,CAAGL,OAAO,CAACM,KAAK,EAAItC,UAAU,CAAC,CAAC,CAAC,EAAIgC,OAAO,CAACM,KAAK,EAAItC,UAAU,CAAC,CAAC,CAAC,CACnF,KAAM,CAAAuC,WAAW,CAAGrC,cAAc,GAAK,CAAC,EAAI8B,OAAO,CAACQ,MAAM,EAAItC,cAAc,CAE5E,MAAO,CAAA+B,aAAa,EAAIE,gBAAgB,EAAIC,SAAS,EAAIC,UAAU,EAAIE,WAAW,CACpF,CAAC,CAAC,CAEF;AACA,OAAQhD,MAAM,EACZ,IAAK,WAAW,CACdwC,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACJ,KAAK,CAAGK,CAAC,CAACL,KAAK,CAAC,CAC1C,MACF,IAAK,YAAY,CACfP,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAC1C,MACF,IAAK,QAAQ,CACXP,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACH,MAAM,CAAGE,CAAC,CAACF,MAAM,CAAC,CAC5C,MACF,IAAK,QAAQ,CACXT,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAClC,EAAE,CAACmC,aAAa,CAACF,CAAC,CAACjC,EAAE,CAAC,CAAC,CACjD,MACF,IAAK,MAAM,CACTsB,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAC/B,IAAI,CAACiC,aAAa,CAACD,CAAC,CAAChC,IAAI,CAAC,CAAC,CACrD,MACF,QACE;AACA,MACJ,CAEA,MAAO,CAAAoB,QAAQ,CACjB,CAAC,CAAE,CAACtC,gBAAgB,CAAEG,mBAAmB,CAAEE,WAAW,CAAEE,UAAU,CAAEE,cAAc,CAAEX,MAAM,CAAC,CAAC,CAE5F,KAAM,CAAAsD,WAAW,CAAGC,IAAA,OAAAC,iBAAA,IAAC,CAAEf,OAAO,CAAEgB,KAAM,CAAC,CAAAF,IAAA,oBACrChE,KAAA,CAACxB,MAAM,CAAC2F,GAAG,EACTC,MAAM,MACNC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAEV,KAAK,CAAG,IAAK,CAAE,CACnDW,SAAS,oHAAAC,MAAA,CACPvE,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,EAAE,CAChC,CAAAwE,QAAA,eAEH/E,KAAA,QAAK6E,SAAS,aAAAC,MAAA,CAAcvE,QAAQ,GAAK,MAAM,CAAG,oBAAoB,CAAG,EAAE,CAAG,CAAAwE,QAAA,eAC5EjF,IAAA,QACEkF,GAAG,CAAE9B,OAAO,CAAC+B,MAAM,CAAG/B,OAAO,CAAC+B,MAAM,CAAC,CAAC,CAAC,CAAG/B,OAAO,CAACgC,KAAM,CACxDC,GAAG,CAAEjC,OAAO,CAACrB,IAAK,CAClBgD,SAAS,gFAAAC,MAAA,CACPvE,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CACpC,CACJ,CAAC,CACD2C,OAAO,CAACkC,KAAK,eACZtF,IAAA,QAAK+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,cACpCjF,IAAA,SAAM+E,SAAS,4DAAAC,MAAA,CACb5B,OAAO,CAACN,IAAI,GAAK,SAAS,CAAG,aAAa,CAAG,qBAAqB,CACjE,CAAAmC,QAAA,CACA7B,OAAO,CAACkC,KAAK,CACV,CAAC,CACJ,CACN,CACAlC,OAAO,CAACN,IAAI,GAAK,SAAS,eACzB9C,IAAA,QAAK+E,SAAS,CAAC,4BAA4B,CAAAE,QAAA,cACzCjF,IAAA,SAAM+E,SAAS,CAAC,iEAAiE,CAAAE,QAAA,CAAC,SAElF,CAAM,CAAC,CACJ,CACN,cACDjF,IAAA,QAAK+E,SAAS,CAAC,wBAAwB,CAAAE,QAAA,cACrCjF,IAAA,CAACtB,MAAM,CAAC6G,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBV,SAAS,CAAC,mDAAmD,CAAAE,QAAA,cAE7DjF,IAAA,CAACd,SAAS,EAAC6F,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAClC,CAAC,CACb,CAAC,CACL,CAAC3B,OAAO,CAACuC,OAAO,eACf3F,IAAA,QAAK+E,SAAS,CAAC,0EAA0E,CAAAE,QAAA,cACvFjF,IAAA,SAAM+E,SAAS,CAAC,0BAA0B,CAAAE,QAAA,CAAC,cAAY,CAAM,CAAC,CAC3D,CACN,EACE,CAAC,cAEN/E,KAAA,QAAK6E,SAAS,QAAAC,MAAA,CAASvE,QAAQ,GAAK,MAAM,CAAG,sCAAsC,CAAG,EAAE,CAAG,CAAAwE,QAAA,eACzF/E,KAAA,QAAA+E,QAAA,eACEjF,IAAA,OAAI+E,SAAS,qCAAAC,MAAA,CACXvE,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CAC1C,CAAAwE,QAAA,CACA7B,OAAO,CAACrB,IAAI,CACX,CAAC,cAEL7B,KAAA,QAAK6E,SAAS,CAAC,wBAAwB,CAAAE,QAAA,eACrCjF,IAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAE,QAAA,CAClB,CAAC,GAAGW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC1D,GAAG,CAAC,CAAC2D,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAAC5C,OAAO,CAACQ,MAAM,CAAC,cAC5B5D,IAAA,CAACL,aAAa,EAASoF,SAAS,CAAC,yBAAyB,EAAtCe,CAAwC,CAAC,cAE7D9F,IAAA,CAACf,QAAQ,EAAS8F,SAAS,CAAC,uBAAuB,EAApCe,CAAsC,CAExD,CAAC,CACC,CAAC,cACN5F,KAAA,SAAM6E,SAAS,CAAC,4BAA4B,CAAAE,QAAA,EACzC7B,OAAO,CAACQ,MAAM,CAAC,IAAE,CAACR,OAAO,CAAC6C,OAAO,CAAC,GACrC,EAAM,CAAC,EACJ,CAAC,cAEN/F,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAE,QAAA,eAC/C/E,KAAA,SAAM6E,SAAS,CAAC,0CAA0C,CAAAE,QAAA,EAAC,GACxD,CAAC7B,OAAO,CAACM,KAAK,EACX,CAAC,CACNN,OAAO,CAAC8C,aAAa,EAAI9C,OAAO,CAAC8C,aAAa,CAAG9C,OAAO,CAACM,KAAK,eAC7DxD,KAAA,SAAM6E,SAAS,CAAC,oCAAoC,CAAAE,QAAA,EAAC,GAClD,CAAC7B,OAAO,CAAC8C,aAAa,EACnB,CACP,EACE,CAAC,CAGL9C,OAAO,CAACN,IAAI,GAAK,SAAS,cACzB5C,KAAA,QAAK6E,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnB/E,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAE,QAAA,eAC/CjF,IAAA,CAACR,SAAS,EAACuF,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAChD/E,IAAA,SAAM+E,SAAS,CAAC,oCAAoC,CAAAE,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACzE,CAAC,CACL7B,OAAO,CAAC+C,SAAS,eAChBjG,KAAA,QAAK6E,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAC1CjF,IAAA,SAAM+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDjF,IAAA,SAAM+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,CAAE7B,OAAO,CAAC+C,SAAS,CAAC1D,IAAI,CAAC,IAAI,CAAC,CAAO,CAAC,EAC1E,CACN,EACE,CAAC,cAENvC,KAAA,CAAAE,SAAA,EAAA6E,QAAA,EAEG7B,OAAO,CAACgD,MAAM,eACblG,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAE,QAAA,eAC/CjF,IAAA,SAAM+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,CAAC,SAAO,CAAM,CAAC,CACrD7B,OAAO,CAACgD,MAAM,CAAC5D,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACN,GAAG,CAAC,CAACmE,KAAK,CAAEjC,KAAK,gBAC3CpE,IAAA,QAEE+E,SAAS,iEAAAC,MAAA,CACPqB,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,aAAa,CACpDA,KAAK,GAAK,KAAK,EAAIA,KAAK,GAAK,KAAK,CAAG,YAAY,CACjDA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,QAAQ,CAAG,aAAa,CACxDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,eAAe,CACtD,aAAa,CACZ,EATEjC,KAUN,CACF,CAAC,CACDhB,OAAO,CAACgD,MAAM,CAACnE,MAAM,CAAG,CAAC,eACxB/B,KAAA,SAAM6E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,EAAC,GAAC,CAAC7B,OAAO,CAACgD,MAAM,CAACnE,MAAM,CAAG,CAAC,EAAO,CAC3E,EACE,CACN,cAED/B,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAE,QAAA,eAC/CjF,IAAA,CAACP,SAAS,EAACsF,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/C/E,IAAA,SAAM+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,CACpC,CAAAd,iBAAA,CAAAf,OAAO,CAACkD,QAAQ,UAAAnC,iBAAA,WAAhBA,iBAAA,CAAkBoC,IAAI,CAAG,eAAe,CAAG,oBAAoB,CAC5D,CAAC,EACJ,CAAC,EACN,CACH,cAGDrG,KAAA,QAAK6E,SAAS,CAAC,kCAAkC,CAAAE,QAAA,eAC/CjF,IAAA,CAACN,eAAe,EAACqF,SAAS,YAAAC,MAAA,CAAa5B,OAAO,CAACuC,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAE,CAAC,cAChGzF,KAAA,SAAM6E,SAAS,YAAAC,MAAA,CAAa5B,OAAO,CAACuC,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAAV,QAAA,EAC/E7B,OAAO,CAACuC,OAAO,CAAG,UAAU,CAAG,cAAc,CAC7CvC,OAAO,CAACoD,UAAU,EAAIpD,OAAO,CAACuC,OAAO,OAAAX,MAAA,CAAS5B,OAAO,CAACoD,UAAU,eAAa,EAC1E,CAAC,EACJ,CAAC,EACH,CAAC,cAENtG,KAAA,CAACxB,MAAM,CAAC6G,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BV,SAAS,CAAC,yOAAyO,CAAAE,QAAA,eAEnPjF,IAAA,CAACb,eAAe,EAAC4F,SAAS,CAAC,SAAS,CAAE,CAAC,cACvC/E,IAAA,SAAAiF,QAAA,CAAM,aAAW,CAAM,CAAC,EACX,CAAC,EACb,CAAC,EACI,CAAC,EACd,CAED,mBACE/E,KAAA,QAAK6E,SAAS,CAAC,yBAAyB,CAAAE,QAAA,eAEtCjF,IAAA,QAAK+E,SAAS,CAAC,mBAAmB,CAAAE,QAAA,cAChCjF,IAAA,QAAK+E,SAAS,CAAC,6CAA6C,CAAAE,QAAA,cAC1D/E,KAAA,QAAK6E,SAAS,CAAC,qCAAqC,CAAAE,QAAA,eAClD/E,KAAA,CAACrB,IAAI,EAAC4H,EAAE,CAAC,GAAG,CAAC1B,SAAS,CAAC,+EAA+E,CAAAE,QAAA,eACpGjF,IAAA,CAACV,QAAQ,EAACyF,SAAS,CAAC,cAAc,CAAE,CAAC,OAEvC,EAAM,CAAC,cACP/E,IAAA,CAACX,gBAAgB,EAAC0F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD/E,IAAA,SAAM+E,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,UAAQ,CAAM,CAAC,CAC9CpE,gBAAgB,GAAK,KAAK,eACzBX,KAAA,CAAAE,SAAA,EAAA6E,QAAA,eACEjF,IAAA,CAACX,gBAAgB,EAAC0F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD/E,IAAA,SAAM+E,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAChDvD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEK,IAAI,CAClB,CAAC,EACP,CACH,CACAf,mBAAmB,GAAK,KAAK,eAC5Bd,KAAA,CAAAE,SAAA,EAAA6E,QAAA,eACEjF,IAAA,CAACX,gBAAgB,EAAC0F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtD/E,IAAA,SAAM+E,SAAS,CAAC,mCAAmC,CAAAE,QAAA,EAAA3E,mBAAA,CAChDwB,aAAa,CAACH,IAAI,CAACQ,GAAG,EAAIA,GAAG,CAACN,EAAE,GAAKb,mBAAmB,CAAC,UAAAV,mBAAA,iBAAzDA,mBAAA,CAA2DyB,IAAI,CAC5D,CAAC,EACP,CACH,EACE,CAAC,CACH,CAAC,CACH,CAAC,cAGN/B,IAAA,QAAK+E,SAAS,CAAC,kEAAkE,CAAAE,QAAA,cAC/EjF,IAAA,QAAK+E,SAAS,CAAC,wCAAwC,CAAAE,QAAA,cACrD/E,KAAA,CAACxB,MAAM,CAAC2F,GAAG,EACTE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BM,SAAS,CAAC,aAAa,CAAAE,QAAA,eAEvBjF,IAAA,OAAI+E,SAAS,CAAC,gDAAgD,CAAAE,QAAA,CAC3DpE,gBAAgB,GAAK,KAAK,CAAG,cAAc,CAAG,CAAAa,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEK,IAAI,GAAI,UAAU,CAChF,CAAC,cACL/B,IAAA,MAAG+E,SAAS,CAAC,iDAAiD,CAAAE,QAAA,CAC3DpE,gBAAgB,GAAK,KAAK,CACvB,qDAAqD,CACrD,CAAAa,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgF,WAAW,GAAI,+BAA+B,CAElE,CAAC,EACM,CAAC,CACV,CAAC,CACH,CAAC,cAGN1G,IAAA,QAAK+E,SAAS,CAAC,mBAAmB,CAAAE,QAAA,cAChCjF,IAAA,QAAK+E,SAAS,CAAC,6CAA6C,CAAAE,QAAA,cAC1DjF,IAAA,QAAK+E,SAAS,CAAC,qCAAqC,CAAAE,QAAA,CACjD,CAAC,CAAEpD,EAAE,CAAE,KAAK,CAAEE,IAAI,CAAE,cAAc,CAAE4E,IAAI,CAAE,KAAM,CAAC,CAAE,GAAG/G,UAAU,CAAC,CAACsC,GAAG,CAAEoB,QAAQ,eAC9EpD,KAAA,CAACxB,MAAM,CAAC6G,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BmB,OAAO,CAAEA,CAAA,GAAM,CACb9F,mBAAmB,CAACwC,QAAQ,CAACzB,EAAE,CAAC,CAChCZ,sBAAsB,CAAC,KAAK,CAAC,CAC7BT,eAAe,CAAC,CAAE8C,QAAQ,CAAEA,QAAQ,CAACzB,EAAG,CAAC,CAAC,CAC5C,CAAE,CACFkD,SAAS,kFAAAC,MAAA,CACPnE,gBAAgB,GAAKyC,QAAQ,CAACzB,EAAE,CAC5B,0CAA0C,CAC1C,iFAAiF,CACpF,CAAAoD,QAAA,eAEHjF,IAAA,SAAM+E,SAAS,CAAC,SAAS,CAAAE,QAAA,CAAE3B,QAAQ,CAACqD,IAAI,CAAO,CAAC,cAChD3G,IAAA,SAAAiF,QAAA,CAAO3B,QAAQ,CAACvB,IAAI,CAAO,CAAC,GAfvBuB,QAAQ,CAACzB,EAgBD,CAChB,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAEN7B,IAAA,QAAK+E,SAAS,CAAC,6CAA6C,CAAAE,QAAA,cAC1D/E,KAAA,QAAK6E,SAAS,CAAC,iCAAiC,CAAAE,QAAA,eAE9CjF,IAAA,QAAK+E,SAAS,CAAC,uBAAuB,CAAAE,QAAA,cACpC/E,KAAA,QAAK6E,SAAS,CAAC,kDAAkD,CAAAE,QAAA,eAC/D/E,KAAA,QAAK6E,SAAS,CAAC,wCAAwC,CAAAE,QAAA,eACrDjF,IAAA,OAAI+E,SAAS,CAAC,qCAAqC,CAAAE,QAAA,CAAC,SAAO,CAAI,CAAC,cAChEjF,IAAA,WACE4G,OAAO,CAAEA,CAAA,GAAMnF,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CuD,SAAS,CAAC,6BAA6B,CAAAE,QAAA,cAEvCjF,IAAA,CAACZ,yBAAyB,EAAC2F,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3C,CAAC,EACN,CAAC,cAEN7E,KAAA,QAAK6E,SAAS,cAAAC,MAAA,CAAexD,WAAW,CAAG,OAAO,CAAG,iBAAiB,CAAG,CAAAyD,QAAA,eAEvE/E,KAAA,QAAA+E,QAAA,eACEjF,IAAA,OAAI+E,SAAS,CAAC,gCAAgC,CAAAE,QAAA,CAAC,cAAY,CAAI,CAAC,cAChEjF,IAAA,QAAK+E,SAAS,CAAC,WAAW,CAAAE,QAAA,CACvBpC,kBAAkB,CAACX,GAAG,CAACY,IAAI,eAC1B9C,IAAA,WAEE4G,OAAO,CAAEA,CAAA,GAAMzF,cAAc,CAAC2B,IAAI,CAACjB,EAAE,CAAE,CACvCkD,SAAS,4DAAAC,MAAA,CACP9D,WAAW,GAAK4B,IAAI,CAACjB,EAAE,CACnB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAoD,QAAA,cAEH/E,KAAA,QAAK6E,SAAS,CAAC,sBAAsB,CAAAE,QAAA,eACnCjF,IAAA,SAAAiF,QAAA,CAAOnC,IAAI,CAACf,IAAI,CAAO,CAAC,cACxB7B,KAAA,SAAM6E,SAAS,CAAC,SAAS,CAAAE,QAAA,EAAC,GAAC,CAACnC,IAAI,CAACd,KAAK,CAAC,GAAC,EAAM,CAAC,EAC5C,CAAC,EAXDc,IAAI,CAACjB,EAYJ,CACT,CAAC,CACC,CAAC,EACH,CAAC,CAGLhB,gBAAgB,GAAK,KAAK,EAAIiB,aAAa,CAACG,MAAM,CAAG,CAAC,eACrD/B,KAAA,QAAA+E,QAAA,eACE/E,KAAA,OAAI6E,SAAS,CAAC,gCAAgC,CAAAE,QAAA,EAC3CvD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEK,IAAI,CAAC,aACzB,EAAI,CAAC,cACL/B,IAAA,QAAK+E,SAAS,CAAC,WAAW,CAAAE,QAAA,CACvBnD,aAAa,CAACI,GAAG,CAACU,WAAW,eAC5B5C,IAAA,WAEE4G,OAAO,CAAEA,CAAA,GAAM3F,sBAAsB,CAAC2B,WAAW,CAACf,EAAE,CAAE,CACtDkD,SAAS,4DAAAC,MAAA,CACPhE,mBAAmB,GAAK4B,WAAW,CAACf,EAAE,CAClC,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAoD,QAAA,cAEH/E,KAAA,QAAK6E,SAAS,CAAC,sBAAsB,CAAAE,QAAA,eACnCjF,IAAA,SAAAiF,QAAA,CAAOrC,WAAW,CAACb,IAAI,CAAO,CAAC,cAC/B7B,KAAA,SAAM6E,SAAS,CAAC,SAAS,CAAAE,QAAA,EAAC,GAAC,CAACrC,WAAW,CAACZ,KAAK,CAAC,GAAC,EAAM,CAAC,EACnD,CAAC,EAXDY,WAAW,CAACf,EAYX,CACT,CAAC,CACC,CAAC,EACH,CACN,cAGD3B,KAAA,QAAA+E,QAAA,eACE/E,KAAA,OAAI6E,SAAS,CAAC,gCAAgC,CAAAE,QAAA,EAAC,gBAC/B,CAAC7D,UAAU,CAAC,CAAC,CAAC,CAAC,MAAI,CAACA,UAAU,CAAC,CAAC,CAAC,EAC7C,CAAC,cACLpB,IAAA,UACE8C,IAAI,CAAC,OAAO,CACZ+D,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,MAAM,CACV9D,KAAK,CAAE5B,UAAU,CAAC,CAAC,CAAE,CACrB2F,QAAQ,CAAGC,CAAC,EAAK3F,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC,CAAE6F,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAClE,KAAK,CAAC,CAAC,CAAE,CAC1E+B,SAAS,CAAC,QAAQ,CACnB,CAAC,EACC,CAAC,cAGN7E,KAAA,QAAA+E,QAAA,eACEjF,IAAA,OAAI+E,SAAS,CAAC,gCAAgC,CAAAE,QAAA,CAAC,gBAAc,CAAI,CAAC,cAClEjF,IAAA,QAAK+E,SAAS,CAAC,WAAW,CAAAE,QAAA,CACvB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC/C,GAAG,CAAC0B,MAAM,eACzB1D,KAAA,WAEE0G,OAAO,CAAEA,CAAA,GAAMrF,iBAAiB,CAACqC,MAAM,CAAE,CACzCmB,SAAS,8EAAAC,MAAA,CACP1D,cAAc,GAAKsC,MAAM,CACrB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAqB,QAAA,eAEHjF,IAAA,QAAK+E,SAAS,CAAC,MAAM,CAAAE,QAAA,CAClB,CAAC,GAAGW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC1D,GAAG,CAAC,CAAC2D,CAAC,CAAEC,CAAC,gBACtB9F,IAAA,CAACL,aAAa,EAEZoF,SAAS,YAAAC,MAAA,CACPc,CAAC,CAAGlC,MAAM,CAAG,iBAAiB,CAAG,eAAe,CAC/C,EAHEkC,CAIN,CACF,CAAC,CACC,CAAC,cACN9F,IAAA,SAAAiF,QAAA,CAAOrB,MAAM,CAAG,CAAC,IAAAoB,MAAA,CAAMpB,MAAM,YAAY,aAAa,CAAO,CAAC,GAlBzDA,MAmBC,CACT,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN1D,KAAA,QAAK6E,SAAS,CAAC,QAAQ,CAAAE,QAAA,eAErBjF,IAAA,QAAK+E,SAAS,CAAC,yCAAyC,CAAAE,QAAA,cACtD/E,KAAA,QAAK6E,SAAS,CAAC,6EAA6E,CAAAE,QAAA,eAC1FjF,IAAA,QAAAiF,QAAA,cACE/E,KAAA,MAAG6E,SAAS,CAAC,eAAe,CAAAE,QAAA,EAAC,UACnB,CAAC/B,yBAAyB,CAACjB,MAAM,CAAC,MAAI,CAACpC,QAAQ,CAACoC,MAAM,CAAC,WACjE,EAAG,CAAC,CACD,CAAC,cAEN/B,KAAA,QAAK6E,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAE1CjF,IAAA,WACEgD,KAAK,CAAErC,MAAO,CACdoG,QAAQ,CAAGC,CAAC,EAAKpG,SAAS,CAACoG,CAAC,CAACE,MAAM,CAAClE,KAAK,CAAE,CAC3C+B,SAAS,CAAC,sFAAsF,CAAAE,QAAA,CAE/FlC,WAAW,CAACb,GAAG,CAACiF,MAAM,eACrBnH,IAAA,WAA2BgD,KAAK,CAAEmE,MAAM,CAACnE,KAAM,CAAAiC,QAAA,CAC5CkC,MAAM,CAAClE,KAAK,EADFkE,MAAM,CAACnE,KAEZ,CACT,CAAC,CACI,CAAC,cAGT9C,KAAA,QAAK6E,SAAS,CAAC,iCAAiC,CAAAE,QAAA,eAC9CjF,IAAA,WACE4G,OAAO,CAAEA,CAAA,GAAMlG,WAAW,CAAC,MAAM,CAAE,CACnCqE,SAAS,qCAAAC,MAAA,CACPvE,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAAwE,QAAA,cAEHjF,IAAA,CAACjB,cAAc,EAACgG,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,cACT/E,IAAA,WACE4G,OAAO,CAAEA,CAAA,GAAMlG,WAAW,CAAC,MAAM,CAAE,CACnCqE,SAAS,qCAAAC,MAAA,CACPvE,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAAwE,QAAA,cAEHjF,IAAA,CAAChB,cAAc,EAAC+F,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN/E,IAAA,CAACrB,eAAe,EAACyI,IAAI,CAAC,MAAM,CAAAnC,QAAA,cAC1BjF,IAAA,CAACtB,MAAM,CAAC2F,GAAG,EAETE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAE,CAAE,CACrBO,SAAS,IAAAC,MAAA,CACPvE,QAAQ,GAAK,MAAM,CACf,sDAAsD,CACtD,WAAW,CACd,CAAAwE,QAAA,CAEF/B,yBAAyB,CAAChB,GAAG,CAAC,CAACkB,OAAO,CAAEgB,KAAK,gBAC5CpE,IAAA,CAACiE,WAAW,EAAkBb,OAAO,CAAEA,OAAQ,CAACgB,KAAK,CAAEA,KAAM,EAA3ChB,OAAO,CAACvB,EAAqC,CAChE,CAAC,KAAAmD,MAAA,CAZMvE,QAAQ,MAAAuE,MAAA,CAAInE,gBAAgB,MAAAmE,MAAA,CAAIrE,MAAM,CAapC,CAAC,CACE,CAAC,CAEjBuC,yBAAyB,CAACjB,MAAM,GAAK,CAAC,eACrC/B,KAAA,QAAK6E,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCjF,IAAA,QAAK+E,SAAS,CAAC,oBAAoB,CAAAE,QAAA,cACjCjF,IAAA,CAAClB,UAAU,EAACiG,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACzC,CAAC,cACN/E,IAAA,OAAI+E,SAAS,CAAC,0CAA0C,CAAAE,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC/EjF,IAAA,MAAG+E,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5E,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}