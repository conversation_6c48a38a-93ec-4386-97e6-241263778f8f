{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Link}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import Navigation from'./components/Navigation';import{CartProvider}from'./components/ShoppingCart';import HomePage from'./pages/HomePage';import ProductsPage from'./pages/ProductsPage';import DigitalProductsPage from'./pages/DigitalProductsPage';import AboutPage from'./pages/AboutPage';import ContactPage from'./pages/ContactPage';import CheckoutPage from'./pages/CheckoutPage';import{HelpPage,ReturnsPage,ShippingPage,TrackOrderPage,PrivacyPage,TermsPage,CookiesPage,OrdersPage}from'./pages/PlaceholderPage';import MultiLanguageSupport from'./components/MultiLanguageSupport';import EmailNotifications from'./components/EmailNotifications';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(CartProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",children:[/*#__PURE__*/_jsx(Navigation,{}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(HomePage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(ProductsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/digital-products\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(DigitalProductsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/about\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(AboutPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/contact\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(ContactPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/checkout\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(CheckoutPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/help\",element:/*#__PURE__*/_jsx(HelpPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/returns\",element:/*#__PURE__*/_jsx(ReturnsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/shipping\",element:/*#__PURE__*/_jsx(ShippingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/track\",element:/*#__PURE__*/_jsx(TrackOrderPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/orders\",element:/*#__PURE__*/_jsx(OrdersPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/privacy\",element:/*#__PURE__*/_jsx(PrivacyPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/terms\",element:/*#__PURE__*/_jsx(TermsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/cookies\",element:/*#__PURE__*/_jsx(CookiesPage,{})})]})}),/*#__PURE__*/_jsx(\"footer\",{className:\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-span-1 md:col-span-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6 text-white\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold\",children:\"ShopHub\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-4 max-w-md\",children:\"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4\",children:[/*#__PURE__*/_jsx(MultiLanguageSupport,{}),/*#__PURE__*/_jsx(EmailNotifications,{})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Quick Links\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/products\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Products\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/digital-products\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Digital Products\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/about\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"About Us\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/contact\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Contact\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Customer Service\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/help\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Help Center\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/returns\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Returns\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/shipping\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Shipping Info\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/track\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Track Order\"})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm\",children:\"\\xA9 2024 ShopHub. All rights reserved.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-6 mt-4 md:mt-0\",children:[/*#__PURE__*/_jsx(Link,{to:\"/privacy\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Privacy Policy\"}),/*#__PURE__*/_jsx(Link,{to:\"/terms\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Terms of Service\"}),/*#__PURE__*/_jsx(Link,{to:\"/cookies\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Cookie Policy\"})]})]})]})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "CartProvider", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "CheckoutPage", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage", "MultiLanguageSupport", "EmailNotifications", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport {\n  HelpPage,\n  ReturnsPage,\n  ShippingPage,\n  TrackOrderPage,\n  PrivacyPage,\n  TermsPage,\n  CookiesPage,\n  OrdersPage\n} from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <CartProvider>\n      <Router>\n        <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n          <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n            <Route path=\"/checkout\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <CheckoutPage />\n              </motion.div>\n            } />\n            <Route path=\"/help\" element={<HelpPage />} />\n            <Route path=\"/returns\" element={<ReturnsPage />} />\n            <Route path=\"/shipping\" element={<ShippingPage />} />\n            <Route path=\"/track\" element={<TrackOrderPage />} />\n            <Route path=\"/orders\" element={<OrdersPage />} />\n            <Route path=\"/privacy\" element={<PrivacyPage />} />\n            <Route path=\"/terms\" element={<TermsPage />} />\n            <Route path=\"/cookies\" element={<CookiesPage />} />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/help\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</Link></li>\n                  <li><Link to=\"/returns\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</Link></li>\n                  <li><Link to=\"/shipping\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</Link></li>\n                  <li><Link to=\"/track\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</Link>\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</Link>\n                <Link to=\"/cookies\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n        </div>\n      </Router>\n    </CartProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,KAAQ,kBAAkB,CAC/E,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,OAASC,YAAY,KAAQ,2BAA2B,CACxD,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,OACEC,QAAQ,CACRC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,SAAS,CACTC,WAAW,CACXC,UAAU,KACL,yBAAyB,CAChC,MAAO,CAAAC,oBAAoB,KAAM,mCAAmC,CACpE,MAAO,CAAAC,kBAAkB,KAAM,iCAAiC,CAChE,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAAClB,YAAY,EAAAsB,QAAA,cACXJ,IAAA,CAACzB,MAAM,EAAA6B,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3EJ,IAAA,CAACnB,UAAU,GAAE,CAAC,cAEhBmB,IAAA,CAACpB,eAAe,EAAC0B,IAAI,CAAC,MAAM,CAAAF,QAAA,cAC1BF,KAAA,CAAC1B,MAAM,EAAA4B,QAAA,eACLJ,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,GAAG,CAACC,OAAO,cACrBR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACjB,QAAQ,GAAE,CAAC,CACF,CACb,CAAE,CAAC,cACJiB,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,WAAW,CAACC,OAAO,cAC7BR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAAChB,YAAY,GAAE,CAAC,CACN,CACb,CAAE,CAAC,cACJgB,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrCR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACf,mBAAmB,GAAE,CAAC,CACb,CACb,CAAE,CAAC,cACJe,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACd,SAAS,GAAE,CAAC,CACH,CACb,CAAE,CAAC,cACJc,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,UAAU,CAACC,OAAO,cAC5BR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACb,WAAW,GAAE,CAAC,CACL,CACb,CAAE,CAAC,cACJa,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,WAAW,CAACC,OAAO,cAC7BR,IAAA,CAACrB,MAAM,CAAC8B,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACZ,YAAY,GAAE,CAAC,CACN,CACb,CAAE,CAAC,cACJY,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,OAAO,CAACC,OAAO,cAAER,IAAA,CAACX,QAAQ,GAAE,CAAE,CAAE,CAAC,cAC7CW,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,UAAU,CAACC,OAAO,cAAER,IAAA,CAACV,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDU,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAER,IAAA,CAACT,YAAY,GAAE,CAAE,CAAE,CAAC,cACrDS,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAER,IAAA,CAACR,cAAc,GAAE,CAAE,CAAE,CAAC,cACpDQ,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,SAAS,CAACC,OAAO,cAAER,IAAA,CAACJ,UAAU,GAAE,CAAE,CAAE,CAAC,cACjDI,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,UAAU,CAACC,OAAO,cAAER,IAAA,CAACP,WAAW,GAAE,CAAE,CAAE,CAAC,cACnDO,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAER,IAAA,CAACN,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CM,IAAA,CAACvB,KAAK,EAAC8B,IAAI,CAAC,UAAU,CAACC,OAAO,cAAER,IAAA,CAACL,WAAW,GAAE,CAAE,CAAE,CAAC,EAC7C,CAAC,CACM,CAAC,cAGlBK,IAAA,WAAQK,SAAS,CAAC,uDAAuD,CAAAD,QAAA,cACvEF,KAAA,QAAKG,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3DF,KAAA,QAAKG,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDF,KAAA,QAAKG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvCF,KAAA,QAAKG,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/CJ,IAAA,QAAKK,SAAS,CAAC,oHAAoH,CAAAD,QAAA,cACjIJ,IAAA,QAAKK,SAAS,CAAC,oBAAoB,CAACY,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAf,QAAA,cACvFJ,IAAA,SAAMoB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,4CAA4C,CAAE,CAAC,CACjH,CAAC,CACH,CAAC,cACNvB,IAAA,SAAMK,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAChD,CAAC,cACNJ,IAAA,MAAGK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,0JAG3C,CAAG,CAAC,cACJF,KAAA,QAAKG,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BJ,IAAA,CAACH,oBAAoB,GAAE,CAAC,cACxBG,IAAA,CAACF,kBAAkB,GAAE,CAAC,EACnB,CAAC,EACH,CAAC,cAGNI,KAAA,QAAAE,QAAA,eACEJ,IAAA,OAAIK,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3DF,KAAA,OAAIG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACvBJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,GAAG,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAI,CAAC,cACzGJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,WAAW,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,cACrHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,mBAAmB,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,CAAI,CAAC,cACrIJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,QAAQ,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,cAClHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,UAAU,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAI,CAAC,EACjH,CAAC,EACF,CAAC,cAGNF,KAAA,QAAAE,QAAA,eACEJ,IAAA,OAAIK,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAChEF,KAAA,OAAIG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACvBJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,OAAO,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,CAAI,CAAC,cACpHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,UAAU,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAI,CAAC,cACnHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,WAAW,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,CAAI,CAAC,cAC1HJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,QAAQ,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,CAAI,CAAC,EACnH,CAAC,EACF,CAAC,EACH,CAAC,cAENF,KAAA,QAAKG,SAAS,CAAC,2FAA2F,CAAAD,QAAA,eACxGJ,IAAA,MAAGK,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,yCAErC,CAAG,CAAC,cACJF,KAAA,QAAKG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,UAAU,CAACnB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,cACzHJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,QAAQ,CAACnB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACzHJ,IAAA,CAACtB,IAAI,EAAC8C,EAAE,CAAC,UAAU,CAACnB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,eAAa,CAAM,CAAC,EACrH,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,EACJ,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}