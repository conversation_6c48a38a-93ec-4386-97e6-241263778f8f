{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Button.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  onClick,\n  className = '',\n  icon: Icon,\n  iconPosition = 'left',\n  fullWidth = false,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';\n  const variants = {\n    primary: 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    secondary: 'bg-white text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-50 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    outline: 'bg-transparent text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-500 hover:text-white focus:ring-light-orange-300',\n    ghost: 'bg-transparent text-light-orange-600 hover:bg-light-orange-100 focus:ring-light-orange-300',\n    danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-300 shadow-md hover:shadow-lg',\n    success: 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-300 shadow-md hover:shadow-lg',\n    digital: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-md hover:shadow-lg'\n  };\n  const sizes = {\n    small: 'px-3 py-2 text-sm',\n    medium: 'px-4 py-3 text-base',\n    large: 'px-6 py-4 text-lg'\n  };\n  const disabledClasses = 'opacity-50 cursor-not-allowed pointer-events-none';\n  const fullWidthClass = fullWidth ? 'w-full' : '';\n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant]}\n    ${sizes[size]}\n    ${disabled ? disabledClasses : ''}\n    ${fullWidthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n  const handleClick = e => {\n    if (!disabled && !loading && onClick) {\n      onClick(e);\n    }\n  };\n  const renderIcon = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this);\n    }\n    if (Icon) {\n      return /*#__PURE__*/_jsxDEV(Icon, {\n        className: `w-5 h-5 ${iconPosition === 'right' ? 'ml-2' : 'mr-2'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: !disabled ? {\n      scale: 1.02\n    } : {},\n    whileTap: !disabled ? {\n      scale: 0.98\n    } : {},\n    className: buttonClasses,\n    onClick: handleClick,\n    disabled: disabled || loading,\n    ...props,\n    children: [iconPosition === 'left' && renderIcon(), loading ? 'Loading...' : children, iconPosition === 'right' && renderIcon()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "disabled", "loading", "onClick", "className", "icon", "Icon", "iconPosition", "fullWidth", "props", "baseClasses", "variants", "primary", "secondary", "outline", "ghost", "danger", "success", "digital", "sizes", "small", "medium", "large", "disabledClasses", "fullWidthClass", "buttonClasses", "trim", "replace", "handleClick", "e", "renderIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Button.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  onClick,\n  className = '',\n  icon: Icon,\n  iconPosition = 'left',\n  fullWidth = false,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    secondary: 'bg-white text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-50 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    outline: 'bg-transparent text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-500 hover:text-white focus:ring-light-orange-300',\n    ghost: 'bg-transparent text-light-orange-600 hover:bg-light-orange-100 focus:ring-light-orange-300',\n    danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-300 shadow-md hover:shadow-lg',\n    success: 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-300 shadow-md hover:shadow-lg',\n    digital: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-md hover:shadow-lg'\n  };\n\n  const sizes = {\n    small: 'px-3 py-2 text-sm',\n    medium: 'px-4 py-3 text-base',\n    large: 'px-6 py-4 text-lg'\n  };\n\n  const disabledClasses = 'opacity-50 cursor-not-allowed pointer-events-none';\n  const fullWidthClass = fullWidth ? 'w-full' : '';\n\n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant]}\n    ${sizes[size]}\n    ${disabled ? disabledClasses : ''}\n    ${fullWidthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  const handleClick = (e) => {\n    if (!disabled && !loading && onClick) {\n      onClick(e);\n    }\n  };\n\n  const renderIcon = () => {\n    if (loading) {\n      return (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\" />\n      );\n    }\n    \n    if (Icon) {\n      return (\n        <Icon className={`w-5 h-5 ${iconPosition === 'right' ? 'ml-2' : 'mr-2'}`} />\n      );\n    }\n    \n    return null;\n  };\n\n  return (\n    <motion.button\n      whileHover={!disabled ? { scale: 1.02 } : {}}\n      whileTap={!disabled ? { scale: 0.98 } : {}}\n      className={buttonClasses}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {iconPosition === 'left' && renderIcon()}\n      {loading ? 'Loading...' : children}\n      {iconPosition === 'right' && renderIcon()}\n    </motion.button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,MAAM,GAAGA,CAAC;EACdC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,OAAO;EACPC,SAAS,GAAG,EAAE;EACdC,IAAI,EAAEC,IAAI;EACVC,YAAY,GAAG,MAAM;EACrBC,SAAS,GAAG,KAAK;EACjB,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,kJAAkJ;EAEtK,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,mLAAmL;IAC5LC,SAAS,EAAE,gJAAgJ;IAC3JC,OAAO,EAAE,8IAA8I;IACvJC,KAAK,EAAE,4FAA4F;IACnGC,MAAM,EAAE,sIAAsI;IAC9IC,OAAO,EAAE,gJAAgJ;IACzJC,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,KAAK,GAAG;IACZC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,qBAAqB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,eAAe,GAAG,mDAAmD;EAC3E,MAAMC,cAAc,GAAGhB,SAAS,GAAG,QAAQ,GAAG,EAAE;EAEhD,MAAMiB,aAAa,GAAG;AACxB,MAAMf,WAAW;AACjB,MAAMC,QAAQ,CAACZ,OAAO,CAAC;AACvB,MAAMoB,KAAK,CAACnB,IAAI,CAAC;AACjB,MAAMC,QAAQ,GAAGsB,eAAe,GAAG,EAAE;AACrC,MAAMC,cAAc;AACpB,MAAMpB,SAAS;AACf,GAAG,CAACsB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAE7B,MAAMC,WAAW,GAAIC,CAAC,IAAK;IACzB,IAAI,CAAC5B,QAAQ,IAAI,CAACC,OAAO,IAAIC,OAAO,EAAE;MACpCA,OAAO,CAAC0B,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5B,OAAO,EAAE;MACX,oBACEN,OAAA;QAAKQ,SAAS,EAAC;MAAkE;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAExF;IAEA,IAAI5B,IAAI,EAAE;MACR,oBACEV,OAAA,CAACU,IAAI;QAACF,SAAS,EAAE,WAAWG,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM;MAAG;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAEhF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEtC,OAAA,CAACF,MAAM,CAACyC,MAAM;IACZC,UAAU,EAAE,CAACnC,QAAQ,GAAG;MAAEoC,KAAK,EAAE;IAAK,CAAC,GAAG,CAAC,CAAE;IAC7CC,QAAQ,EAAE,CAACrC,QAAQ,GAAG;MAAEoC,KAAK,EAAE;IAAK,CAAC,GAAG,CAAC,CAAE;IAC3CjC,SAAS,EAAEqB,aAAc;IACzBtB,OAAO,EAAEyB,WAAY;IACrB3B,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAAA,GAC1BO,KAAK;IAAAX,QAAA,GAERS,YAAY,KAAK,MAAM,IAAIuB,UAAU,CAAC,CAAC,EACvC5B,OAAO,GAAG,YAAY,GAAGJ,QAAQ,EACjCS,YAAY,KAAK,OAAO,IAAIuB,UAAU,CAAC,CAAC;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEpB,CAAC;AAACK,EAAA,GA/EI1C,MAAM;AAiFZ,eAAeA,MAAM;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}