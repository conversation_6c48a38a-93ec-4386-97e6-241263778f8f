{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport { HelpPage, ReturnsPage, ShippingPage, TrackOrderPage, PrivacyPage, TermsPage, CookiesPage, OrdersPage } from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(CartProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n        children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/products\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/digital-products\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(DigitalProductsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/contact\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/checkout\",\n              element: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/help\",\n              element: /*#__PURE__*/_jsxDEV(HelpPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/returns\",\n              element: /*#__PURE__*/_jsxDEV(ReturnsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/shipping\",\n              element: /*#__PURE__*/_jsxDEV(ShippingPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/track\",\n              element: /*#__PURE__*/_jsxDEV(TrackOrderPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/orders\",\n              element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/privacy\",\n              element: /*#__PURE__*/_jsxDEV(PrivacyPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/terms\",\n              element: /*#__PURE__*/_jsxDEV(TermsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cookies\",\n              element: /*#__PURE__*/_jsxDEV(CookiesPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n          className: \"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-span-1 md:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-white\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 116,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 115,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold\",\n                    children: \"ShopHub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-300 mb-4 max-w-md\",\n                  children: \"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(MultiLanguageSupport, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(EmailNotifications, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-4\",\n                  children: \"Quick Links\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Home\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/products\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/digital-products\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Digital Products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/about\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"About Us\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/contact\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Contact\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 139,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-4\",\n                  children: \"Customer Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/help\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Help Center\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/returns\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Returns\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/shipping\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Shipping Info\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/track\",\n                      className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                      children: \"Track Order\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-sm\",\n                children: \"\\xA9 2024 ShopHub. All rights reserved.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-6 mt-4 md:mt-0\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/privacy\",\n                  className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/terms\",\n                  className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                  children: \"Terms of Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/cookies\",\n                  className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                  children: \"Cookie Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "CartProvider", "UserProvider", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "CheckoutPage", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage", "MultiLanguageSupport", "EmailNotifications", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport {\n  HelpPage,\n  ReturnsPage,\n  ShippingPage,\n  TrackOrderPage,\n  PrivacyPage,\n  TermsPage,\n  CookiesPage,\n  OrdersPage\n} from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <CartProvider>\n      <Router>\n        <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n          <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n            <Route path=\"/checkout\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <CheckoutPage />\n              </motion.div>\n            } />\n            <Route path=\"/help\" element={<HelpPage />} />\n            <Route path=\"/returns\" element={<ReturnsPage />} />\n            <Route path=\"/shipping\" element={<ShippingPage />} />\n            <Route path=\"/track\" element={<TrackOrderPage />} />\n            <Route path=\"/orders\" element={<OrdersPage />} />\n            <Route path=\"/privacy\" element={<PrivacyPage />} />\n            <Route path=\"/terms\" element={<TermsPage />} />\n            <Route path=\"/cookies\" element={<CookiesPage />} />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/help\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</Link></li>\n                  <li><Link to=\"/returns\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</Link></li>\n                  <li><Link to=\"/shipping\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</Link></li>\n                  <li><Link to=\"/track\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</Link>\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</Link>\n                <Link to=\"/cookies\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n        </div>\n      </Router>\n    </CartProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SACEC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,QACL,yBAAyB;AAChC,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACnB,YAAY;IAAAqB,QAAA,eACXF,OAAA,CAAC1B,MAAM;MAAA4B,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,8DAA8D;QAAAD,QAAA,gBAC3EF,OAAA,CAACpB,UAAU;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhBP,OAAA,CAACrB,eAAe;UAAC6B,IAAI,EAAC,MAAM;UAAAN,QAAA,eAC1BF,OAAA,CAACzB,MAAM;YAAA2B,QAAA,gBACLF,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,GAAG;cAACC,OAAO,eACrBV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAACjB,QAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,WAAW;cAACC,OAAO,eAC7BV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAAChB,YAAY;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,mBAAmB;cAACC,OAAO,eACrCV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAACf,mBAAmB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAC1BV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAACd,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,UAAU;cAACC,OAAO,eAC5BV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAACb,WAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,WAAW;cAACC,OAAO,eAC7BV,OAAA,CAACtB,MAAM,CAACiC,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,IAAI,EAAE;kBAAEH,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BG,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,eAE9BF,OAAA,CAACZ,YAAY;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,OAAO;cAACC,OAAO,eAAEV,OAAA,CAACX,QAAQ;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEV,OAAA,CAACV,WAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEV,OAAA,CAACT,YAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEV,OAAA,CAACR,cAAc;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEV,OAAA,CAACJ,UAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEV,OAAA,CAACP,WAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEV,OAAA,CAACN,SAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAACxB,KAAK;cAACiC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEV,OAAA,CAACL,WAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGlBP,OAAA;UAAQG,SAAS,EAAC,uDAAuD;UAAAD,QAAA,eACvEF,OAAA;YAAKG,SAAS,EAAC,8CAA8C;YAAAD,QAAA,gBAC3DF,OAAA;cAAKG,SAAS,EAAC,uCAAuC;cAAAD,QAAA,gBAEpDF,OAAA;gBAAKG,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,gBACvCF,OAAA;kBAAKG,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,gBAC/CF,OAAA;oBAAKG,SAAS,EAAC,oHAAoH;oBAAAD,QAAA,eACjIF,OAAA;sBAAKG,SAAS,EAAC,oBAAoB;sBAACgB,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAnB,QAAA,eACvFF,OAAA;wBAAMsB,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA4C;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNP,OAAA;oBAAMG,SAAS,EAAC,oBAAoB;oBAAAD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNP,OAAA;kBAAGG,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,EAAC;gBAG3C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAKG,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC7BF,OAAA,CAACH,oBAAoB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBP,OAAA,CAACF,kBAAkB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNP,OAAA;gBAAAE,QAAA,gBACEF,OAAA;kBAAIG,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3DP,OAAA;kBAAIG,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACvBF,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,GAAG;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzGP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,WAAW;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrHP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,mBAAmB;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrIP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,QAAQ;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClHP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,UAAU;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGNP,OAAA;gBAAAE,QAAA,gBACEF,OAAA;kBAAIG,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEP,OAAA;kBAAIG,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACvBF,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,OAAO;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpHP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,UAAU;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnHP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,WAAW;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1HP,OAAA;oBAAAE,QAAA,eAAIF,OAAA,CAACvB,IAAI;sBAACiD,EAAE,EAAC,QAAQ;sBAACvB,SAAS,EAAC,6DAA6D;sBAAAD,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENP,OAAA;cAAKG,SAAS,EAAC,2FAA2F;cAAAD,QAAA,gBACxGF,OAAA;gBAAGG,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAErC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAKG,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAC1CF,OAAA,CAACvB,IAAI;kBAACiD,EAAE,EAAC,UAAU;kBAACvB,SAAS,EAAC,qEAAqE;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzHP,OAAA,CAACvB,IAAI;kBAACiD,EAAE,EAAC,QAAQ;kBAACvB,SAAS,EAAC,qEAAqE;kBAAAD,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzHP,OAAA,CAACvB,IAAI;kBAACiD,EAAE,EAAC,UAAU;kBAACvB,SAAS,EAAC,qEAAqE;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACoB,EAAA,GAhJQ1B,GAAG;AAkJZ,eAAeA,GAAG;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}