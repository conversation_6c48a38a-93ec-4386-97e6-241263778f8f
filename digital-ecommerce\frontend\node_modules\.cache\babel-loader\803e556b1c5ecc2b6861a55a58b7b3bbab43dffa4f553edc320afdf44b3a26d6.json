{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{<PERSON>}from'react-router-dom';import{ArrowDownTrayIcon as CloudDownloadIcon,ShieldCheckIcon,ClockIcon,StarIcon,ShoppingBagIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,GlobeAltIcon,CheckCircleIcon,InformationCircleIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid}from'@heroicons/react/24/solid';import{getDigitalProducts}from'../data/products';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DigitalProductsPage=()=>{const[selectedCategory,setSelectedCategory]=useState('all');const[selectedPlatform,setSelectedPlatform]=useState('all');const digitalProducts=getDigitalProducts();const digitalCategories=[{id:'all',name:'All Digital Products',icon:'💿'},{id:'software',name:'Software & Licenses',icon:'💻'},{id:'gaming',name:'Gaming',icon:'🎮'}];const platforms=[{id:'all',name:'All Platforms'},{id:'Windows',name:'Windows'},{id:'macOS',name:'macOS'},{id:'Steam',name:'Steam'},{id:'Xbox Console',name:'Xbox'},{id:'PlayStation',name:'PlayStation'}];const filteredProducts=digitalProducts.filter(product=>{const categoryMatch=selectedCategory==='all'||product.category===selectedCategory;const platformMatch=selectedPlatform==='all'||product.platforms&&product.platforms.includes(selectedPlatform)||product.platform===selectedPlatform;return categoryMatch&&platformMatch;});const DigitalProductCard=_ref=>{let{product}=_ref;return/*#__PURE__*/_jsxs(motion.div,{whileHover:{y:-5},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images[0],alt:product.name,className:\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",children:product.badge||'Digital'})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-3 h-3 mr-1\"}),\"Instant\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-2\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\")\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 space-y-2\",children:[product.platforms&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ComputerDesktopIcon,{className:\"w-4 h-4 text-gray-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:product.platforms.join(', ')})]}),product.licenseType&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-4 h-4 text-green-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-green-600\",children:product.licenseType})]}),product.validityPeriod&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 text-blue-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-blue-600\",children:product.validityPeriod})]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},className:\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Get Instantly\"})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 py-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl lg:text-5xl font-bold text-white mb-6\",children:\"Digital Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-blue-100 max-w-2xl mx-auto mb-8\",children:\"Instant access to software licenses, games, and digital content. Download immediately after purchase with lifetime support.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"Instant Delivery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"Get your license keys and download links immediately\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"100% Genuine\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"All licenses are authentic and verified\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"Lifetime Support\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"Get help whenever you need it\"})]})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-4 justify-center\",children:digitalCategories.map(category=>/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>setSelectedCategory(category.id),className:\"flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all \".concat(selectedCategory===category.id?'bg-blue-500 text-white shadow-lg':'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:category.icon}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]},category.id))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:w-64 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Filters\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Platform\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:platforms.map(platform=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedPlatform(platform.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(selectedPlatform===platform.id?'bg-blue-100 text-blue-700':'text-gray-600 hover:bg-gray-100'),children:platform.name},platform.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(InformationCircleIcon,{className:\"w-5 h-5 text-blue-600 mr-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-blue-900\",children:\"Digital Delivery\"})]}),/*#__PURE__*/_jsxs(\"ul\",{className:\"text-sm text-blue-700 space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Instant email delivery\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 No shipping required\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 24/7 download access\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Secure activation\"})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Showing \",filteredProducts.length,\" digital products\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8\",children:filteredProducts.map((product,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3,delay:index*0.1},children:/*#__PURE__*/_jsx(DigitalProductCard,{product:product})},product.id))}),filteredProducts.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-4\",children:/*#__PURE__*/_jsx(ComputerDesktopIcon,{className:\"w-16 h-16 mx-auto\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:\"No digital products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Try adjusting your filters to see more results.\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 to-purple-600 py-16\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-white mb-4\",children:\"Need Help Choosing?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-blue-100 mb-8\",children:\"Our experts are here to help you find the perfect software solution\"}),/*#__PURE__*/_jsx(Link,{to:\"/contact\",className:\"inline-flex items-center bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\",children:\"Contact Support\"})]})})]});};export default DigitalProductsPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "ArrowDownTrayIcon", "CloudDownloadIcon", "ShieldCheckIcon", "ClockIcon", "StarIcon", "ShoppingBagIcon", "ComputerDesktopIcon", "DevicePhoneMobileIcon", "GlobeAltIcon", "CheckCircleIcon", "InformationCircleIcon", "StarIconSolid", "getDigitalProducts", "jsx", "_jsx", "jsxs", "_jsxs", "DigitalProductsPage", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedPlatform", "setSelectedPlatform", "digitalProducts", "digitalCategories", "id", "name", "icon", "platforms", "filteredProducts", "filter", "product", "categoryMatch", "category", "platformMatch", "includes", "platform", "DigitalProductCard", "_ref", "div", "whileHover", "y", "className", "children", "src", "images", "alt", "badge", "Array", "map", "_", "i", "Math", "floor", "rating", "reviews", "price", "originalPrice", "join", "licenseType", "validityPeriod", "button", "scale", "whileTap", "initial", "opacity", "animate", "onClick", "concat", "length", "index", "transition", "duration", "delay", "to"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/DigitalProductsPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport {\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ShieldCheckIcon,\n  ClockIcon,\n  StarIcon,\n  ShoppingBagIcon,\n  ComputerDesktopIcon,\n  DevicePhoneMobileIcon,\n  GlobeAltIcon,\n  CheckCircleIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getDigitalProducts } from '../data/products';\n\nconst DigitalProductsPage = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedPlatform, setSelectedPlatform] = useState('all');\n  \n  const digitalProducts = getDigitalProducts();\n  \n  const digitalCategories = [\n    { id: 'all', name: 'All Digital Products', icon: '💿' },\n    { id: 'software', name: 'Software & Licenses', icon: '💻' },\n    { id: 'gaming', name: 'Gaming', icon: '🎮' }\n  ];\n\n  const platforms = [\n    { id: 'all', name: 'All Platforms' },\n    { id: 'Windows', name: 'Windows' },\n    { id: 'macOS', name: 'macOS' },\n    { id: 'Steam', name: 'Steam' },\n    { id: 'Xbox Console', name: 'Xbox' },\n    { id: 'PlayStation', name: 'PlayStation' }\n  ];\n\n  const filteredProducts = digitalProducts.filter(product => {\n    const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n    const platformMatch = selectedPlatform === 'all' || \n      (product.platforms && product.platforms.includes(selectedPlatform)) ||\n      product.platform === selectedPlatform;\n    return categoryMatch && platformMatch;\n  });\n\n  const DigitalProductCard = ({ product }) => (\n    <motion.div\n      whileHover={{ y: -5 }}\n      className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n    >\n      <div className=\"relative\">\n        <img\n          src={product.images[0]}\n          alt={product.name}\n          className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n        />\n        <div className=\"absolute top-4 left-4\">\n          <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n            {product.badge || 'Digital'}\n          </span>\n        </div>\n        <div className=\"absolute top-4 right-4\">\n          <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n            <ClockIcon className=\"w-3 h-3 mr-1\" />\n            Instant\n          </span>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{product.name}</h3>\n        \n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex\">\n            {[...Array(5)].map((_, i) => (\n              i < Math.floor(product.rating) ? (\n                <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n              ) : (\n                <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n              )\n            ))}\n          </div>\n          <span className=\"text-sm text-gray-600 ml-2\">\n            {product.rating} ({product.reviews})\n          </span>\n        </div>\n\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <span className=\"text-2xl font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.originalPrice && product.originalPrice > product.price && (\n            <span className=\"text-lg text-gray-500 line-through\">\n              ${product.originalPrice}\n            </span>\n          )}\n        </div>\n\n        {/* Platform/License Info */}\n        <div className=\"mb-4 space-y-2\">\n          {product.platforms && (\n            <div className=\"flex items-center space-x-2\">\n              <ComputerDesktopIcon className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-sm text-gray-600\">\n                {product.platforms.join(', ')}\n              </span>\n            </div>\n          )}\n          {product.licenseType && (\n            <div className=\"flex items-center space-x-2\">\n              <ShieldCheckIcon className=\"w-4 h-4 text-green-500\" />\n              <span className=\"text-sm text-green-600\">{product.licenseType}</span>\n            </div>\n          )}\n          {product.validityPeriod && (\n            <div className=\"flex items-center space-x-2\">\n              <ClockIcon className=\"w-4 h-4 text-blue-500\" />\n              <span className=\"text-sm text-blue-600\">{product.validityPeriod}</span>\n            </div>\n          )}\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n        >\n          <CloudDownloadIcon className=\"w-5 h-5\" />\n          <span>Get Instantly</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-6\">\n              Digital Products\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-2xl mx-auto mb-8\">\n              Instant access to software licenses, games, and digital content. \n              Download immediately after purchase with lifetime support.\n            </p>\n            \n            {/* Key Benefits */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\">\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <CloudDownloadIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Instant Delivery</h3>\n                <p className=\"text-blue-100 text-sm\">Get your license keys and download links immediately</p>\n              </div>\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <ShieldCheckIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">100% Genuine</h3>\n                <p className=\"text-blue-100 text-sm\">All licenses are authentic and verified</p>\n              </div>\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <CheckCircleIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Lifetime Support</h3>\n                <p className=\"text-blue-100 text-sm\">Get help whenever you need it</p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {digitalCategories.map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Filters</h3>\n              \n              {/* Platform Filter */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium text-gray-900 mb-3\">Platform</h4>\n                <div className=\"space-y-2\">\n                  {platforms.map(platform => (\n                    <button\n                      key={platform.id}\n                      onClick={() => setSelectedPlatform(platform.id)}\n                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                        selectedPlatform === platform.id\n                          ? 'bg-blue-100 text-blue-700'\n                          : 'text-gray-600 hover:bg-gray-100'\n                      }`}\n                    >\n                      {platform.name}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Digital Product Info */}\n              <div className=\"bg-blue-50 rounded-lg p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <InformationCircleIcon className=\"w-5 h-5 text-blue-600 mr-2\" />\n                  <h4 className=\"font-medium text-blue-900\">Digital Delivery</h4>\n                </div>\n                <ul className=\"text-sm text-blue-700 space-y-1\">\n                  <li>• Instant email delivery</li>\n                  <li>• No shipping required</li>\n                  <li>• 24/7 download access</li>\n                  <li>• Secure activation</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Products Grid */}\n          <div className=\"flex-1\">\n            <div className=\"mb-6\">\n              <p className=\"text-gray-600\">\n                Showing {filteredProducts.length} digital products\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8\">\n              {filteredProducts.map((product, index) => (\n                <motion.div\n                  key={product.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                >\n                  <DigitalProductCard product={product} />\n                </motion.div>\n              ))}\n            </div>\n\n            {filteredProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <ComputerDesktopIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No digital products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 py-16\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">\n            Need Help Choosing?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Our experts are here to help you find the perfect software solution\n          </p>\n          <Link\n            to=\"/contact\"\n            className=\"inline-flex items-center bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\"\n          >\n            Contact Support\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DigitalProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,iBAAiB,GAAI,CAAAC,iBAAiB,CACtCC,eAAe,CACfC,SAAS,CACTC,QAAQ,CACRC,eAAe,CACfC,mBAAmB,CACnBC,qBAAqB,CACrBC,YAAY,CACZC,eAAe,CACfC,qBAAqB,KAChB,6BAA6B,CACpC,OAASN,QAAQ,GAAI,CAAAO,aAAa,KAAQ,2BAA2B,CACrE,OAASC,kBAAkB,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAAAyB,eAAe,CAAGV,kBAAkB,CAAC,CAAC,CAE5C,KAAM,CAAAW,iBAAiB,CAAG,CACxB,CAAEC,EAAE,CAAE,KAAK,CAAEC,IAAI,CAAE,sBAAsB,CAAEC,IAAI,CAAE,IAAK,CAAC,CACvD,CAAEF,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC3D,CAAEF,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAE,IAAK,CAAC,CAC7C,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEH,EAAE,CAAE,KAAK,CAAEC,IAAI,CAAE,eAAgB,CAAC,CACpC,CAAED,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,SAAU,CAAC,CAClC,CAAED,EAAE,CAAE,OAAO,CAAEC,IAAI,CAAE,OAAQ,CAAC,CAC9B,CAAED,EAAE,CAAE,OAAO,CAAEC,IAAI,CAAE,OAAQ,CAAC,CAC9B,CAAED,EAAE,CAAE,cAAc,CAAEC,IAAI,CAAE,MAAO,CAAC,CACpC,CAAED,EAAE,CAAE,aAAa,CAAEC,IAAI,CAAE,aAAc,CAAC,CAC3C,CAED,KAAM,CAAAG,gBAAgB,CAAGN,eAAe,CAACO,MAAM,CAACC,OAAO,EAAI,CACzD,KAAM,CAAAC,aAAa,CAAGb,gBAAgB,GAAK,KAAK,EAAIY,OAAO,CAACE,QAAQ,GAAKd,gBAAgB,CACzF,KAAM,CAAAe,aAAa,CAAGb,gBAAgB,GAAK,KAAK,EAC7CU,OAAO,CAACH,SAAS,EAAIG,OAAO,CAACH,SAAS,CAACO,QAAQ,CAACd,gBAAgB,CAAE,EACnEU,OAAO,CAACK,QAAQ,GAAKf,gBAAgB,CACvC,MAAO,CAAAW,aAAa,EAAIE,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAG,kBAAkB,CAAGC,IAAA,MAAC,CAAEP,OAAQ,CAAC,CAAAO,IAAA,oBACrCrB,KAAA,CAAClB,MAAM,CAACwC,GAAG,EACTC,UAAU,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBC,SAAS,CAAC,qEAAqE,CAAAC,QAAA,eAE/E1B,KAAA,QAAKyB,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB5B,IAAA,QACE6B,GAAG,CAAEb,OAAO,CAACc,MAAM,CAAC,CAAC,CAAE,CACvBC,GAAG,CAAEf,OAAO,CAACL,IAAK,CAClBgB,SAAS,CAAC,kFAAkF,CAC7F,CAAC,cACF3B,IAAA,QAAK2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC5B,IAAA,SAAM2B,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAClFZ,OAAO,CAACgB,KAAK,EAAI,SAAS,CACvB,CAAC,CACJ,CAAC,cACNhC,IAAA,QAAK2B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC1B,KAAA,SAAMyB,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eACjG5B,IAAA,CAACX,SAAS,EAACsC,SAAS,CAAC,cAAc,CAAE,CAAC,UAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClB5B,IAAA,OAAI2B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEZ,OAAO,CAACL,IAAI,CAAK,CAAC,cAE5ET,KAAA,QAAKyB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC5B,IAAA,QAAK2B,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACtB,OAAO,CAACuB,MAAM,CAAC,cAC5BvC,IAAA,CAACH,aAAa,EAAS8B,SAAS,CAAC,yBAAyB,EAAtCS,CAAwC,CAAC,cAE7DpC,IAAA,CAACV,QAAQ,EAASqC,SAAS,CAAC,uBAAuB,EAApCS,CAAsC,CAExD,CAAC,CACC,CAAC,cACNlC,KAAA,SAAMyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCZ,OAAO,CAACuB,MAAM,CAAC,IAAE,CAACvB,OAAO,CAACwB,OAAO,CAAC,GACrC,EAAM,CAAC,EACJ,CAAC,cAENtC,KAAA,QAAKyB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C1B,KAAA,SAAMyB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,GACxD,CAACZ,OAAO,CAACyB,KAAK,EACX,CAAC,CACNzB,OAAO,CAAC0B,aAAa,EAAI1B,OAAO,CAAC0B,aAAa,CAAG1B,OAAO,CAACyB,KAAK,eAC7DvC,KAAA,SAAMyB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACZ,OAAO,CAAC0B,aAAa,EACnB,CACP,EACE,CAAC,cAGNxC,KAAA,QAAKyB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5BZ,OAAO,CAACH,SAAS,eAChBX,KAAA,QAAKyB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5B,IAAA,CAACR,mBAAmB,EAACmC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACzD3B,IAAA,SAAM2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCZ,OAAO,CAACH,SAAS,CAAC8B,IAAI,CAAC,IAAI,CAAC,CACzB,CAAC,EACJ,CACN,CACA3B,OAAO,CAAC4B,WAAW,eAClB1C,KAAA,QAAKyB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5B,IAAA,CAACZ,eAAe,EAACuC,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtD3B,IAAA,SAAM2B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEZ,OAAO,CAAC4B,WAAW,CAAO,CAAC,EAClE,CACN,CACA5B,OAAO,CAAC6B,cAAc,eACrB3C,KAAA,QAAKyB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5B,IAAA,CAACX,SAAS,EAACsC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/C3B,IAAA,SAAM2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEZ,OAAO,CAAC6B,cAAc,CAAO,CAAC,EACpE,CACN,EACE,CAAC,cAEN3C,KAAA,CAAClB,MAAM,CAAC8D,MAAM,EACZrB,UAAU,CAAE,CAAEsB,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BpB,SAAS,CAAC,yMAAyM,CAAAC,QAAA,eAEnN5B,IAAA,CAACb,iBAAiB,EAACwC,SAAS,CAAC,SAAS,CAAE,CAAC,cACzC3B,IAAA,SAAA4B,QAAA,CAAM,eAAa,CAAM,CAAC,EACb,CAAC,EACb,CAAC,EACI,CAAC,EACd,CAED,mBACE1B,KAAA,QAAKyB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAEtC5B,IAAA,QAAK2B,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9E5B,IAAA,QAAK2B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrD1B,KAAA,CAAClB,MAAM,CAACwC,GAAG,EACTyB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAExB,CAAC,CAAE,EAAG,CAAE,CAC/ByB,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAExB,CAAC,CAAE,CAAE,CAAE,CAC9BC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEvB5B,IAAA,OAAI2B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,kBAE/D,CAAI,CAAC,cACL5B,IAAA,MAAG2B,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6HAG5D,CAAG,CAAC,cAGJ1B,KAAA,QAAKyB,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D1B,KAAA,QAAKyB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE5B,IAAA,CAACb,iBAAiB,EAACwC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACjE3B,IAAA,OAAI2B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3E5B,IAAA,MAAG2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sDAAoD,CAAG,CAAC,EAC1F,CAAC,cACN1B,KAAA,QAAKyB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE5B,IAAA,CAACZ,eAAe,EAACuC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC/D3B,IAAA,OAAI2B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACvE5B,IAAA,MAAG2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yCAAuC,CAAG,CAAC,EAC7E,CAAC,cACN1B,KAAA,QAAKyB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrE5B,IAAA,CAACL,eAAe,EAACgC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC/D3B,IAAA,OAAI2B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3E5B,IAAA,MAAG2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EACnE,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,CACH,CAAC,cAGN5B,IAAA,QAAK2B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC5B,IAAA,QAAK2B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D5B,IAAA,QAAK2B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CACjDnB,iBAAiB,CAACyB,GAAG,CAAEhB,QAAQ,eAC9BhB,KAAA,CAAClB,MAAM,CAAC8D,MAAM,EAEZrB,UAAU,CAAE,CAAEsB,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BK,OAAO,CAAEA,CAAA,GAAM/C,mBAAmB,CAACa,QAAQ,CAACR,EAAE,CAAE,CAChDiB,SAAS,kFAAA0B,MAAA,CACPjD,gBAAgB,GAAKc,QAAQ,CAACR,EAAE,CAC5B,kCAAkC,CAClC,iEAAiE,CACpE,CAAAkB,QAAA,eAEH5B,IAAA,SAAM2B,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEV,QAAQ,CAACN,IAAI,CAAO,CAAC,cAChDZ,IAAA,SAAA4B,QAAA,CAAOV,QAAQ,CAACP,IAAI,CAAO,CAAC,GAXvBO,QAAQ,CAACR,EAYD,CAChB,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENV,IAAA,QAAK2B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D1B,KAAA,QAAKyB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE9C5B,IAAA,QAAK2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC1B,KAAA,QAAKyB,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/D5B,IAAA,OAAI2B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAGrE1B,KAAA,QAAKyB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5B,IAAA,OAAI2B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC5D5B,IAAA,QAAK2B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBf,SAAS,CAACqB,GAAG,CAACb,QAAQ,eACrBrB,IAAA,WAEEoD,OAAO,CAAEA,CAAA,GAAM7C,mBAAmB,CAACc,QAAQ,CAACX,EAAE,CAAE,CAChDiB,SAAS,4DAAA0B,MAAA,CACP/C,gBAAgB,GAAKe,QAAQ,CAACX,EAAE,CAC5B,2BAA2B,CAC3B,iCAAiC,CACpC,CAAAkB,QAAA,CAEFP,QAAQ,CAACV,IAAI,EARTU,QAAQ,CAACX,EASR,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNR,KAAA,QAAKyB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC1B,KAAA,QAAKyB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC5B,IAAA,CAACJ,qBAAqB,EAAC+B,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAChE3B,IAAA,OAAI2B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,EAC5D,CAAC,cACN1B,KAAA,OAAIyB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC7C5B,IAAA,OAAA4B,QAAA,CAAI,+BAAwB,CAAI,CAAC,cACjC5B,IAAA,OAAA4B,QAAA,CAAI,6BAAsB,CAAI,CAAC,cAC/B5B,IAAA,OAAA4B,QAAA,CAAI,6BAAsB,CAAI,CAAC,cAC/B5B,IAAA,OAAA4B,QAAA,CAAI,0BAAmB,CAAI,CAAC,EAC1B,CAAC,EACF,CAAC,EACH,CAAC,CACH,CAAC,cAGN1B,KAAA,QAAKyB,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB5B,IAAA,QAAK2B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB1B,KAAA,MAAGyB,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,UACnB,CAACd,gBAAgB,CAACwC,MAAM,CAAC,mBACnC,EAAG,CAAC,CACD,CAAC,cAENtD,IAAA,QAAK2B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEd,gBAAgB,CAACoB,GAAG,CAAC,CAAClB,OAAO,CAAEuC,KAAK,gBACnCvD,IAAA,CAAChB,MAAM,CAACwC,GAAG,EAETyB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAExB,CAAC,CAAE,EAAG,CAAE,CAC/ByB,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAExB,CAAC,CAAE,CAAE,CAAE,CAC9B8B,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAEH,KAAK,CAAG,GAAI,CAAE,CAAA3B,QAAA,cAElD5B,IAAA,CAACsB,kBAAkB,EAACN,OAAO,CAAEA,OAAQ,CAAE,CAAC,EALnCA,OAAO,CAACN,EAMH,CACb,CAAC,CACC,CAAC,CAELI,gBAAgB,CAACwC,MAAM,GAAK,CAAC,eAC5BpD,KAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC5B,IAAA,QAAK2B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjC5B,IAAA,CAACR,mBAAmB,EAACmC,SAAS,CAAC,mBAAmB,CAAE,CAAC,CAClD,CAAC,cACN3B,IAAA,OAAI2B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACvF5B,IAAA,MAAG2B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAGN5B,IAAA,QAAK2B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjE1B,KAAA,QAAKyB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjE5B,IAAA,OAAI2B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,qBAEnD,CAAI,CAAC,cACL5B,IAAA,MAAG2B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qEAE1C,CAAG,CAAC,cACJ5B,IAAA,CAACf,IAAI,EACH0E,EAAE,CAAC,UAAU,CACbhC,SAAS,CAAC,uHAAuH,CAAAC,QAAA,CAClI,iBAED,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}