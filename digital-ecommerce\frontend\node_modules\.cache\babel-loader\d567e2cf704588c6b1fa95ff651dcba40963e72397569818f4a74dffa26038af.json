{"ast": null, "code": "function calculateRepeatDuration(duration, repeat, _repeatDelay) {\n  return duration * (repeat + 1);\n}\nexport { calculateRepeatDuration };", "map": {"version": 3, "names": ["calculateRepeatDuration", "duration", "repeat", "_repeatDelay"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/animation/sequence/utils/calc-repeat-duration.mjs"], "sourcesContent": ["function calculateRepeatDuration(duration, repeat, _repeatDelay) {\n    return duration * (repeat + 1);\n}\n\nexport { calculateRepeatDuration };\n"], "mappings": "AAAA,SAASA,uBAAuBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAE;EAC7D,OAAOF,QAAQ,IAAIC,MAAM,GAAG,CAAC,CAAC;AAClC;AAEA,SAASF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}