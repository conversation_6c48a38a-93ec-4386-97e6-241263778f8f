{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { clamp } from 'motion-utils';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\nconst clampRgbUnit = v => clamp(0, 255, v);\nconst rgbUnit = _objectSpread(_objectSpread({}, number), {}, {\n  transform: v => Math.round(clampRgbUnit(v))\n});\nconst rgba = {\n  test: /*@__PURE__*/isColorString(\"rgb\", \"red\"),\n  parse: /*@__PURE__*/splitColor(\"red\", \"green\", \"blue\"),\n  transform: _ref => {\n    let {\n      red,\n      green,\n      blue,\n      alpha: alpha$1 = 1\n    } = _ref;\n    return \"rgba(\" + rgbUnit.transform(red) + \", \" + rgbUnit.transform(green) + \", \" + rgbUnit.transform(blue) + \", \" + sanitize(alpha.transform(alpha$1)) + \")\";\n  }\n};\nexport { rgbUnit, rgba };", "map": {"version": 3, "names": ["clamp", "number", "alpha", "sanitize", "isColorString", "splitColor", "clampRgbUnit", "v", "rgbUnit", "_objectSpread", "transform", "Math", "round", "rgba", "test", "parse", "_ref", "red", "green", "blue", "alpha$1"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/value/types/color/rgba.mjs"], "sourcesContent": ["import { clamp } from 'motion-utils';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = (v) => clamp(0, 255, v);\nconst rgbUnit = {\n    ...number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: /*@__PURE__*/ isColorString(\"rgb\", \"red\"),\n    parse: /*@__PURE__*/ splitColor(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        sanitize(alpha.transform(alpha$1)) +\n        \")\",\n};\n\nexport { rgbUnit, rgba };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,cAAc;AACpC,SAASC,MAAM,EAAEC,KAAK,QAAQ,sBAAsB;AACpD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,aAAa,EAAEC,UAAU,QAAQ,aAAa;AAEvD,MAAMC,YAAY,GAAIC,CAAC,IAAKP,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEO,CAAC,CAAC;AAC5C,MAAMC,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACNR,MAAM;EACTS,SAAS,EAAGH,CAAC,IAAKI,IAAI,CAACC,KAAK,CAACN,YAAY,CAACC,CAAC,CAAC;AAAC,EAChD;AACD,MAAMM,IAAI,GAAG;EACTC,IAAI,EAAE,aAAcV,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC;EAC/CW,KAAK,EAAE,aAAcV,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;EACvDK,SAAS,EAAEM,IAAA;IAAA,IAAC;MAAEC,GAAG;MAAEC,KAAK;MAAEC,IAAI;MAAEjB,KAAK,EAAEkB,OAAO,GAAG;IAAE,CAAC,GAAAJ,IAAA;IAAA,OAAK,OAAO,GAC5DR,OAAO,CAACE,SAAS,CAACO,GAAG,CAAC,GACtB,IAAI,GACJT,OAAO,CAACE,SAAS,CAACQ,KAAK,CAAC,GACxB,IAAI,GACJV,OAAO,CAACE,SAAS,CAACS,IAAI,CAAC,GACvB,IAAI,GACJhB,QAAQ,CAACD,KAAK,CAACQ,SAAS,CAACU,OAAO,CAAC,CAAC,GAClC,GAAG;EAAA;AACX,CAAC;AAED,SAASZ,OAAO,EAAEK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}