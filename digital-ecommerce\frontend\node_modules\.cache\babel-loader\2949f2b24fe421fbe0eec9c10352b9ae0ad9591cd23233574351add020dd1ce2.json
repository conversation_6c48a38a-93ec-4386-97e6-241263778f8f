{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"children\",\"variant\",\"size\",\"disabled\",\"loading\",\"onClick\",\"className\",\"icon\",\"iconPosition\",\"fullWidth\"];import React from'react';import{motion}from'framer-motion';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Button=_ref=>{let{children,variant='primary',size='medium',disabled=false,loading=false,onClick,className='',icon:Icon,iconPosition='left',fullWidth=false}=_ref,props=_objectWithoutProperties(_ref,_excluded);const baseClasses='inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';const variants={primary:'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700 focus:ring-light-orange-300 shadow-md hover:shadow-lg',secondary:'bg-white text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-50 focus:ring-light-orange-300 shadow-md hover:shadow-lg',outline:'bg-transparent text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-500 hover:text-white focus:ring-light-orange-300',ghost:'bg-transparent text-light-orange-600 hover:bg-light-orange-100 focus:ring-light-orange-300',danger:'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-300 shadow-md hover:shadow-lg',success:'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-300 shadow-md hover:shadow-lg',digital:'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-md hover:shadow-lg'};const sizes={small:'px-3 py-2 text-sm',medium:'px-4 py-3 text-base',large:'px-6 py-4 text-lg'};const disabledClasses='opacity-50 cursor-not-allowed pointer-events-none';const fullWidthClass=fullWidth?'w-full':'';const buttonClasses=\"\\n    \".concat(baseClasses,\"\\n    \").concat(variants[variant],\"\\n    \").concat(sizes[size],\"\\n    \").concat(disabled?disabledClasses:'',\"\\n    \").concat(fullWidthClass,\"\\n    \").concat(className,\"\\n  \").trim().replace(/\\s+/g,' ');const handleClick=e=>{if(!disabled&&!loading&&onClick){onClick(e);}};const renderIcon=()=>{if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\"});}if(Icon){return/*#__PURE__*/_jsx(Icon,{className:\"w-5 h-5 \".concat(iconPosition==='right'?'ml-2':'mr-2')});}return null;};return/*#__PURE__*/_jsxs(motion.button,_objectSpread(_objectSpread({whileHover:!disabled?{scale:1.02}:{},whileTap:!disabled?{scale:0.98}:{},className:buttonClasses,onClick:handleClick,disabled:disabled||loading},props),{},{children:[iconPosition==='left'&&renderIcon(),loading?'Loading...':children,iconPosition==='right'&&renderIcon()]}));};export default Button;", "map": {"version": 3, "names": ["React", "motion", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "_ref", "children", "variant", "size", "disabled", "loading", "onClick", "className", "icon", "Icon", "iconPosition", "fullWidth", "props", "_objectWithoutProperties", "_excluded", "baseClasses", "variants", "primary", "secondary", "outline", "ghost", "danger", "success", "digital", "sizes", "small", "medium", "large", "disabledClasses", "fullWidthClass", "buttonClasses", "concat", "trim", "replace", "handleClick", "e", "renderIcon", "button", "_objectSpread", "whileHover", "scale", "whileTap"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Button.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  onClick,\n  className = '',\n  icon: Icon,\n  iconPosition = 'left',\n  fullWidth = false,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    secondary: 'bg-white text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-50 focus:ring-light-orange-300 shadow-md hover:shadow-lg',\n    outline: 'bg-transparent text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-500 hover:text-white focus:ring-light-orange-300',\n    ghost: 'bg-transparent text-light-orange-600 hover:bg-light-orange-100 focus:ring-light-orange-300',\n    danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-300 shadow-md hover:shadow-lg',\n    success: 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-300 shadow-md hover:shadow-lg',\n    digital: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-md hover:shadow-lg'\n  };\n\n  const sizes = {\n    small: 'px-3 py-2 text-sm',\n    medium: 'px-4 py-3 text-base',\n    large: 'px-6 py-4 text-lg'\n  };\n\n  const disabledClasses = 'opacity-50 cursor-not-allowed pointer-events-none';\n  const fullWidthClass = fullWidth ? 'w-full' : '';\n\n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant]}\n    ${sizes[size]}\n    ${disabled ? disabledClasses : ''}\n    ${fullWidthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  const handleClick = (e) => {\n    if (!disabled && !loading && onClick) {\n      onClick(e);\n    }\n  };\n\n  const renderIcon = () => {\n    if (loading) {\n      return (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\" />\n      );\n    }\n    \n    if (Icon) {\n      return (\n        <Icon className={`w-5 h-5 ${iconPosition === 'right' ? 'ml-2' : 'mr-2'}`} />\n      );\n    }\n    \n    return null;\n  };\n\n  return (\n    <motion.button\n      whileHover={!disabled ? { scale: 1.02 } : {}}\n      whileTap={!disabled ? { scale: 0.98 } : {}}\n      className={buttonClasses}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {iconPosition === 'left' && renderIcon()}\n      {loading ? 'Loading...' : children}\n      {iconPosition === 'right' && renderIcon()}\n    </motion.button>\n  );\n};\n\nexport default Button;\n"], "mappings": "wcAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAYT,IAZU,CACdC,QAAQ,CACRC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,QAAQ,CACfC,QAAQ,CAAG,KAAK,CAChBC,OAAO,CAAG,KAAK,CACfC,OAAO,CACPC,SAAS,CAAG,EAAE,CACdC,IAAI,CAAEC,IAAI,CACVC,YAAY,CAAG,MAAM,CACrBC,SAAS,CAAG,KAEd,CAAC,CAAAX,IAAA,CADIY,KAAK,CAAAC,wBAAA,CAAAb,IAAA,CAAAc,SAAA,EAER,KAAM,CAAAC,WAAW,CAAG,kJAAkJ,CAEtK,KAAM,CAAAC,QAAQ,CAAG,CACfC,OAAO,CAAE,mLAAmL,CAC5LC,SAAS,CAAE,gJAAgJ,CAC3JC,OAAO,CAAE,8IAA8I,CACvJC,KAAK,CAAE,4FAA4F,CACnGC,MAAM,CAAE,sIAAsI,CAC9IC,OAAO,CAAE,gJAAgJ,CACzJC,OAAO,CAAE,2IACX,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZC,KAAK,CAAE,mBAAmB,CAC1BC,MAAM,CAAE,qBAAqB,CAC7BC,KAAK,CAAE,mBACT,CAAC,CAED,KAAM,CAAAC,eAAe,CAAG,mDAAmD,CAC3E,KAAM,CAAAC,cAAc,CAAGlB,SAAS,CAAG,QAAQ,CAAG,EAAE,CAEhD,KAAM,CAAAmB,aAAa,CAAG,SAAAC,MAAA,CAClBhB,WAAW,WAAAgB,MAAA,CACXf,QAAQ,CAACd,OAAO,CAAC,WAAA6B,MAAA,CACjBP,KAAK,CAACrB,IAAI,CAAC,WAAA4B,MAAA,CACX3B,QAAQ,CAAGwB,eAAe,CAAG,EAAE,WAAAG,MAAA,CAC/BF,cAAc,WAAAE,MAAA,CACdxB,SAAS,SACXyB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CAE7B,KAAM,CAAAC,WAAW,CAAIC,CAAC,EAAK,CACzB,GAAI,CAAC/B,QAAQ,EAAI,CAACC,OAAO,EAAIC,OAAO,CAAE,CACpCA,OAAO,CAAC6B,CAAC,CAAC,CACZ,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI/B,OAAO,CAAE,CACX,mBACET,IAAA,QAAKW,SAAS,CAAC,kEAAkE,CAAE,CAAC,CAExF,CAEA,GAAIE,IAAI,CAAE,CACR,mBACEb,IAAA,CAACa,IAAI,EAACF,SAAS,YAAAwB,MAAA,CAAarB,YAAY,GAAK,OAAO,CAAG,MAAM,CAAG,MAAM,CAAG,CAAE,CAAC,CAEhF,CAEA,MAAO,KAAI,CACb,CAAC,CAED,mBACEZ,KAAA,CAACJ,MAAM,CAAC2C,MAAM,CAAAC,aAAA,CAAAA,aAAA,EACZC,UAAU,CAAE,CAACnC,QAAQ,CAAG,CAAEoC,KAAK,CAAE,IAAK,CAAC,CAAG,CAAC,CAAE,CAC7CC,QAAQ,CAAE,CAACrC,QAAQ,CAAG,CAAEoC,KAAK,CAAE,IAAK,CAAC,CAAG,CAAC,CAAE,CAC3CjC,SAAS,CAAEuB,aAAc,CACzBxB,OAAO,CAAE4B,WAAY,CACrB9B,QAAQ,CAAEA,QAAQ,EAAIC,OAAQ,EAC1BO,KAAK,MAAAX,QAAA,EAERS,YAAY,GAAK,MAAM,EAAI0B,UAAU,CAAC,CAAC,CACvC/B,OAAO,CAAG,YAAY,CAAGJ,QAAQ,CACjCS,YAAY,GAAK,OAAO,EAAI0B,UAAU,CAAC,CAAC,GAC5B,CAAC,CAEpB,CAAC,CAED,cAAe,CAAArC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}