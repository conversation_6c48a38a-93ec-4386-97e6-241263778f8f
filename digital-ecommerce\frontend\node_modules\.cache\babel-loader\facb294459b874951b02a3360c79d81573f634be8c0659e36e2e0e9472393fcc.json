{"ast": null, "code": "import React,{useState,useMemo}from'react';import{motion,AnimatePresence}from'framer-motion';import{useSearchParams,Link}from'react-router-dom';import{FunnelIcon,Squares2X2Icon,ListBulletIcon,StarIcon,HeartIcon,ShoppingBagIcon,AdjustmentsHorizontalIcon,ChevronRightIcon,HomeIcon,// TagIcon,\nClockIcon,TruckIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid}from'@heroicons/react/24/solid';import{categories,products,getProductsByCategory}from'../data/products';import{useCart}from'../components/ShoppingCart';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProductsPage=()=>{var _subcategories$find;const[searchParams,setSearchParams]=useSearchParams();const[viewMode,setViewMode]=useState('grid');const[sortBy,setSortBy]=useState('featured');const[selectedCategory,setSelectedCategory]=useState(searchParams.get('category')||'all');const[selectedSubcategory,setSelectedSubcategory]=useState(searchParams.get('subcategory')||'all');const[productType,setProductType]=useState('all');// all, physical, digital\nconst[priceRange,setPriceRange]=useState([0,1000]);const[selectedRating,setSelectedRating]=useState(0);const[showFilters,setShowFilters]=useState(false);const{addToCart}=useCart();const handleAddToCart=product=>{addToCart(product);toast.success(\"\".concat(product.name,\" added to cart!\"),{duration:3000,position:'top-right'});};// Get current category data\nconst currentCategory=categories.find(cat=>cat.id===selectedCategory);const subcategories=currentCategory?[{id:'all',name:'All '+currentCategory.name,count:getProductsByCategory(selectedCategory).length},...currentCategory.subcategories.map(sub=>({id:sub,name:sub.split('-').map(word=>word.charAt(0).toUpperCase()+word.slice(1)).join(' '),count:products.filter(p=>p.subcategory===sub).length}))]:[];const productTypeOptions=[{id:'all',name:'All Products',count:products.length},{id:'physical',name:'Physical Products',count:products.filter(p=>p.type==='physical').length},{id:'digital',name:'Digital Products',count:products.filter(p=>p.type==='digital').length}];const sortOptions=[{value:'featured',label:'Featured'},{value:'price-low',label:'Price: Low to High'},{value:'price-high',label:'Price: High to Low'},{value:'rating',label:'Highest Rated'},{value:'newest',label:'Newest First'},{value:'name',label:'Name: A to Z'},{value:'popularity',label:'Most Popular'}];const filteredAndSortedProducts=useMemo(()=>{let filtered=products.filter(product=>{const categoryMatch=selectedCategory==='all'||product.category===selectedCategory;const subcategoryMatch=selectedSubcategory==='all'||product.subcategory===selectedSubcategory;const typeMatch=productType==='all'||product.type===productType;const priceMatch=product.price>=priceRange[0]&&product.price<=priceRange[1];const ratingMatch=selectedRating===0||product.rating>=selectedRating;return categoryMatch&&subcategoryMatch&&typeMatch&&priceMatch&&ratingMatch;});// Sort products\nswitch(sortBy){case'price-low':filtered.sort((a,b)=>a.price-b.price);break;case'price-high':filtered.sort((a,b)=>b.price-a.price);break;case'rating':filtered.sort((a,b)=>b.rating-a.rating);break;case'newest':filtered.sort((a,b)=>b.id.localeCompare(a.id));break;case'name':filtered.sort((a,b)=>a.name.localeCompare(b.name));break;default:// Featured - keep original order\nbreak;}return filtered;},[selectedCategory,selectedSubcategory,productType,priceRange,selectedRating,sortBy]);const ProductCard=_ref=>{var _product$shipping;let{product,index}=_ref;return/*#__PURE__*/_jsxs(motion.div,{layout:true,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3,delay:index*0.05},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 \".concat(viewMode==='list'?'flex':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative \".concat(viewMode==='list'?'w-48 flex-shrink-0':''),children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:product.image,alt:product.name,className:\"w-full object-cover group-hover:scale-105 transition-transform duration-300 \".concat(viewMode==='list'?'h-48':'h-64')}),product.badge&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-1 rounded-full text-sm font-semibold text-white \".concat(product.type==='digital'?'bg-blue-500':'bg-light-orange-500'),children:product.badge})}),product.type==='digital'&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4 mt-8\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",children:\"Digital\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},className:\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",children:/*#__PURE__*/_jsx(HeartIcon,{className:\"w-5 h-5 text-gray-600\"})})}),!product.inStock&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-semibold\",children:\"Out of Stock\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 \".concat(viewMode==='list'?'flex-1 flex flex-col justify-between':''),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900 mb-2 \".concat(viewMode==='list'?'text-xl':'text-lg'),children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\")\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-3\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),product.type==='digital'?/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 text-green-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-green-600 font-medium\",children:\"Instant Delivery\"})]}),product.platforms&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Platforms:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-800\",children:product.platforms.join(', ')})]})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[product.colors&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Colors:\"}),product.colors.slice(0,3).map((color,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer \".concat(color==='black'||color==='Black'?'bg-black':color==='white'||color==='White'?'bg-white':color==='blue'||color==='Blue'?'bg-blue-500':color==='red'||color==='Red'?'bg-red-500':color==='silver'||color==='Silver'?'bg-gray-400':color==='gold'||color==='Gold'?'bg-yellow-400':'bg-gray-300')},index)),product.colors.length>3&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500\",children:[\"+\",product.colors.length-3]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(TruckIcon,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-blue-600\",children:(_product$shipping=product.shipping)!==null&&_product$shipping!==void 0&&_product$shipping.free?'Free Shipping':'Shipping Available'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-4 h-4 \".concat(product.inStock?'text-green-600':'text-red-600')}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm \".concat(product.inStock?'text-green-600':'text-red-600'),children:[product.inStock?'In Stock':'Out of Stock',product.stockCount&&product.inStock&&\" (\".concat(product.stockCount,\" available)\")]})]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>handleAddToCart(product),disabled:!product.inStock,className:\"w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 \".concat(product.inStock?'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700':'bg-gray-300 text-gray-500 cursor-not-allowed'),children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:product.inStock?'Add to Cart':'Out of Stock'})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",children:/*#__PURE__*/_jsxs(\"nav\",{className:\"flex items-center space-x-2 text-sm\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\",children:[/*#__PURE__*/_jsx(HomeIcon,{className:\"w-4 h-4 mr-1\"}),\"Home\"]}),/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Products\"}),selectedCategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:currentCategory===null||currentCategory===void 0?void 0:currentCategory.name})]}),selectedSubcategory!=='all'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChevronRightIcon,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-600 font-medium\",children:(_subcategories$find=subcategories.find(sub=>sub.id===selectedSubcategory))===null||_subcategories$find===void 0?void 0:_subcategories$find.name})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl lg:text-5xl font-bold text-white mb-4\",children:selectedCategory==='all'?'All Products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.name)||'Products'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-light-orange-100 max-w-2xl mx-auto\",children:selectedCategory==='all'?'Discover our amazing collection of premium products':(currentCategory===null||currentCategory===void 0?void 0:currentCategory.description)||'Explore our curated selection'})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-4 justify-center\",children:[{id:'all',name:'All Products',icon:'🛍️'},...categories].map(category=>/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>{setSelectedCategory(category.id);setSelectedSubcategory('all');setSearchParams({category:category.id});},className:\"flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all \".concat(selectedCategory===category.id?'bg-light-orange-500 text-white shadow-lg':'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:category.icon}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]},category.id))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:w-64 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Filters\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowFilters(!showFilters),className:\"lg:hidden p-2 text-gray-600\",children:/*#__PURE__*/_jsx(AdjustmentsHorizontalIcon,{className:\"w-5 h-5\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6 \".concat(showFilters?'block':'hidden lg:block'),children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Product Type\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:productTypeOptions.map(type=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setProductType(type.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(productType===type.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:type.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",type.count,\")\"]})]})},type.id))})]}),selectedCategory!=='all'&&subcategories.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[currentCategory===null||currentCategory===void 0?void 0:currentCategory.name,\" Categories\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:subcategories.map(subcategory=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedSubcategory(subcategory.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(selectedSubcategory===subcategory.id?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{children:subcategory.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm\",children:[\"(\",subcategory.count,\")\"]})]})},subcategory.id))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:[\"Price Range: $\",priceRange[0],\" - $\",priceRange[1]]}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"0\",max:\"1000\",value:priceRange[1],onChange:e=>setPriceRange([priceRange[0],parseInt(e.target.value)]),className:\"w-full\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Minimum Rating\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:[4,3,2,1,0].map(rating=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setSelectedRating(rating),className:\"flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors \".concat(selectedRating===rating?'bg-light-orange-100 text-light-orange-700':'text-gray-600 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 \".concat(i<rating?'text-yellow-400':'text-gray-300')},i))}),/*#__PURE__*/_jsx(\"span\",{children:rating>0?\"\".concat(rating,\"+ Stars\"):'All Ratings'})]},rating))})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Showing \",filteredAndSortedProducts.length,\" of \",products.length,\" products\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"select\",{value:sortBy,onChange:e=>setSortBy(e.target.value),className:\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\",children:sortOptions.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex bg-gray-100 rounded-lg p-1\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('grid'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='grid'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(Squares2X2Icon,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('list'),className:\"p-2 rounded-md transition-colors \".concat(viewMode==='list'?'bg-white text-light-orange-600 shadow-sm':'text-gray-600'),children:/*#__PURE__*/_jsx(ListBulletIcon,{className:\"w-5 h-5\"})})]})]})]})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"\".concat(viewMode==='grid'?'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8':'space-y-6'),children:filteredAndSortedProducts.map((product,index)=>/*#__PURE__*/_jsx(ProductCard,{product:product,index:index},product.id))},\"\".concat(viewMode,\"-\").concat(selectedCategory,\"-\").concat(sortBy))}),filteredAndSortedProducts.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-4\",children:/*#__PURE__*/_jsx(FunnelIcon,{className:\"w-16 h-16 mx-auto\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:\"No products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Try adjusting your filters to see more results.\"})]})]})]})})]});};export default ProductsPage;", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "useSearchParams", "Link", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "StarIcon", "HeartIcon", "ShoppingBagIcon", "AdjustmentsHorizontalIcon", "ChevronRightIcon", "HomeIcon", "ClockIcon", "TruckIcon", "CheckCircleIcon", "StarIconSolid", "categories", "products", "getProductsByCategory", "useCart", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProductsPage", "_subcategories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "sortBy", "setSortBy", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "get", "selectedSubcategory", "setSelectedSubcategory", "productType", "setProductType", "priceRange", "setPriceRange", "selectedRating", "setSelectedRating", "showFilters", "setShowFilters", "addToCart", "handleAddToCart", "product", "success", "concat", "name", "duration", "position", "currentCategory", "find", "cat", "id", "subcategories", "count", "length", "map", "sub", "split", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "filter", "p", "subcategory", "productTypeOptions", "type", "sortOptions", "value", "label", "filteredAndSortedProducts", "filtered", "categoryMatch", "category", "subcategoryMatch", "typeMatch", "priceMatch", "price", "ratingMatch", "rating", "sort", "a", "b", "localeCompare", "ProductCard", "_ref", "_product$shipping", "index", "div", "layout", "initial", "opacity", "y", "animate", "exit", "transition", "delay", "className", "children", "src", "images", "image", "alt", "badge", "button", "whileHover", "scale", "whileTap", "inStock", "Array", "_", "i", "Math", "floor", "reviews", "originalPrice", "platforms", "colors", "color", "shipping", "free", "stockCount", "onClick", "disabled", "to", "description", "icon", "min", "max", "onChange", "e", "parseInt", "target", "option", "mode"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport {\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon,\n  StarIcon,\n  HeartIcon,\n  ShoppingBagIcon,\n  AdjustmentsHorizontalIcon,\n  ChevronRightIcon,\n  HomeIcon,\n  // TagIcon,\n  ClockIcon,\n  TruckIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { categories, products, getProductsByCategory } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst ProductsPage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortBy, setSortBy] = useState('featured');\n  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');\n  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');\n  const [productType, setProductType] = useState('all'); // all, physical, digital\n  const [priceRange, setPriceRange] = useState([0, 1000]);\n  const [selectedRating, setSelectedRating] = useState(0);\n  const [showFilters, setShowFilters] = useState(false);\n  const { addToCart } = useCart();\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n\n  // Get current category data\n  const currentCategory = categories.find(cat => cat.id === selectedCategory);\n  const subcategories = currentCategory ? [\n    { id: 'all', name: 'All ' + currentCategory.name, count: getProductsByCategory(selectedCategory).length },\n    ...currentCategory.subcategories.map(sub => ({\n      id: sub,\n      name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),\n      count: products.filter(p => p.subcategory === sub).length\n    }))\n  ] : [];\n\n  const productTypeOptions = [\n    { id: 'all', name: 'All Products', count: products.length },\n    { id: 'physical', name: 'Physical Products', count: products.filter(p => p.type === 'physical').length },\n    { id: 'digital', name: 'Digital Products', count: products.filter(p => p.type === 'digital').length }\n  ];\n\n  const sortOptions = [\n    { value: 'featured', label: 'Featured' },\n    { value: 'price-low', label: 'Price: Low to High' },\n    { value: 'price-high', label: 'Price: High to Low' },\n    { value: 'rating', label: 'Highest Rated' },\n    { value: 'newest', label: 'Newest First' },\n    { value: 'name', label: 'Name: A to Z' },\n    { value: 'popularity', label: 'Most Popular' }\n  ];\n\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;\n      const typeMatch = productType === 'all' || product.type === productType;\n      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];\n      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;\n\n      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'newest':\n        filtered.sort((a, b) => b.id.localeCompare(a.id));\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n\n    return filtered;\n  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);\n\n  const ProductCard = ({ product, index }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3, delay: index * 0.05 }}\n      className={`bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 ${\n        viewMode === 'list' ? 'flex' : ''\n      }`}\n    >\n      <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n        <img\n          src={product.images ? product.images[0] : product.image}\n          alt={product.name}\n          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${\n            viewMode === 'list' ? 'h-48' : 'h-64'\n          }`}\n        />\n        {product.badge && (\n          <div className=\"absolute top-4 left-4\">\n            <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${\n              product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'\n            }`}>\n              {product.badge}\n            </span>\n          </div>\n        )}\n        {product.type === 'digital' && (\n          <div className=\"absolute top-4 left-4 mt-8\">\n            <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n              Digital\n            </span>\n          </div>\n        )}\n        <div className=\"absolute top-4 right-4\">\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n          >\n            <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n          </motion.button>\n        </div>\n        {!product.inStock && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-semibold\">Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>\n        <div>\n          <h3 className={`font-semibold text-gray-900 mb-2 ${\n            viewMode === 'list' ? 'text-xl' : 'text-lg'\n          }`}>\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex\">\n              {[...Array(5)].map((_, i) => (\n                i < Math.floor(product.rating) ? (\n                  <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                ) : (\n                  <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                )\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-600 ml-2\">\n              {product.rating} ({product.reviews})\n            </span>\n          </div>\n\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <span className=\"text-2xl font-bold text-light-orange-600\">\n              ${product.price}\n            </span>\n            {product.originalPrice && product.originalPrice > product.price && (\n              <span className=\"text-lg text-gray-500 line-through\">\n                ${product.originalPrice}\n              </span>\n            )}\n          </div>\n\n          {/* Product Type Specific Info */}\n          {product.type === 'digital' ? (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <ClockIcon className=\"w-4 h-4 text-green-600\" />\n                <span className=\"text-sm text-green-600 font-medium\">Instant Delivery</span>\n              </div>\n              {product.platforms && (\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-600\">Platforms:</span>\n                  <span className=\"text-sm text-gray-800\">{product.platforms.join(', ')}</span>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Color Options for Physical Products */}\n              {product.colors && (\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <span className=\"text-sm text-gray-600\">Colors:</span>\n                  {product.colors.slice(0, 3).map((color, index) => (\n                    <div\n                      key={index}\n                      className={`w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${\n                        color === 'black' || color === 'Black' ? 'bg-black' :\n                        color === 'white' || color === 'White' ? 'bg-white' :\n                        color === 'blue' || color === 'Blue' ? 'bg-blue-500' :\n                        color === 'red' || color === 'Red' ? 'bg-red-500' :\n                        color === 'silver' || color === 'Silver' ? 'bg-gray-400' :\n                        color === 'gold' || color === 'Gold' ? 'bg-yellow-400' :\n                        'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                  {product.colors.length > 3 && (\n                    <span className=\"text-sm text-gray-500\">+{product.colors.length - 3}</span>\n                  )}\n                </div>\n              )}\n              {/* Shipping Info */}\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TruckIcon className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm text-blue-600\">\n                  {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}\n                </span>\n              </div>\n            </>\n          )}\n\n          {/* Stock Status */}\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <CheckCircleIcon className={`w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`} />\n            <span className={`text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>\n              {product.inStock ? 'In Stock' : 'Out of Stock'}\n              {product.stockCount && product.inStock && ` (${product.stockCount} available)`}\n            </span>\n          </div>\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={() => handleAddToCart(product)}\n          disabled={!product.inStock}\n          className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${\n            product.inStock\n              ? 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700'\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n          }`}\n        >\n          <ShoppingBagIcon className=\"w-5 h-5\" />\n          <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      {/* Breadcrumb */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex items-center space-x-2 text-sm\">\n            <Link to=\"/\" className=\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\">\n              <HomeIcon className=\"w-4 h-4 mr-1\" />\n              Home\n            </Link>\n            <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n            <span className=\"text-gray-600\">Products</span>\n            {selectedCategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {currentCategory?.name}\n                </span>\n              </>\n            )}\n            {selectedSubcategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {subcategories.find(sub => sub.id === selectedSubcategory)?.name}\n                </span>\n              </>\n            )}\n          </nav>\n        </div>\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\n              {selectedCategory === 'all' ? 'All Products' : currentCategory?.name || 'Products'}\n            </h1>\n            <p className=\"text-xl text-light-orange-100 max-w-2xl mx-auto\">\n              {selectedCategory === 'all'\n                ? 'Discover our amazing collection of premium products'\n                : currentCategory?.description || 'Explore our curated selection'\n              }\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {[{ id: 'all', name: 'All Products', icon: '🛍️' }, ...categories].map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => {\n                  setSelectedCategory(category.id);\n                  setSelectedSubcategory('all');\n                  setSearchParams({ category: category.id });\n                }}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-light-orange-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar Filters */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n                <button\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"lg:hidden p-2 text-gray-600\"\n                >\n                  <AdjustmentsHorizontalIcon className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                {/* Product Type */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Product Type</h4>\n                  <div className=\"space-y-2\">\n                    {productTypeOptions.map(type => (\n                      <button\n                        key={type.id}\n                        onClick={() => setProductType(type.id)}\n                        className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                          productType === type.id\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex justify-between\">\n                          <span>{type.name}</span>\n                          <span className=\"text-sm\">({type.count})</span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Subcategories */}\n                {selectedCategory !== 'all' && subcategories.length > 0 && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">\n                      {currentCategory?.name} Categories\n                    </h4>\n                    <div className=\"space-y-2\">\n                      {subcategories.map(subcategory => (\n                        <button\n                          key={subcategory.id}\n                          onClick={() => setSelectedSubcategory(subcategory.id)}\n                          className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                            selectedSubcategory === subcategory.id\n                              ? 'bg-light-orange-100 text-light-orange-700'\n                              : 'text-gray-600 hover:bg-gray-100'\n                          }`}\n                        >\n                          <div className=\"flex justify-between\">\n                            <span>{subcategory.name}</span>\n                            <span className=\"text-sm\">({subcategory.count})</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Price Range */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">\n                    Price Range: ${priceRange[0]} - ${priceRange[1]}\n                  </h4>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1000\"\n                    value={priceRange[1]}\n                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}\n                    className=\"w-full\"\n                  />\n                </div>\n\n                {/* Rating Filter */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Minimum Rating</h4>\n                  <div className=\"space-y-2\">\n                    {[4, 3, 2, 1, 0].map(rating => (\n                      <button\n                        key={rating}\n                        onClick={() => setSelectedRating(rating)}\n                        className={`flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${\n                          selectedRating === rating\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex\">\n                          {[...Array(5)].map((_, i) => (\n                            <StarIconSolid\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < rating ? 'text-yellow-400' : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                        <span>{rating > 0 ? `${rating}+ Stars` : 'All Ratings'}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {/* Toolbar */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8\">\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n                <div>\n                  <p className=\"text-gray-600\">\n                    Showing {filteredAndSortedProducts.length} of {products.length} products\n                  </p>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  {/* Sort Dropdown */}\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\"\n                  >\n                    {sortOptions.map(option => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n\n                  {/* View Mode Toggle */}\n                  <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                    <button\n                      onClick={() => setViewMode('grid')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'grid'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <Squares2X2Icon className=\"w-5 h-5\" />\n                    </button>\n                    <button\n                      onClick={() => setViewMode('list')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'list'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <ListBulletIcon className=\"w-5 h-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Products Grid */}\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={`${viewMode}-${selectedCategory}-${sortBy}`}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className={`${\n                  viewMode === 'grid'\n                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8'\n                    : 'space-y-6'\n                }`}\n              >\n                {filteredAndSortedProducts.map((product, index) => (\n                  <ProductCard key={product.id} product={product} index={index} />\n                ))}\n              </motion.div>\n            </AnimatePresence>\n\n            {filteredAndSortedProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <FunnelIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,OAAO,KAAQ,OAAO,CAChD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,eAAe,CAAEC,IAAI,KAAQ,kBAAkB,CACxD,OACEC,UAAU,CACVC,cAAc,CACdC,cAAc,CACdC,QAAQ,CACRC,SAAS,CACTC,eAAe,CACfC,yBAAyB,CACzBC,gBAAgB,CAChBC,QAAQ,CACR;AACAC,SAAS,CACTC,SAAS,CACTC,eAAe,KACV,6BAA6B,CACpC,OAASR,QAAQ,GAAI,CAAAS,aAAa,KAAQ,2BAA2B,CACrE,OAASC,UAAU,CAAEC,QAAQ,CAAEC,qBAAqB,KAAQ,kBAAkB,CAC9E,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CACzB,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG9B,eAAe,CAAC,CAAC,CACzD,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGpC,QAAQ,CAAC,MAAM,CAAC,CAChD,KAAM,CAACqC,MAAM,CAAEC,SAAS,CAAC,CAAGtC,QAAQ,CAAC,UAAU,CAAC,CAChD,KAAM,CAACuC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxC,QAAQ,CAACiC,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAI,KAAK,CAAC,CAC/F,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG3C,QAAQ,CAACiC,YAAY,CAACQ,GAAG,CAAC,aAAa,CAAC,EAAI,KAAK,CAAC,CACxG,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAAE;AACvD,KAAM,CAAC8C,UAAU,CAAEC,aAAa,CAAC,CAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAC,CACvD,KAAM,CAACgD,cAAc,CAAEC,iBAAiB,CAAC,CAAGjD,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACkD,WAAW,CAAEC,cAAc,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAEoD,SAAU,CAAC,CAAG9B,OAAO,CAAC,CAAC,CAE/B,KAAM,CAAA+B,eAAe,CAAIC,OAAO,EAAK,CACnCF,SAAS,CAACE,OAAO,CAAC,CAClB/B,KAAK,CAACgC,OAAO,IAAAC,MAAA,CAAIF,OAAO,CAACG,IAAI,oBAAmB,CAC9CC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,WACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAGzC,UAAU,CAAC0C,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACC,EAAE,GAAKxB,gBAAgB,CAAC,CAC3E,KAAM,CAAAyB,aAAa,CAAGJ,eAAe,CAAG,CACtC,CAAEG,EAAE,CAAE,KAAK,CAAEN,IAAI,CAAE,MAAM,CAAGG,eAAe,CAACH,IAAI,CAAEQ,KAAK,CAAE5C,qBAAqB,CAACkB,gBAAgB,CAAC,CAAC2B,MAAO,CAAC,CACzG,GAAGN,eAAe,CAACI,aAAa,CAACG,GAAG,CAACC,GAAG,GAAK,CAC3CL,EAAE,CAAEK,GAAG,CACPX,IAAI,CAAEW,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,GAAG,CAACG,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACxFT,KAAK,CAAE7C,QAAQ,CAACuD,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,WAAW,GAAKT,GAAG,CAAC,CAACF,MACrD,CAAC,CAAC,CAAC,CACJ,CAAG,EAAE,CAEN,KAAM,CAAAY,kBAAkB,CAAG,CACzB,CAAEf,EAAE,CAAE,KAAK,CAAEN,IAAI,CAAE,cAAc,CAAEQ,KAAK,CAAE7C,QAAQ,CAAC8C,MAAO,CAAC,CAC3D,CAAEH,EAAE,CAAE,UAAU,CAAEN,IAAI,CAAE,mBAAmB,CAAEQ,KAAK,CAAE7C,QAAQ,CAACuD,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,UAAU,CAAC,CAACb,MAAO,CAAC,CACxG,CAAEH,EAAE,CAAE,SAAS,CAAEN,IAAI,CAAE,kBAAkB,CAAEQ,KAAK,CAAE7C,QAAQ,CAACuD,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACG,IAAI,GAAK,SAAS,CAAC,CAACb,MAAO,CAAC,CACtG,CAED,KAAM,CAAAc,WAAW,CAAG,CAClB,CAAEC,KAAK,CAAE,UAAU,CAAEC,KAAK,CAAE,UAAW,CAAC,CACxC,CAAED,KAAK,CAAE,WAAW,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACnD,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACpD,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC3C,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC1C,CAAED,KAAK,CAAE,MAAM,CAAEC,KAAK,CAAE,cAAe,CAAC,CACxC,CAAED,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC/C,CAED,KAAM,CAAAC,yBAAyB,CAAGlF,OAAO,CAAC,IAAM,CAC9C,GAAI,CAAAmF,QAAQ,CAAGhE,QAAQ,CAACuD,MAAM,CAACrB,OAAO,EAAI,CACxC,KAAM,CAAA+B,aAAa,CAAG9C,gBAAgB,GAAK,KAAK,EAAIe,OAAO,CAACgC,QAAQ,GAAK/C,gBAAgB,CACzF,KAAM,CAAAgD,gBAAgB,CAAG7C,mBAAmB,GAAK,KAAK,EAAIY,OAAO,CAACuB,WAAW,GAAKnC,mBAAmB,CACrG,KAAM,CAAA8C,SAAS,CAAG5C,WAAW,GAAK,KAAK,EAAIU,OAAO,CAACyB,IAAI,GAAKnC,WAAW,CACvE,KAAM,CAAA6C,UAAU,CAAGnC,OAAO,CAACoC,KAAK,EAAI5C,UAAU,CAAC,CAAC,CAAC,EAAIQ,OAAO,CAACoC,KAAK,EAAI5C,UAAU,CAAC,CAAC,CAAC,CACnF,KAAM,CAAA6C,WAAW,CAAG3C,cAAc,GAAK,CAAC,EAAIM,OAAO,CAACsC,MAAM,EAAI5C,cAAc,CAE5E,MAAO,CAAAqC,aAAa,EAAIE,gBAAgB,EAAIC,SAAS,EAAIC,UAAU,EAAIE,WAAW,CACpF,CAAC,CAAC,CAEF;AACA,OAAQtD,MAAM,EACZ,IAAK,WAAW,CACd+C,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACJ,KAAK,CAAGK,CAAC,CAACL,KAAK,CAAC,CAC1C,MACF,IAAK,YAAY,CACfN,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAC1C,MACF,IAAK,QAAQ,CACXN,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACH,MAAM,CAAGE,CAAC,CAACF,MAAM,CAAC,CAC5C,MACF,IAAK,QAAQ,CACXR,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAChC,EAAE,CAACiC,aAAa,CAACF,CAAC,CAAC/B,EAAE,CAAC,CAAC,CACjD,MACF,IAAK,MAAM,CACTqB,QAAQ,CAACS,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACrC,IAAI,CAACuC,aAAa,CAACD,CAAC,CAACtC,IAAI,CAAC,CAAC,CACrD,MACF,QACE;AACA,MACJ,CAEA,MAAO,CAAA2B,QAAQ,CACjB,CAAC,CAAE,CAAC7C,gBAAgB,CAAEG,mBAAmB,CAAEE,WAAW,CAAEE,UAAU,CAAEE,cAAc,CAAEX,MAAM,CAAC,CAAC,CAE5F,KAAM,CAAA4D,WAAW,CAAGC,IAAA,OAAAC,iBAAA,IAAC,CAAE7C,OAAO,CAAE8C,KAAM,CAAC,CAAAF,IAAA,oBACrCtE,KAAA,CAAC1B,MAAM,CAACmG,GAAG,EACTC,MAAM,MACNC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAElD,QAAQ,CAAE,GAAG,CAAEmD,KAAK,CAAET,KAAK,CAAG,IAAK,CAAE,CACnDU,SAAS,oHAAAtD,MAAA,CACPrB,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,EAAE,CAChC,CAAA4E,QAAA,eAEHnF,KAAA,QAAKkF,SAAS,aAAAtD,MAAA,CAAcrB,QAAQ,GAAK,MAAM,CAAG,oBAAoB,CAAG,EAAE,CAAG,CAAA4E,QAAA,eAC5ErF,IAAA,QACEsF,GAAG,CAAE1D,OAAO,CAAC2D,MAAM,CAAG3D,OAAO,CAAC2D,MAAM,CAAC,CAAC,CAAC,CAAG3D,OAAO,CAAC4D,KAAM,CACxDC,GAAG,CAAE7D,OAAO,CAACG,IAAK,CAClBqD,SAAS,gFAAAtD,MAAA,CACPrB,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CACpC,CACJ,CAAC,CACDmB,OAAO,CAAC8D,KAAK,eACZ1F,IAAA,QAAKoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCrF,IAAA,SAAMoF,SAAS,4DAAAtD,MAAA,CACbF,OAAO,CAACyB,IAAI,GAAK,SAAS,CAAG,aAAa,CAAG,qBAAqB,CACjE,CAAAgC,QAAA,CACAzD,OAAO,CAAC8D,KAAK,CACV,CAAC,CACJ,CACN,CACA9D,OAAO,CAACyB,IAAI,GAAK,SAAS,eACzBrD,IAAA,QAAKoF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCrF,IAAA,SAAMoF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,CAAC,SAElF,CAAM,CAAC,CACJ,CACN,cACDrF,IAAA,QAAKoF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCrF,IAAA,CAACxB,MAAM,CAACmH,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAE7DrF,IAAA,CAAChB,SAAS,EAACoG,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAClC,CAAC,CACb,CAAC,CACL,CAACxD,OAAO,CAACmE,OAAO,eACf/F,IAAA,QAAKoF,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvFrF,IAAA,SAAMoF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,CAC3D,CACN,EACE,CAAC,cAENnF,KAAA,QAAKkF,SAAS,QAAAtD,MAAA,CAASrB,QAAQ,GAAK,MAAM,CAAG,sCAAsC,CAAG,EAAE,CAAG,CAAA4E,QAAA,eACzFnF,KAAA,QAAAmF,QAAA,eACErF,IAAA,OAAIoF,SAAS,qCAAAtD,MAAA,CACXrB,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CAC1C,CAAA4E,QAAA,CACAzD,OAAO,CAACG,IAAI,CACX,CAAC,cAEL7B,KAAA,QAAKkF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCrF,IAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACvD,GAAG,CAAC,CAACwD,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACxE,OAAO,CAACsC,MAAM,CAAC,cAC5BlE,IAAA,CAACR,aAAa,EAAS4F,SAAS,CAAC,yBAAyB,EAAtCc,CAAwC,CAAC,cAE7DlG,IAAA,CAACjB,QAAQ,EAASqG,SAAS,CAAC,uBAAuB,EAApCc,CAAsC,CAExD,CAAC,CACC,CAAC,cACNhG,KAAA,SAAMkF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCzD,OAAO,CAACsC,MAAM,CAAC,IAAE,CAACtC,OAAO,CAACyE,OAAO,CAAC,GACrC,EAAM,CAAC,EACJ,CAAC,cAENnG,KAAA,QAAKkF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CnF,KAAA,SAAMkF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,GACxD,CAACzD,OAAO,CAACoC,KAAK,EACX,CAAC,CACNpC,OAAO,CAAC0E,aAAa,EAAI1E,OAAO,CAAC0E,aAAa,CAAG1E,OAAO,CAACoC,KAAK,eAC7D9D,KAAA,SAAMkF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACzD,OAAO,CAAC0E,aAAa,EACnB,CACP,EACE,CAAC,CAGL1E,OAAO,CAACyB,IAAI,GAAK,SAAS,cACzBnD,KAAA,QAAKkF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBnF,KAAA,QAAKkF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CrF,IAAA,CAACX,SAAS,EAAC+F,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAChDpF,IAAA,SAAMoF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACzE,CAAC,CACLzD,OAAO,CAAC2E,SAAS,eAChBrG,KAAA,QAAKkF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CrF,IAAA,SAAMoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDrF,IAAA,SAAMoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEzD,OAAO,CAAC2E,SAAS,CAACvD,IAAI,CAAC,IAAI,CAAC,CAAO,CAAC,EAC1E,CACN,EACE,CAAC,cAEN9C,KAAA,CAAAE,SAAA,EAAAiF,QAAA,EAEGzD,OAAO,CAAC4E,MAAM,eACbtG,KAAA,QAAKkF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CrF,IAAA,SAAMoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,CACrDzD,OAAO,CAAC4E,MAAM,CAACzD,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACN,GAAG,CAAC,CAACgE,KAAK,CAAE/B,KAAK,gBAC3C1E,IAAA,QAEEoF,SAAS,iEAAAtD,MAAA,CACP2E,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,OAAO,CAAG,UAAU,CACnDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,aAAa,CACpDA,KAAK,GAAK,KAAK,EAAIA,KAAK,GAAK,KAAK,CAAG,YAAY,CACjDA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,QAAQ,CAAG,aAAa,CACxDA,KAAK,GAAK,MAAM,EAAIA,KAAK,GAAK,MAAM,CAAG,eAAe,CACtD,aAAa,CACZ,EATE/B,KAUN,CACF,CAAC,CACD9C,OAAO,CAAC4E,MAAM,CAAChE,MAAM,CAAG,CAAC,eACxBtC,KAAA,SAAMkF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,GAAC,CAACzD,OAAO,CAAC4E,MAAM,CAAChE,MAAM,CAAG,CAAC,EAAO,CAC3E,EACE,CACN,cAEDtC,KAAA,QAAKkF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CrF,IAAA,CAACV,SAAS,EAAC8F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/CpF,IAAA,SAAMoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpC,CAAAZ,iBAAA,CAAA7C,OAAO,CAAC8E,QAAQ,UAAAjC,iBAAA,WAAhBA,iBAAA,CAAkBkC,IAAI,CAAG,eAAe,CAAG,oBAAoB,CAC5D,CAAC,EACJ,CAAC,EACN,CACH,cAGDzG,KAAA,QAAKkF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CrF,IAAA,CAACT,eAAe,EAAC6F,SAAS,YAAAtD,MAAA,CAAaF,OAAO,CAACmE,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAE,CAAC,cAChG7F,KAAA,SAAMkF,SAAS,YAAAtD,MAAA,CAAaF,OAAO,CAACmE,OAAO,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAAV,QAAA,EAC/EzD,OAAO,CAACmE,OAAO,CAAG,UAAU,CAAG,cAAc,CAC7CnE,OAAO,CAACgF,UAAU,EAAIhF,OAAO,CAACmE,OAAO,OAAAjE,MAAA,CAASF,OAAO,CAACgF,UAAU,eAAa,EAC1E,CAAC,EACJ,CAAC,EACH,CAAC,cAEN1G,KAAA,CAAC1B,MAAM,CAACmH,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BgB,OAAO,CAAEA,CAAA,GAAMlF,eAAe,CAACC,OAAO,CAAE,CACxCkF,QAAQ,CAAE,CAAClF,OAAO,CAACmE,OAAQ,CAC3BX,SAAS,gHAAAtD,MAAA,CACPF,OAAO,CAACmE,OAAO,CACX,6HAA6H,CAC7H,8CAA8C,CACjD,CAAAV,QAAA,eAEHrF,IAAA,CAACf,eAAe,EAACmG,SAAS,CAAC,SAAS,CAAE,CAAC,cACvCpF,IAAA,SAAAqF,QAAA,CAAOzD,OAAO,CAACmE,OAAO,CAAG,aAAa,CAAG,cAAc,CAAO,CAAC,EAClD,CAAC,EACb,CAAC,EACI,CAAC,EACd,CAED,mBACE7F,KAAA,QAAKkF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCrF,IAAA,CAACF,OAAO,EAACmC,QAAQ,CAAC,WAAW,CAAE,CAAC,cAEhCjC,IAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCrF,IAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DnF,KAAA,QAAKkF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDnF,KAAA,CAACvB,IAAI,EAACoI,EAAE,CAAC,GAAG,CAAC3B,SAAS,CAAC,+EAA+E,CAAAC,QAAA,eACpGrF,IAAA,CAACZ,QAAQ,EAACgG,SAAS,CAAC,cAAc,CAAE,CAAC,OAEvC,EAAM,CAAC,cACPpF,IAAA,CAACb,gBAAgB,EAACiG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtDpF,IAAA,SAAMoF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,CAC9CxE,gBAAgB,GAAK,KAAK,eACzBX,KAAA,CAAAE,SAAA,EAAAiF,QAAA,eACErF,IAAA,CAACb,gBAAgB,EAACiG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtDpF,IAAA,SAAMoF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChDnD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEH,IAAI,CAClB,CAAC,EACP,CACH,CACAf,mBAAmB,GAAK,KAAK,eAC5Bd,KAAA,CAAAE,SAAA,EAAAiF,QAAA,eACErF,IAAA,CAACb,gBAAgB,EAACiG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACtDpF,IAAA,SAAMoF,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAA/E,mBAAA,CAChDgC,aAAa,CAACH,IAAI,CAACO,GAAG,EAAIA,GAAG,CAACL,EAAE,GAAKrB,mBAAmB,CAAC,UAAAV,mBAAA,iBAAzDA,mBAAA,CAA2DyB,IAAI,CAC5D,CAAC,EACP,CACH,EACE,CAAC,CACH,CAAC,CACH,CAAC,cAGN/B,IAAA,QAAKoF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ErF,IAAA,QAAKoF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDnF,KAAA,CAAC1B,MAAM,CAACmG,GAAG,EACTE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BK,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEvBrF,IAAA,OAAIoF,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAC3DxE,gBAAgB,GAAK,KAAK,CAAG,cAAc,CAAG,CAAAqB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEH,IAAI,GAAI,UAAU,CAChF,CAAC,cACL/B,IAAA,MAAGoF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAC3DxE,gBAAgB,GAAK,KAAK,CACvB,qDAAqD,CACrD,CAAAqB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE8E,WAAW,GAAI,+BAA+B,CAElE,CAAC,EACM,CAAC,CACV,CAAC,CACH,CAAC,cAGNhH,IAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCrF,IAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DrF,IAAA,QAAKoF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CACjD,CAAC,CAAEhD,EAAE,CAAE,KAAK,CAAEN,IAAI,CAAE,cAAc,CAAEkF,IAAI,CAAE,KAAM,CAAC,CAAE,GAAGxH,UAAU,CAAC,CAACgD,GAAG,CAAEmB,QAAQ,eAC9E1D,KAAA,CAAC1B,MAAM,CAACmH,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BgB,OAAO,CAAEA,CAAA,GAAM,CACb/F,mBAAmB,CAAC8C,QAAQ,CAACvB,EAAE,CAAC,CAChCpB,sBAAsB,CAAC,KAAK,CAAC,CAC7BT,eAAe,CAAC,CAAEoD,QAAQ,CAAEA,QAAQ,CAACvB,EAAG,CAAC,CAAC,CAC5C,CAAE,CACF+C,SAAS,kFAAAtD,MAAA,CACPjB,gBAAgB,GAAK+C,QAAQ,CAACvB,EAAE,CAC5B,0CAA0C,CAC1C,iFAAiF,CACpF,CAAAgD,QAAA,eAEHrF,IAAA,SAAMoF,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEzB,QAAQ,CAACqD,IAAI,CAAO,CAAC,cAChDjH,IAAA,SAAAqF,QAAA,CAAOzB,QAAQ,CAAC7B,IAAI,CAAO,CAAC,GAfvB6B,QAAQ,CAACvB,EAgBD,CAChB,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENrC,IAAA,QAAKoF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DnF,KAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE9CrF,IAAA,QAAKoF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCnF,KAAA,QAAKkF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,KAAA,QAAKkF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDrF,IAAA,OAAIoF,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAChErF,IAAA,WACE6G,OAAO,CAAEA,CAAA,GAAMpF,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5C4D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAEvCrF,IAAA,CAACd,yBAAyB,EAACkG,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3C,CAAC,EACN,CAAC,cAENlF,KAAA,QAAKkF,SAAS,cAAAtD,MAAA,CAAeN,WAAW,CAAG,OAAO,CAAG,iBAAiB,CAAG,CAAA6D,QAAA,eAEvEnF,KAAA,QAAAmF,QAAA,eACErF,IAAA,OAAIoF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAChErF,IAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBjC,kBAAkB,CAACX,GAAG,CAACY,IAAI,eAC1BrD,IAAA,WAEE6G,OAAO,CAAEA,CAAA,GAAM1F,cAAc,CAACkC,IAAI,CAAChB,EAAE,CAAE,CACvC+C,SAAS,4DAAAtD,MAAA,CACPZ,WAAW,GAAKmC,IAAI,CAAChB,EAAE,CACnB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAgD,QAAA,cAEHnF,KAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrF,IAAA,SAAAqF,QAAA,CAAOhC,IAAI,CAACtB,IAAI,CAAO,CAAC,cACxB7B,KAAA,SAAMkF,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,GAAC,CAAChC,IAAI,CAACd,KAAK,CAAC,GAAC,EAAM,CAAC,EAC5C,CAAC,EAXDc,IAAI,CAAChB,EAYJ,CACT,CAAC,CACC,CAAC,EACH,CAAC,CAGLxB,gBAAgB,GAAK,KAAK,EAAIyB,aAAa,CAACE,MAAM,CAAG,CAAC,eACrDtC,KAAA,QAAAmF,QAAA,eACEnF,KAAA,OAAIkF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAC3CnD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEH,IAAI,CAAC,aACzB,EAAI,CAAC,cACL/B,IAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB/C,aAAa,CAACG,GAAG,CAACU,WAAW,eAC5BnD,IAAA,WAEE6G,OAAO,CAAEA,CAAA,GAAM5F,sBAAsB,CAACkC,WAAW,CAACd,EAAE,CAAE,CACtD+C,SAAS,4DAAAtD,MAAA,CACPd,mBAAmB,GAAKmC,WAAW,CAACd,EAAE,CAClC,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAgD,QAAA,cAEHnF,KAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrF,IAAA,SAAAqF,QAAA,CAAOlC,WAAW,CAACpB,IAAI,CAAO,CAAC,cAC/B7B,KAAA,SAAMkF,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,GAAC,CAAClC,WAAW,CAACZ,KAAK,CAAC,GAAC,EAAM,CAAC,EACnD,CAAC,EAXDY,WAAW,CAACd,EAYX,CACT,CAAC,CACC,CAAC,EACH,CACN,cAGDnC,KAAA,QAAAmF,QAAA,eACEnF,KAAA,OAAIkF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAC,gBAC/B,CAACjE,UAAU,CAAC,CAAC,CAAC,CAAC,MAAI,CAACA,UAAU,CAAC,CAAC,CAAC,EAC7C,CAAC,cACLpB,IAAA,UACEqD,IAAI,CAAC,OAAO,CACZ6D,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,MAAM,CACV5D,KAAK,CAAEnC,UAAU,CAAC,CAAC,CAAE,CACrBgG,QAAQ,CAAGC,CAAC,EAAKhG,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC,CAAEkG,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAC,CAAC,CAAE,CAC1E6B,SAAS,CAAC,QAAQ,CACnB,CAAC,EACC,CAAC,cAGNlF,KAAA,QAAAmF,QAAA,eACErF,IAAA,OAAIoF,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAClErF,IAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC5C,GAAG,CAACyB,MAAM,eACzBhE,KAAA,WAEE2G,OAAO,CAAEA,CAAA,GAAMtF,iBAAiB,CAAC2C,MAAM,CAAE,CACzCkB,SAAS,8EAAAtD,MAAA,CACPR,cAAc,GAAK4C,MAAM,CACrB,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAmB,QAAA,eAEHrF,IAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACvD,GAAG,CAAC,CAACwD,CAAC,CAAEC,CAAC,gBACtBlG,IAAA,CAACR,aAAa,EAEZ4F,SAAS,YAAAtD,MAAA,CACPoE,CAAC,CAAGhC,MAAM,CAAG,iBAAiB,CAAG,eAAe,CAC/C,EAHEgC,CAIN,CACF,CAAC,CACC,CAAC,cACNlG,IAAA,SAAAqF,QAAA,CAAOnB,MAAM,CAAG,CAAC,IAAApC,MAAA,CAAMoC,MAAM,YAAY,aAAa,CAAO,CAAC,GAlBzDA,MAmBC,CACT,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNhE,KAAA,QAAKkF,SAAS,CAAC,QAAQ,CAAAC,QAAA,eAErBrF,IAAA,QAAKoF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDnF,KAAA,QAAKkF,SAAS,CAAC,6EAA6E,CAAAC,QAAA,eAC1FrF,IAAA,QAAAqF,QAAA,cACEnF,KAAA,MAAGkF,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,UACnB,CAAC5B,yBAAyB,CAACjB,MAAM,CAAC,MAAI,CAAC9C,QAAQ,CAAC8C,MAAM,CAAC,WACjE,EAAG,CAAC,CACD,CAAC,cAENtC,KAAA,QAAKkF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAE1CrF,IAAA,WACEuD,KAAK,CAAE5C,MAAO,CACdyG,QAAQ,CAAGC,CAAC,EAAKzG,SAAS,CAACyG,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAE,CAC3C6B,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CAE/F/B,WAAW,CAACb,GAAG,CAAC+E,MAAM,eACrBxH,IAAA,WAA2BuD,KAAK,CAAEiE,MAAM,CAACjE,KAAM,CAAA8B,QAAA,CAC5CmC,MAAM,CAAChE,KAAK,EADFgE,MAAM,CAACjE,KAEZ,CACT,CAAC,CACI,CAAC,cAGTrD,KAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CrF,IAAA,WACE6G,OAAO,CAAEA,CAAA,GAAMnG,WAAW,CAAC,MAAM,CAAE,CACnC0E,SAAS,qCAAAtD,MAAA,CACPrB,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAA4E,QAAA,cAEHrF,IAAA,CAACnB,cAAc,EAACuG,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,cACTpF,IAAA,WACE6G,OAAO,CAAEA,CAAA,GAAMnG,WAAW,CAAC,MAAM,CAAE,CACnC0E,SAAS,qCAAAtD,MAAA,CACPrB,QAAQ,GAAK,MAAM,CACf,0CAA0C,CAC1C,eAAe,CAClB,CAAA4E,QAAA,cAEHrF,IAAA,CAAClB,cAAc,EAACsG,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNpF,IAAA,CAACvB,eAAe,EAACgJ,IAAI,CAAC,MAAM,CAAApC,QAAA,cAC1BrF,IAAA,CAACxB,MAAM,CAACmG,GAAG,EAETE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAE,CAAE,CACrBM,SAAS,IAAAtD,MAAA,CACPrB,QAAQ,GAAK,MAAM,CACf,sDAAsD,CACtD,WAAW,CACd,CAAA4E,QAAA,CAEF5B,yBAAyB,CAAChB,GAAG,CAAC,CAACb,OAAO,CAAE8C,KAAK,gBAC5C1E,IAAA,CAACuE,WAAW,EAAkB3C,OAAO,CAAEA,OAAQ,CAAC8C,KAAK,CAAEA,KAAM,EAA3C9C,OAAO,CAACS,EAAqC,CAChE,CAAC,KAAAP,MAAA,CAZMrB,QAAQ,MAAAqB,MAAA,CAAIjB,gBAAgB,MAAAiB,MAAA,CAAInB,MAAM,CAapC,CAAC,CACE,CAAC,CAEjB8C,yBAAyB,CAACjB,MAAM,GAAK,CAAC,eACrCtC,KAAA,QAAKkF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrF,IAAA,QAAKoF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCrF,IAAA,CAACpB,UAAU,EAACwG,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACzC,CAAC,cACNpF,IAAA,OAAIoF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC/ErF,IAAA,MAAGoF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}