{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"source\", \"container\"];\nimport { supportsScrollTimeline } from 'motion-dom';\nimport { scrollInfo } from '../track.mjs';\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n  const currentTime = {\n    value: 0\n  };\n  const cancel = scrollInfo(info => {\n    currentTime.value = info[options.axis].progress * 100;\n  }, options);\n  return {\n    currentTime,\n    cancel\n  };\n}\nfunction getTimeline(_ref) {\n  var _timelineCache$get, _options$target, _containerCache$get, _options$offset;\n  let {\n      source,\n      container\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const {\n    axis\n  } = options;\n  if (source) container = source;\n  const containerCache = (_timelineCache$get = timelineCache.get(container)) !== null && _timelineCache$get !== void 0 ? _timelineCache$get : new Map();\n  timelineCache.set(container, containerCache);\n  const targetKey = (_options$target = options.target) !== null && _options$target !== void 0 ? _options$target : \"self\";\n  const targetCache = (_containerCache$get = containerCache.get(targetKey)) !== null && _containerCache$get !== void 0 ? _containerCache$get : {};\n  const axisKey = axis + ((_options$offset = options.offset) !== null && _options$offset !== void 0 ? _options$offset : []).join(\",\");\n  if (!targetCache[axisKey]) {\n    targetCache[axisKey] = !options.target && supportsScrollTimeline() ? new ScrollTimeline({\n      source: container,\n      axis\n    }) : scrollTimelineFallback(_objectSpread({\n      container\n    }, options));\n  }\n  return targetCache[axisKey];\n}\nexport { getTimeline };", "map": {"version": 3, "names": ["supportsScrollTimeline", "scrollInfo", "timelineCache", "Map", "scrollTimelineFallback", "options", "currentTime", "value", "cancel", "info", "axis", "progress", "getTimeline", "_ref", "_timelineCache$get", "_options$target", "_containerCache$get", "_options$offset", "source", "container", "_objectWithoutProperties", "_excluded", "containerCache", "get", "set", "<PERSON><PERSON><PERSON>", "target", "targetCache", "axisKey", "offset", "join", "ScrollTimeline", "_objectSpread"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs"], "sourcesContent": ["import { supportsScrollTimeline } from 'motion-dom';\nimport { scrollInfo } from '../track.mjs';\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = scrollInfo((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && supportsScrollTimeline()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\nexport { getTimeline };\n"], "mappings": ";;;AAAA,SAASA,sBAAsB,QAAQ,YAAY;AACnD,SAASC,UAAU,QAAQ,cAAc;AAEzC,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EACrC,MAAMC,WAAW,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EAChC,MAAMC,MAAM,GAAGP,UAAU,CAAEQ,IAAI,IAAK;IAChCH,WAAW,CAACC,KAAK,GAAGE,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAACC,QAAQ,GAAG,GAAG;EACzD,CAAC,EAAEN,OAAO,CAAC;EACX,OAAO;IAAEC,WAAW;IAAEE;EAAO,CAAC;AAClC;AACA,SAASI,WAAWA,CAAAC,IAAA,EAAoC;EAAA,IAAAC,kBAAA,EAAAC,eAAA,EAAAC,mBAAA,EAAAC,eAAA;EAAA,IAAnC;MAAEC,MAAM;MAAEC;IAAsB,CAAC,GAAAN,IAAA;IAATR,OAAO,GAAAe,wBAAA,CAAAP,IAAA,EAAAQ,SAAA;EAChD,MAAM;IAAEX;EAAK,CAAC,GAAGL,OAAO;EACxB,IAAIa,MAAM,EACNC,SAAS,GAAGD,MAAM;EACtB,MAAMI,cAAc,IAAAR,kBAAA,GAAGZ,aAAa,CAACqB,GAAG,CAACJ,SAAS,CAAC,cAAAL,kBAAA,cAAAA,kBAAA,GAAI,IAAIX,GAAG,CAAC,CAAC;EAChED,aAAa,CAACsB,GAAG,CAACL,SAAS,EAAEG,cAAc,CAAC;EAC5C,MAAMG,SAAS,IAAAV,eAAA,GAAGV,OAAO,CAACqB,MAAM,cAAAX,eAAA,cAAAA,eAAA,GAAI,MAAM;EAC1C,MAAMY,WAAW,IAAAX,mBAAA,GAAGM,cAAc,CAACC,GAAG,CAACE,SAAS,CAAC,cAAAT,mBAAA,cAAAA,mBAAA,GAAI,CAAC,CAAC;EACvD,MAAMY,OAAO,GAAGlB,IAAI,GAAG,EAAAO,eAAA,GAACZ,OAAO,CAACwB,MAAM,cAAAZ,eAAA,cAAAA,eAAA,GAAI,EAAE,EAAEa,IAAI,CAAC,GAAG,CAAC;EACvD,IAAI,CAACH,WAAW,CAACC,OAAO,CAAC,EAAE;IACvBD,WAAW,CAACC,OAAO,CAAC,GAChB,CAACvB,OAAO,CAACqB,MAAM,IAAI1B,sBAAsB,CAAC,CAAC,GACrC,IAAI+B,cAAc,CAAC;MAAEb,MAAM,EAAEC,SAAS;MAAET;IAAK,CAAC,CAAC,GAC/CN,sBAAsB,CAAA4B,aAAA;MAAGb;IAAS,GAAKd,OAAO,CAAE,CAAC;EAC/D;EACA,OAAOsB,WAAW,CAACC,OAAO,CAAC;AAC/B;AAEA,SAAShB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}