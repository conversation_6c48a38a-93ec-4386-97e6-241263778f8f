{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\UserAccounts.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { UserIcon, ArrowRightOnRectangleIcon as LoginIcon, ArrowLeftOnRectangleIcon as LogoutIcon, CogIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserAccounts = () => {\n  _s();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [showRegisterModal, setShowRegisterModal] = useState(false);\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [registerForm, setRegisterForm] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n\n  // Mock user data\n  const user = {\n    name: 'John Doe',\n    email: '<EMAIL>',\n    avatar: 'https://via.placeholder.com/40'\n  };\n  const handleLogin = e => {\n    e.preventDefault();\n    // Mock login logic\n    setIsLoggedIn(true);\n    setShowLoginModal(false);\n    setLoginForm({\n      email: '',\n      password: ''\n    });\n  };\n  const handleRegister = e => {\n    e.preventDefault();\n    // Mock register logic\n    setIsLoggedIn(true);\n    setShowRegisterModal(false);\n    setRegisterForm({\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n  };\n  const handleLogout = () => {\n    setIsLoggedIn(false);\n    setIsDropdownOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [isLoggedIn ? /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsDropdownOpen(!isDropdownOpen),\n      className: \"flex items-center space-x-2 p-2 bg-light-orange-100 text-light-orange-700 rounded-full hover:bg-light-orange-200 transition-colors shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.avatar,\n        alt: user.name,\n        className: \"w-8 h-8 rounded-full border-2 border-light-orange-300\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"hidden md:block font-medium\",\n        children: user.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-4 h-4\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M19 9l-7 7-7-7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setShowLoginModal(true),\n      className: \"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Sign In\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this), isLoggedIn && isDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-light-orange-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.avatar,\n            alt: user.name,\n            className: \"w-12 h-12 rounded-full border-2 border-light-orange-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-light-orange-800\",\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-light-orange-600\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n            className: \"w-5 h-5 text-light-orange-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-light-orange-800\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(CogIcon, {\n            className: \"w-5 h-5 text-light-orange-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-light-orange-800\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n          className: \"my-2 border-light-orange-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(LogoutIcon, {\n            className: \"w-5 h-5 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-red-800\",\n            children: \"Sign Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this), showLoginModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLoginModal(false),\n              className: \"text-white hover:text-light-orange-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleLogin,\n          className: \"p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: loginForm.email,\n              onChange: e => setLoginForm({\n                ...loginForm,\n                email: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Enter your email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: loginForm.password,\n              onChange: e => setLoginForm({\n                ...loginForm,\n                password: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Enter your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => {\n                setShowLoginModal(false);\n                setShowRegisterModal(true);\n              },\n              className: \"text-light-orange-600 hover:text-light-orange-700 transition-colors\",\n              children: \"Don't have an account? Sign up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this), showRegisterModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowRegisterModal(false),\n              className: \"text-white hover:text-light-orange-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleRegister,\n          className: \"p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: registerForm.name,\n              onChange: e => setRegisterForm({\n                ...registerForm,\n                name: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Enter your full name\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: registerForm.email,\n              onChange: e => setRegisterForm({\n                ...registerForm,\n                email: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Enter your email\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: registerForm.password,\n              onChange: e => setRegisterForm({\n                ...registerForm,\n                password: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Create a password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-light-orange-800 mb-2\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              value: registerForm.confirmPassword,\n              onChange: e => setRegisterForm({\n                ...registerForm,\n                confirmPassword: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n              placeholder: \"Confirm your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => {\n                setShowRegisterModal(false);\n                setShowLoginModal(true);\n              },\n              className: \"text-light-orange-600 hover:text-light-orange-700 transition-colors\",\n              children: \"Already have an account? Sign in\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(UserAccounts, \"9x0ku+nr6anCl+5lOuezXbVVtlI=\");\n_c = UserAccounts;\nexport default UserAccounts;\nvar _c;\n$RefreshReg$(_c, \"UserAccounts\");", "map": {"version": 3, "names": ["React", "useState", "UserIcon", "ArrowRightOnRectangleIcon", "LoginIcon", "ArrowLeftOnRectangleIcon", "LogoutIcon", "CogIcon", "jsxDEV", "_jsxDEV", "UserAccounts", "_s", "isLoggedIn", "setIsLoggedIn", "isDropdownOpen", "setIsDropdownOpen", "showLoginModal", "setShowLoginModal", "showRegisterModal", "setShowRegisterModal", "loginForm", "setLoginForm", "email", "password", "registerForm", "setRegisterForm", "name", "confirmPassword", "user", "avatar", "handleLogin", "e", "preventDefault", "handleRegister", "handleLogout", "className", "children", "onClick", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/UserAccounts.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { UserIcon, ArrowRightOnRectangleIcon as LoginIcon, ArrowLeftOnRectangleIcon as LogoutIcon, CogIcon } from '@heroicons/react/24/outline';\r\n\r\nconst UserAccounts = () => {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n  const [showLoginModal, setShowLoginModal] = useState(false);\r\n  const [showRegisterModal, setShowRegisterModal] = useState(false);\r\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\r\n  const [registerForm, setRegisterForm] = useState({\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n\r\n  // Mock user data\r\n  const user = {\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    avatar: 'https://via.placeholder.com/40'\r\n  };\r\n\r\n  const handleLogin = (e) => {\r\n    e.preventDefault();\r\n    // Mock login logic\r\n    setIsLoggedIn(true);\r\n    setShowLoginModal(false);\r\n    setLoginForm({ email: '', password: '' });\r\n  };\r\n\r\n  const handleRegister = (e) => {\r\n    e.preventDefault();\r\n    // Mock register logic\r\n    setIsLoggedIn(true);\r\n    setShowRegisterModal(false);\r\n    setRegisterForm({ name: '', email: '', password: '', confirmPassword: '' });\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setIsLoggedIn(false);\r\n    setIsDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* User Button */}\r\n      {isLoggedIn ? (\r\n        <button\r\n          onClick={() => setIsDropdownOpen(!isDropdownOpen)}\r\n          className=\"flex items-center space-x-2 p-2 bg-light-orange-100 text-light-orange-700 rounded-full hover:bg-light-orange-200 transition-colors shadow-md\"\r\n        >\r\n          <img\r\n            src={user.avatar}\r\n            alt={user.name}\r\n            className=\"w-8 h-8 rounded-full border-2 border-light-orange-300\"\r\n          />\r\n          <span className=\"hidden md:block font-medium\">{user.name}</span>\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n          </svg>\r\n        </button>\r\n      ) : (\r\n        <button\r\n          onClick={() => setShowLoginModal(true)}\r\n          className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors shadow-md\"\r\n        >\r\n          <LoginIcon className=\"w-5 h-5\" />\r\n          <span>Sign In</span>\r\n        </button>\r\n      )}\r\n\r\n      {/* User Dropdown Menu */}\r\n      {isLoggedIn && isDropdownOpen && (\r\n        <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\">\r\n          <div className=\"p-4 border-b border-light-orange-100\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <img\r\n                src={user.avatar}\r\n                alt={user.name}\r\n                className=\"w-12 h-12 rounded-full border-2 border-light-orange-300\"\r\n              />\r\n              <div>\r\n                <h3 className=\"font-semibold text-light-orange-800\">{user.name}</h3>\r\n                <p className=\"text-sm text-light-orange-600\">{user.email}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-2\">\r\n            <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\">\r\n              <UserIcon className=\"w-5 h-5 text-light-orange-600\" />\r\n              <span className=\"text-light-orange-800\">Profile</span>\r\n            </button>\r\n            <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\">\r\n              <CogIcon className=\"w-5 h-5 text-light-orange-600\" />\r\n              <span className=\"text-light-orange-800\">Settings</span>\r\n            </button>\r\n            <hr className=\"my-2 border-light-orange-100\" />\r\n            <button\r\n              onClick={handleLogout}\r\n              className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors\"\r\n            >\r\n              <LogoutIcon className=\"w-5 h-5 text-red-600\" />\r\n              <span className=\"text-red-800\">Sign Out</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Login Modal */}\r\n      {showLoginModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\">\r\n            <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-bold text-white\">Sign In</h2>\r\n                <button\r\n                  onClick={() => setShowLoginModal(false)}\r\n                  className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <form onSubmit={handleLogin} className=\"p-6 space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Email Address\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  value={loginForm.email}\r\n                  onChange={(e) => setLoginForm({...loginForm, email: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={loginForm.password}\r\n                  onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\"\r\n              >\r\n                Sign In\r\n              </button>\r\n\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    setShowLoginModal(false);\r\n                    setShowRegisterModal(true);\r\n                  }}\r\n                  className=\"text-light-orange-600 hover:text-light-orange-700 transition-colors\"\r\n                >\r\n                  Don't have an account? Sign up\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Register Modal */}\r\n      {showRegisterModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\">\r\n            <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-bold text-white\">Create Account</h2>\r\n                <button\r\n                  onClick={() => setShowRegisterModal(false)}\r\n                  className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <form onSubmit={handleRegister} className=\"p-6 space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Full Name\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={registerForm.name}\r\n                  onChange={(e) => setRegisterForm({...registerForm, name: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your full name\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Email Address\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  value={registerForm.email}\r\n                  onChange={(e) => setRegisterForm({...registerForm, email: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={registerForm.password}\r\n                  onChange={(e) => setRegisterForm({...registerForm, password: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Create a password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Confirm Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={registerForm.confirmPassword}\r\n                  onChange={(e) => setRegisterForm({...registerForm, confirmPassword: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Confirm your password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\"\r\n              >\r\n                Create Account\r\n              </button>\r\n\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    setShowRegisterModal(false);\r\n                    setShowLoginModal(true);\r\n                  }}\r\n                  className=\"text-light-orange-600 hover:text-light-orange-700 transition-colors\"\r\n                >\r\n                  Already have an account? Sign in\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserAccounts;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,yBAAyB,IAAIC,SAAS,EAAEC,wBAAwB,IAAIC,UAAU,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhJ,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IAAEqB,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC;IAC/CyB,IAAI,EAAE,EAAE;IACRJ,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZI,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,IAAI,GAAG;IACXF,IAAI,EAAE,UAAU;IAChBJ,KAAK,EAAE,sBAAsB;IAC7BO,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,WAAW,GAAIC,CAAC,IAAK;IACzBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAnB,aAAa,CAAC,IAAI,CAAC;IACnBI,iBAAiB,CAAC,KAAK,CAAC;IACxBI,YAAY,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMU,cAAc,GAAIF,CAAC,IAAK;IAC5BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAnB,aAAa,CAAC,IAAI,CAAC;IACnBM,oBAAoB,CAAC,KAAK,CAAC;IAC3BM,eAAe,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEJ,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEI,eAAe,EAAE;IAAG,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBrB,aAAa,CAAC,KAAK,CAAC;IACpBE,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,oBACEN,OAAA;IAAK0B,SAAS,EAAC,UAAU;IAAAC,QAAA,GAEtBxB,UAAU,gBACTH,OAAA;MACE4B,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAAC,CAACD,cAAc,CAAE;MAClDqB,SAAS,EAAC,8IAA8I;MAAAC,QAAA,gBAExJ3B,OAAA;QACE6B,GAAG,EAAEV,IAAI,CAACC,MAAO;QACjBU,GAAG,EAAEX,IAAI,CAACF,IAAK;QACfS,SAAS,EAAC;MAAuD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACFlC,OAAA;QAAM0B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAER,IAAI,CAACF;MAAI;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChElC,OAAA;QAAK0B,SAAS,EAAC,SAAS;QAACS,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAV,QAAA,eAC5E3B,OAAA;UAAMsC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAAgB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,gBAETlC,OAAA;MACE4B,OAAO,EAAEA,CAAA,KAAMpB,iBAAiB,CAAC,IAAI,CAAE;MACvCkB,SAAS,EAAC,uIAAuI;MAAAC,QAAA,gBAEjJ3B,OAAA,CAACL,SAAS;QAAC+B,SAAS,EAAC;MAAS;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjClC,OAAA;QAAA2B,QAAA,EAAM;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACT,EAGA/B,UAAU,IAAIE,cAAc,iBAC3BL,OAAA;MAAK0B,SAAS,EAAC,+FAA+F;MAAAC,QAAA,gBAC5G3B,OAAA;QAAK0B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACnD3B,OAAA;UAAK0B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C3B,OAAA;YACE6B,GAAG,EAAEV,IAAI,CAACC,MAAO;YACjBU,GAAG,EAAEX,IAAI,CAACF,IAAK;YACfS,SAAS,EAAC;UAAyD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACFlC,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAI0B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAER,IAAI,CAACF;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpElC,OAAA;cAAG0B,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAER,IAAI,CAACN;YAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA;UAAQ0B,SAAS,EAAC,mGAAmG;UAAAC,QAAA,gBACnH3B,OAAA,CAACP,QAAQ;YAACiC,SAAS,EAAC;UAA+B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDlC,OAAA;YAAM0B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACTlC,OAAA;UAAQ0B,SAAS,EAAC,mGAAmG;UAAAC,QAAA,gBACnH3B,OAAA,CAACF,OAAO;YAAC4B,SAAS,EAAC;UAA+B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDlC,OAAA;YAAM0B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACTlC,OAAA;UAAI0B,SAAS,EAAC;QAA8B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ClC,OAAA;UACE4B,OAAO,EAAEH,YAAa;UACtBC,SAAS,EAAC,0FAA0F;UAAAC,QAAA,gBAEpG3B,OAAA,CAACH,UAAU;YAAC6B,SAAS,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ClC,OAAA;YAAM0B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA3B,cAAc,iBACbP,OAAA;MAAK0B,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF3B,OAAA;QAAK0B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE3B,OAAA;UAAK0B,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAChG3B,OAAA;YAAK0B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3B,OAAA;cAAI0B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDlC,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAMpB,iBAAiB,CAAC,KAAK,CAAE;cACxCkB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,eAEpE3B,OAAA;gBAAK0B,SAAS,EAAC,SAAS;gBAACS,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC5E3B,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAM0C,QAAQ,EAAErB,WAAY;UAACK,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACpD3B,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEjC,SAAS,CAACE,KAAM;cACvBgC,QAAQ,EAAGvB,CAAC,IAAKV,YAAY,CAAC;gBAAC,GAAGD,SAAS;gBAAEE,KAAK,EAAES,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrElB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEjC,SAAS,CAACG,QAAS;cAC1B+B,QAAQ,EAAGvB,CAAC,IAAKV,YAAY,CAAC;gBAAC,GAAGD,SAAS;gBAAEG,QAAQ,EAAEQ,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cACxElB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,qBAAqB;cACjCC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbjB,SAAS,EAAC,6MAA6M;YAAAC,QAAA,EACxN;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlC,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B3B,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbf,OAAO,EAAEA,CAAA,KAAM;gBACbpB,iBAAiB,CAAC,KAAK,CAAC;gBACxBE,oBAAoB,CAAC,IAAI,CAAC;cAC5B,CAAE;cACFgB,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAChF;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAzB,iBAAiB,iBAChBT,OAAA;MAAK0B,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF3B,OAAA;QAAK0B,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE3B,OAAA;UAAK0B,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAChG3B,OAAA;YAAK0B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3B,OAAA;cAAI0B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChElC,OAAA;cACE4B,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAAC,KAAK,CAAE;cAC3CgB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,eAEpE3B,OAAA;gBAAK0B,SAAS,EAAC,SAAS;gBAACS,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC5E3B,OAAA;kBAAMsC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAM0C,QAAQ,EAAElB,cAAe;UAACE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACvD3B,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE7B,YAAY,CAACE,IAAK;cACzB4B,QAAQ,EAAGvB,CAAC,IAAKN,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEE,IAAI,EAAEK,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cAC1ElB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,sBAAsB;cAClCC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE7B,YAAY,CAACF,KAAM;cAC1BgC,QAAQ,EAAGvB,CAAC,IAAKN,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEF,KAAK,EAAES,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cAC3ElB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE7B,YAAY,CAACD,QAAS;cAC7B+B,QAAQ,EAAGvB,CAAC,IAAKN,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAED,QAAQ,EAAEQ,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cAC9ElB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,mBAAmB;cAC/BC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YAAA2B,QAAA,gBACE3B,OAAA;cAAO0B,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAAC;YAE1E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACE2C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE7B,YAAY,CAACG,eAAgB;cACpC2B,QAAQ,EAAGvB,CAAC,IAAKN,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEG,eAAe,EAAEI,CAAC,CAACwB,MAAM,CAACF;cAAK,CAAC,CAAE;cACrFlB,SAAS,EAAC,qJAAqJ;cAC/JqB,WAAW,EAAC,uBAAuB;cACnCC,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlC,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbjB,SAAS,EAAC,6MAA6M;YAAAC,QAAA,EACxN;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlC,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B3B,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbf,OAAO,EAAEA,CAAA,KAAM;gBACblB,oBAAoB,CAAC,KAAK,CAAC;gBAC3BF,iBAAiB,CAAC,IAAI,CAAC;cACzB,CAAE;cACFkB,SAAS,EAAC,qEAAqE;cAAAC,QAAA,EAChF;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChC,EAAA,CAtRID,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAwRlB,eAAeA,YAAY;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}