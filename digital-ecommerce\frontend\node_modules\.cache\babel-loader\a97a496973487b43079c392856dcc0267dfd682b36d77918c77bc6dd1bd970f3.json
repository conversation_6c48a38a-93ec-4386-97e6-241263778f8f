{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"attrX\", \"attrY\", \"attrScale\", \"pathLength\", \"pathSpacing\", \"pathOffset\"];\nimport { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, _ref, isSVGTag, transformTemplate, styleProp) {\n  let {\n      attrX,\n      attrY,\n      attrScale,\n      pathLength,\n      pathSpacing = 1,\n      pathOffset = 0\n      // This is object creation, which we try to avoid per-frame.\n    } = _ref,\n    latest = _objectWithoutProperties(_ref, _excluded);\n  buildHTMLStyles(state, latest, transformTemplate);\n  /**\n   * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n   * as normal HTML tags.\n   */\n  if (isSVGTag) {\n    if (state.style.viewBox) {\n      state.attrs.viewBox = state.style.viewBox;\n    }\n    return;\n  }\n  state.attrs = state.style;\n  state.style = {};\n  const {\n    attrs,\n    style\n  } = state;\n  /**\n   * However, we apply transforms as CSS transforms.\n   * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n   */\n  if (attrs.transform) {\n    style.transform = attrs.transform;\n    delete attrs.transform;\n  }\n  if (style.transform || attrs.transformOrigin) {\n    var _attrs$transformOrigi;\n    style.transformOrigin = (_attrs$transformOrigi = attrs.transformOrigin) !== null && _attrs$transformOrigi !== void 0 ? _attrs$transformOrigi : \"50% 50%\";\n    delete attrs.transformOrigin;\n  }\n  if (style.transform) {\n    var _styleProp$transformB;\n    /**\n     * SVG's element transform-origin uses its own median as a reference.\n     * Therefore, transformBox becomes a fill-box\n     */\n    style.transformBox = (_styleProp$transformB = styleProp === null || styleProp === void 0 ? void 0 : styleProp.transformBox) !== null && _styleProp$transformB !== void 0 ? _styleProp$transformB : \"fill-box\";\n    delete attrs.transformBox;\n  }\n  // Render attrX/attrY/attrScale as attributes\n  if (attrX !== undefined) attrs.x = attrX;\n  if (attrY !== undefined) attrs.y = attrY;\n  if (attrScale !== undefined) attrs.scale = attrScale;\n  // Build SVG path if one has been defined\n  if (pathLength !== undefined) {\n    buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n  }\n}\nexport { buildSVGAttrs };", "map": {"version": 3, "names": ["buildHTMLStyles", "buildSVGPath", "buildSVGAttrs", "state", "_ref", "isSVGTag", "transformTemplate", "styleProp", "attrX", "attrY", "attrScale", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "pathOffset", "latest", "_objectWithoutProperties", "_excluded", "style", "viewBox", "attrs", "transform", "transform<PERSON><PERSON>in", "_attrs$transformOrigi", "_styleProp$transformB", "transformBox", "undefined", "x", "y", "scale"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,mCAAmC;AACnE,SAASC,YAAY,QAAQ,YAAY;;AAEzC;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAAC,IAAA,EAEfC,QAAQ,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;EAAA,IAFvB;MAAEC,KAAK;MAAEC,KAAK;MAAEC,SAAS;MAAEC,UAAU;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU,GAAG;MACnG;IACU,CAAC,GAAAT,IAAA;IAARU,MAAM,GAAAC,wBAAA,CAAAX,IAAA,EAAAY,SAAA;EACLhB,eAAe,CAACG,KAAK,EAAEW,MAAM,EAAER,iBAAiB,CAAC;EACjD;AACJ;AACA;AACA;EACI,IAAID,QAAQ,EAAE;IACV,IAAIF,KAAK,CAACc,KAAK,CAACC,OAAO,EAAE;MACrBf,KAAK,CAACgB,KAAK,CAACD,OAAO,GAAGf,KAAK,CAACc,KAAK,CAACC,OAAO;IAC7C;IACA;EACJ;EACAf,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACc,KAAK;EACzBd,KAAK,CAACc,KAAK,GAAG,CAAC,CAAC;EAChB,MAAM;IAAEE,KAAK;IAAEF;EAAM,CAAC,GAAGd,KAAK;EAC9B;AACJ;AACA;AACA;EACI,IAAIgB,KAAK,CAACC,SAAS,EAAE;IACjBH,KAAK,CAACG,SAAS,GAAGD,KAAK,CAACC,SAAS;IACjC,OAAOD,KAAK,CAACC,SAAS;EAC1B;EACA,IAAIH,KAAK,CAACG,SAAS,IAAID,KAAK,CAACE,eAAe,EAAE;IAAA,IAAAC,qBAAA;IAC1CL,KAAK,CAACI,eAAe,IAAAC,qBAAA,GAAGH,KAAK,CAACE,eAAe,cAAAC,qBAAA,cAAAA,qBAAA,GAAI,SAAS;IAC1D,OAAOH,KAAK,CAACE,eAAe;EAChC;EACA,IAAIJ,KAAK,CAACG,SAAS,EAAE;IAAA,IAAAG,qBAAA;IACjB;AACR;AACA;AACA;IACQN,KAAK,CAACO,YAAY,IAAAD,qBAAA,GAAGhB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiB,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,UAAU;IAC1D,OAAOJ,KAAK,CAACK,YAAY;EAC7B;EACA;EACA,IAAIhB,KAAK,KAAKiB,SAAS,EACnBN,KAAK,CAACO,CAAC,GAAGlB,KAAK;EACnB,IAAIC,KAAK,KAAKgB,SAAS,EACnBN,KAAK,CAACQ,CAAC,GAAGlB,KAAK;EACnB,IAAIC,SAAS,KAAKe,SAAS,EACvBN,KAAK,CAACS,KAAK,GAAGlB,SAAS;EAC3B;EACA,IAAIC,UAAU,KAAKc,SAAS,EAAE;IAC1BxB,YAAY,CAACkB,KAAK,EAAER,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE,KAAK,CAAC;EACnE;AACJ;AAEA,SAASX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}