{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport { ShoppingBagIcon, StarIcon, TruckIcon, ShieldCheckIcon, HeartIcon, SparklesIcon, CloudDownloadIcon, ComputerDesktopIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [heroRef, heroInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [featuresRef, featuresInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [productsRef, productsInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n  const testimonials = [{\n    id: 1,\n    name: 'Sarah Johnson',\n    role: 'Verified Customer',\n    content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n  }, {\n    id: 2,\n    name: 'Mike Chen',\n    role: 'Tech Enthusiast',\n    content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n  }, {\n    id: 3,\n    name: 'Emily Davis',\n    role: 'Regular Shopper',\n    content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n  }];\n  const features = [{\n    icon: TruckIcon,\n    title: 'Free Shipping',\n    description: 'Free delivery on orders over $50'\n  }, {\n    icon: ShieldCheckIcon,\n    title: 'Secure Payment',\n    description: '100% secure payment processing'\n  }, {\n    icon: HeartIcon,\n    title: '24/7 Support',\n    description: 'Round-the-clock customer service'\n  }, {\n    icon: SparklesIcon,\n    title: 'Premium Quality',\n    description: 'Only the finest products'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(motion.section, {\n      ref: heroRef,\n      initial: {\n        opacity: 0\n      },\n      animate: heroInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 1\n      },\n      className: \"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: -100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Discover Amazing\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-light-orange-100 mb-8 leading-relaxed\",\n              children: \"Shop the latest trends with unbeatable prices and premium quality. Your perfect shopping experience starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                children: \"Shop Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                className: \"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\",\n                children: \"Learn More\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: 100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\",\n                alt: \"Shopping Experience\",\n                className: \"rounded-2xl shadow-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: featuresRef,\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: featuresInView ? {\n        opacity: 1,\n        y: 0\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Us?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"We're committed to providing you with the best shopping experience possible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: featuresInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-10 h-10 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: productsRef,\n      initial: {\n        opacity: 0\n      },\n      animate: productsInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-gradient-to-br from-light-orange-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover our handpicked selection of premium products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: featuredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: productsInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: product.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  className: \"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-5 h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-light-orange-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-semibold\",\n                  children: [\"Save $\", (product.originalPrice - product.price).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Customers Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Don't just take our word for it - hear from our satisfied customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: testimonial.avatar,\n                alt: testimonial.name,\n                className: \"w-12 h-12 rounded-full mr-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: testimonial.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex mb-4\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-5 h-5 text-yellow-400\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: [\"\\\"\", testimonial.content, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, testimonial.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"upm06Umj9fFXLKVrW1uYhY0asgA=\", false, function () {\n  return [useInView, useInView, useInView];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "motion", "useInView", "Link", "ShoppingBagIcon", "StarIcon", "TruckIcon", "ShieldCheckIcon", "HeartIcon", "SparklesIcon", "CloudDownloadIcon", "ComputerDesktopIcon", "StarIconSolid", "getFeaturedProducts", "getDigitalProducts", "jsxDEV", "_jsxDEV", "HomePage", "_s", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "featuresRef", "featuresInView", "productsRef", "productsInView", "featuredProducts", "slice", "digitalProducts", "testimonials", "id", "name", "role", "content", "rating", "avatar", "features", "icon", "title", "description", "className", "children", "section", "ref", "initial", "opacity", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "x", "delay", "button", "whileHover", "scale", "whileTap", "src", "alt", "y", "map", "feature", "index", "product", "image", "badge", "Array", "_", "i", "Math", "floor", "reviews", "price", "originalPrice", "toFixed", "testimonial", "whileInView", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport {\n  ShoppingBagIcon,\n  StarIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  SparklesIcon,\n  CloudDownloadIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\n\nconst HomePage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [productsRef, productsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n\n  const testimonials = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      role: 'Verified Customer',\n      content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n    },\n    {\n      id: 2,\n      name: 'Mike Chen',\n      role: 'Tech Enthusiast',\n      content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n    },\n    {\n      id: 3,\n      name: 'Emily Davis',\n      role: 'Regular Shopper',\n      content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n    }\n  ];\n\n  const features = [\n    {\n      icon: TruckIcon,\n      title: 'Free Shipping',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Secure Payment',\n      description: '100% secure payment processing'\n    },\n    {\n      icon: HeartIcon,\n      title: '24/7 Support',\n      description: 'Round-the-clock customer service'\n    },\n    {\n      icon: SparklesIcon,\n      title: 'Premium Quality',\n      description: 'Only the finest products'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                Discover Amazing\n                <span className=\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n                  Products\n                </span>\n              </h1>\n              <p className=\"text-xl text-light-orange-100 mb-8 leading-relaxed\">\n                Shop the latest trends with unbeatable prices and premium quality. \n                Your perfect shopping experience starts here.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  Shop Now\n                </motion.button>\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\"\n                >\n                  Learn More\n                </motion.button>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\"\n                  alt=\"Shopping Experience\"\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section\n        ref={featuresRef}\n        initial={{ opacity: 0, y: 50 }}\n        animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We're committed to providing you with the best shopping experience possible\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <feature.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Products Section */}\n      <motion.section\n        ref={productsRef}\n        initial={{ opacity: 0 }}\n        animate={productsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of premium products\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                animate={productsInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.image}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      {product.badge}\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n                    >\n                      <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-light-orange-600\">\n                        ${product.price}\n                      </span>\n                      <span className=\"text-lg text-gray-500 line-through\">\n                        ${product.originalPrice}\n                      </span>\n                    </div>\n                    <span className=\"text-sm text-green-600 font-semibold\">\n                      Save ${(product.originalPrice - product.price).toFixed(2)}\n                    </span>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <ShoppingBagIcon className=\"w-5 h-5\" />\n                    <span>Add to Cart</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Customers Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it - hear from our satisfied customers\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <img\n                    src={testimonial.avatar}\n                    alt={testimonial.name}\n                    className=\"w-12 h-12 rounded-full mr-4\"\n                  />\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <StarIconSolid key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-700 italic\">\"{testimonial.content}\"</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,eAAe,EACfC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,YAAY,EACZC,iBAAiB,EACjBC,mBAAmB,QACd,6BAA6B;AACpC,SAASN,QAAQ,IAAIO,aAAa,QAAQ,2BAA2B;AACrE,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,SAAS,CAAC;IAAEmB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,SAAS,CAAC;IAAEmB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGxB,SAAS,CAAC;IAAEmB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAEtF,MAAMK,gBAAgB,GAAGd,mBAAmB,CAAC,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGf,kBAAkB,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExD,MAAME,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,4FAA4F;IACrGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,0FAA0F;IACnGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,gGAAgG;IACzGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEhC,SAAS;IACfiC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/B,eAAe;IACrBgC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE9B,SAAS;IACf+B,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE7B,YAAY;IAClB8B,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B1B,OAAA,CAACf,MAAM,CAAC0C,OAAO;MACbC,GAAG,EAAEzB,OAAQ;MACb0B,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE3B,UAAU,GAAG;QAAE0B,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAE;MAC5BR,SAAS,EAAC,2GAA2G;MAAAC,QAAA,gBAGrH1B,OAAA;QAAKyB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1B,OAAA;UAAKyB,SAAS,EAAC;QAAqF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3GrC,OAAA;UAAKyB,SAAS,EAAC;QAAuF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7GrC,OAAA;UAAKyB,SAAS,EAAC;QAAwF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eAENrC,OAAA;QAAKyB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7E1B,OAAA;UAAKyB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClE1B,OAAA,CAACf,MAAM,CAACqD,GAAG;YACTT,OAAO,EAAE;cAAEU,CAAC,EAAE,CAAC,GAAG;cAAET,OAAO,EAAE;YAAE,CAAE;YACjCC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAAAd,QAAA,gBAE1C1B,OAAA;cAAIyB,SAAS,EAAC,8DAA8D;cAAAC,QAAA,GAAC,kBAE3E,eAAA1B,OAAA;gBAAMyB,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAErG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLrC,OAAA;cAAGyB,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAGlE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrC,OAAA;cAAKyB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C1B,OAAA,CAACf,MAAM,CAACwD,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BlB,SAAS,EAAC,mIAAmI;gBAAAC,QAAA,EAC9I;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChBrC,OAAA,CAACf,MAAM,CAACwD,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BlB,SAAS,EAAC,sJAAsJ;gBAAAC,QAAA,EACjK;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrC,OAAA,CAACf,MAAM,CAACqD,GAAG;YACTT,OAAO,EAAE;cAAEU,CAAC,EAAE,GAAG;cAAET,OAAO,EAAE;YAAE,CAAE;YAChCC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAC1Cf,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB1B,OAAA;cAAKyB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B1B,OAAA;gBACE6C,GAAG,EAAC,oEAAoE;gBACxEC,GAAG,EAAC,qBAAqB;gBACzBrB,SAAS,EAAC;cAAwB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA;cAAKyB,SAAS,EAAC;YAA+G;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACf,MAAM,CAAC0C,OAAO;MACbC,GAAG,EAAErB,WAAY;MACjBsB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAG,CAAE;MAC/BhB,OAAO,EAAEvB,cAAc,GAAG;QAAEsB,OAAO,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MACpDf,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAE1B1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEL,QAAQ,CAAC2B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BlD,OAAA,CAACf,MAAM,CAACqD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/BhB,OAAO,EAAEvB,cAAc,GAAG;cAAEsB,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDf,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B1B,OAAA;cAAKyB,SAAS,EAAC,0LAA0L;cAAAC,QAAA,eACvM1B,OAAA,CAACiD,OAAO,CAAC3B,IAAI;gBAACG,SAAS,EAAC;cAAiC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNrC,OAAA;cAAIyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEuB,OAAO,CAAC1B;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ErC,OAAA;cAAGyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEuB,OAAO,CAACzB;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAVjDa,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACf,MAAM,CAAC0C,OAAO;MACbC,GAAG,EAAEnB,WAAY;MACjBoB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAErB,cAAc,GAAG;QAAEoB,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC9CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eAEjE1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEf,gBAAgB,CAACqC,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBACnClD,OAAA,CAACf,MAAM,CAACqD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/BhB,OAAO,EAAErB,cAAc,GAAG;cAAEoB,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDf,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDR,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBtB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,gBAE/E1B,OAAA;cAAKyB,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1B,OAAA;gBACE6C,GAAG,EAAEM,OAAO,CAACC,KAAM;gBACnBN,GAAG,EAAEK,OAAO,CAACnC,IAAK;gBAClBS,SAAS,EAAC;cAAkF;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACFrC,OAAA;gBAAKyB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpC1B,OAAA;kBAAMyB,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAC1FyB,OAAO,CAACE;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrC1B,OAAA,CAACf,MAAM,CAACwD,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAI,CAAE;kBACzBlB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAE7D1B,OAAA,CAACR,SAAS;oBAACiC,SAAS,EAAC;kBAAuB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKyB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB1B,OAAA;gBAAIyB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEyB,OAAO,CAACnC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5ErC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC1B,OAAA;kBAAKyB,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG4B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACP,OAAO,CAAChC,MAAM,CAAC,gBAC5BnB,OAAA,CAACJ,aAAa;oBAAS6B,SAAS,EAAC;kBAAyB,GAAtC+B,CAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7DrC,OAAA,CAACX,QAAQ;oBAASoC,SAAS,EAAC;kBAAuB,GAApC+B,CAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrC,OAAA;kBAAMyB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzCyB,OAAO,CAAChC,MAAM,EAAC,IAAE,EAACgC,OAAO,CAACQ,OAAO,EAAC,WACrC;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA;gBAAKyB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1B,OAAA;kBAAKyB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1B,OAAA;oBAAMyB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,GACxD,EAACyB,OAAO,CAACS,KAAK;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACPrC,OAAA;oBAAMyB,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAACyB,OAAO,CAACU,aAAa;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrC,OAAA;kBAAMyB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GAAC,QAC/C,EAAC,CAACyB,OAAO,CAACU,aAAa,GAAGV,OAAO,CAACS,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA,CAACf,MAAM,CAACwD,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BlB,SAAS,EAAC,yOAAyO;gBAAAC,QAAA,gBAEnP1B,OAAA,CAACZ,eAAe;kBAACqC,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCrC,OAAA;kBAAA0B,QAAA,EAAM;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,GArEDc,OAAO,CAACpC,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsEL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA;MAASyB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDZ,YAAY,CAACkC,GAAG,CAAC,CAACe,WAAW,EAAEb,KAAK,kBACnClD,OAAA,CAACf,MAAM,CAACqD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/BiB,WAAW,EAAE;cAAElC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAClCf,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDzB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBAErF1B,OAAA;cAAKyB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC1B,OAAA;gBACE6C,GAAG,EAAEkB,WAAW,CAAC3C,MAAO;gBACxB0B,GAAG,EAAEiB,WAAW,CAAC/C,IAAK;gBACtBS,SAAS,EAAC;cAA6B;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACFrC,OAAA;gBAAA0B,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEqC,WAAW,CAAC/C;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErC,OAAA;kBAAGyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEqC,WAAW,CAAC9C;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKyB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,GAAG4B,KAAK,CAACS,WAAW,CAAC5C,MAAM,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAACO,CAAC,EAAEC,CAAC,kBACvCxD,OAAA,CAACJ,aAAa;gBAAS6B,SAAS,EAAC;cAAyB,GAAtC+B,CAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuC,CAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrC,OAAA;cAAGyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,IAAC,EAACqC,WAAW,CAAC7C,OAAO,EAAC,IAAC;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAxB1D0B,WAAW,CAAChD,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CAvTID,QAAQ;EAAA,QACkBf,SAAS,EACDA,SAAS,EACTA,SAAS;AAAA;AAAA+E,EAAA,GAH3ChE,QAAQ;AAyTd,eAAeA,QAAQ;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}