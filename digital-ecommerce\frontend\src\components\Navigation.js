import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bars3Icon, 
  XMarkIcon, 
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  UserIcon,
  HeartIcon,
  HomeIcon,
  TagIcon,
  PhoneIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Products', href: '/products', icon: TagIcon },
    { name: 'Digital', href: '/digital-products', icon: TagIcon },
    { name: 'About', href: '/about', icon: InformationCircleIcon },
    { name: 'Contact', href: '/contact', icon: PhoneIcon }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <>
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg' 
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center"
              >
                <ShoppingBagIcon className="w-6 h-6 text-white" />
              </motion.div>
              <span className={`text-2xl font-bold transition-colors duration-300 ${
                isScrolled ? 'text-gray-900' : 'text-white'
              }`}>
                ShopHub
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${
                    isActive(item.href)
                      ? isScrolled 
                        ? 'text-light-orange-600' 
                        : 'text-yellow-300'
                      : isScrolled 
                        ? 'text-gray-700 hover:text-light-orange-600' 
                        : 'text-white hover:text-yellow-300'
                  }`}
                >
                  {item.name}
                  {isActive(item.href) && (
                    <motion.div
                      layoutId="activeTab"
                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${
                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'
                      }`}
                    />
                  )}
                </Link>
              ))}
            </div>

            {/* Search Bar */}
            <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className={`h-5 w-5 ${
                    isScrolled ? 'text-gray-400' : 'text-white/70'
                  }`} />
                </div>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${
                    isScrolled
                      ? 'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300'
                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'
                  }`}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className={`p-2 rounded-full transition-colors duration-300 ${
                  isScrolled 
                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' 
                    : 'text-white hover:text-yellow-300 hover:bg-white/10'
                }`}
              >
                <HeartIcon className="w-6 h-6" />
              </motion.button>

              <Link to="/account">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={`p-2 rounded-full transition-colors duration-300 ${
                    isScrolled 
                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' 
                      : 'text-white hover:text-yellow-300 hover:bg-white/10'
                  }`}
                >
                  <UserIcon className="w-6 h-6" />
                </motion.button>
              </Link>

              <Link to="/cart">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={`relative p-2 rounded-full transition-colors duration-300 ${
                    isScrolled 
                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' 
                      : 'text-white hover:text-yellow-300 hover:bg-white/10'
                  }`}
                >
                  <ShoppingBagIcon className="w-6 h-6" />
                  <span className="absolute -top-1 -right-1 bg-light-orange-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                    3
                  </span>
                </motion.button>
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsOpen(!isOpen)}
                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${
                  isScrolled 
                    ? 'text-gray-700 hover:text-light-orange-600' 
                    : 'text-white hover:text-yellow-300'
                }`}
              >
                {isOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-200"
            >
              <div className="px-4 py-6 space-y-4">
                {/* Mobile Search */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-100 text-gray-900 placeholder-gray-500 rounded-lg focus:bg-white focus:ring-2 focus:ring-light-orange-300"
                  />
                </div>

                {/* Mobile Navigation Links */}
                <div className="space-y-2">
                  {navigationItems.map((item) => (
                    <Link
                      key={item.name}
                      to={item.href}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${
                        isActive(item.href)
                          ? 'bg-light-orange-100 text-light-orange-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.name}</span>
                    </Link>
                  ))}
                </div>

                {/* Mobile Action Buttons */}
                <div className="flex items-center justify-around pt-4 border-t border-gray-200">
                  <button className="flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600">
                    <HeartIcon className="w-6 h-6" />
                    <span className="text-xs">Wishlist</span>
                  </button>
                  <Link to="/account" className="flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600">
                    <UserIcon className="w-6 h-6" />
                    <span className="text-xs">Account</span>
                  </Link>
                  <Link to="/cart" className="flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600">
                    <div className="relative">
                      <ShoppingBagIcon className="w-6 h-6" />
                      <span className="absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center">
                        3
                      </span>
                    </div>
                    <span className="text-xs">Cart</span>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Spacer to prevent content from hiding behind fixed nav */}
      <div className="h-16 lg:h-20"></div>
    </>
  );
};

export default Navigation;
