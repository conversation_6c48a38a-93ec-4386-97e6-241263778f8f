{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport { ShoppingBagIcon, StarIcon, TruckIcon, ShieldCheckIcon, HeartIcon, SparklesIcon, ArrowDownTrayIcon as CloudDownloadIcon, ComputerDesktopIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [heroRef, heroInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [featuresRef, featuresInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [productsRef, productsInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n  const testimonials = [{\n    id: 1,\n    name: 'Sarah Johnson',\n    role: 'Verified Customer',\n    content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n  }, {\n    id: 2,\n    name: 'Mike Chen',\n    role: 'Tech Enthusiast',\n    content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n  }, {\n    id: 3,\n    name: 'Emily Davis',\n    role: 'Regular Shopper',\n    content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n    rating: 5,\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n  }];\n  const features = [{\n    icon: TruckIcon,\n    title: 'Free Shipping',\n    description: 'Free delivery on orders over $50'\n  }, {\n    icon: ShieldCheckIcon,\n    title: 'Secure Payment',\n    description: '100% secure payment processing'\n  }, {\n    icon: HeartIcon,\n    title: '24/7 Support',\n    description: 'Round-the-clock customer service'\n  }, {\n    icon: SparklesIcon,\n    title: 'Premium Quality',\n    description: 'Only the finest products'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(motion.section, {\n      ref: heroRef,\n      initial: {\n        opacity: 0\n      },\n      animate: heroInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 1\n      },\n      className: \"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: -100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Discover Amazing\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-light-orange-100 mb-8 leading-relaxed\",\n              children: \"Shop the latest trends with unbeatable prices and premium quality. Your perfect shopping experience starts here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  children: \"Shop Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/digital-products\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\",\n                  children: \"Digital Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: 100,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              x: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\",\n                alt: \"Shopping Experience\",\n                className: \"rounded-2xl shadow-2xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: featuresRef,\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: featuresInView ? {\n        opacity: 1,\n        y: 0\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Why Choose Us?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"We're committed to providing you with the best shopping experience possible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: featuresInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"w-10 h-10 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: productsRef,\n      initial: {\n        opacity: 0\n      },\n      animate: productsInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-gradient-to-br from-light-orange-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover our handpicked selection of premium products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: featuredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: productsInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images ? product.images[0] : product.image,\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: product.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  className: \"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",\n                  children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                    className: \"w-5 h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-light-orange-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 font-semibold\",\n                  children: [\"Save $\", (product.originalPrice - product.price).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      initial: {\n        opacity: 0\n      },\n      whileInView: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.8\n      },\n      viewport: {\n        once: true\n      },\n      className: \"py-20 bg-gradient-to-br from-blue-50 to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Digital Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Instant access to software, games, and digital content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: digitalProducts.map((product, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              y: -10\n            },\n            className: \"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.images[0],\n                alt: product.name,\n                className: \"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                  children: \"Digital\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                    className: \"w-3 h-3 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), \"Instant\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex\",\n                  children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                    className: \"w-4 h-4 text-yellow-400\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"w-4 h-4 text-gray-300\"\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 ml-2\",\n                  children: [product.rating, \" (\", product.reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl font-bold text-blue-600\",\n                    children: [\"$\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg text-gray-500 line-through\",\n                    children: [\"$\", product.originalPrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                className: \"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(CloudDownloadIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Get Instantly\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/digital-products\",\n            children: /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n              children: [/*#__PURE__*/_jsxDEV(ComputerDesktopIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View All Digital Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"What Our Customers Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Don't just take our word for it - hear from our satisfied customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: testimonial.avatar,\n                alt: testimonial.name,\n                className: \"w-12 h-12 rounded-full mr-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: testimonial.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: testimonial.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex mb-4\",\n              children: [...Array(testimonial.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-5 h-5 text-yellow-400\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 italic\",\n              children: [\"\\\"\", testimonial.content, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, testimonial.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"upm06Umj9fFXLKVrW1uYhY0asgA=\", false, function () {\n  return [useInView, useInView, useInView];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "motion", "useInView", "Link", "ShoppingBagIcon", "StarIcon", "TruckIcon", "ShieldCheckIcon", "HeartIcon", "SparklesIcon", "ArrowDownTrayIcon", "CloudDownloadIcon", "ComputerDesktopIcon", "StarIconSolid", "getFeaturedProducts", "getDigitalProducts", "jsxDEV", "_jsxDEV", "HomePage", "_s", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "featuresRef", "featuresInView", "productsRef", "productsInView", "featuredProducts", "slice", "digitalProducts", "testimonials", "id", "name", "role", "content", "rating", "avatar", "features", "icon", "title", "description", "className", "children", "section", "ref", "initial", "opacity", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "x", "delay", "to", "button", "whileHover", "scale", "whileTap", "src", "alt", "y", "map", "feature", "index", "product", "images", "image", "badge", "Array", "_", "i", "Math", "floor", "reviews", "price", "originalPrice", "toFixed", "whileInView", "viewport", "once", "testimonial", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport {\n  ShoppingBagIcon,\n  StarIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  SparklesIcon,\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\n\nconst HomePage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [productsRef, productsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n\n  const testimonials = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      role: 'Verified Customer',\n      content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n    },\n    {\n      id: 2,\n      name: 'Mike Chen',\n      role: 'Tech Enthusiast',\n      content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n    },\n    {\n      id: 3,\n      name: 'Emily Davis',\n      role: 'Regular Shopper',\n      content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n    }\n  ];\n\n  const features = [\n    {\n      icon: TruckIcon,\n      title: 'Free Shipping',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Secure Payment',\n      description: '100% secure payment processing'\n    },\n    {\n      icon: HeartIcon,\n      title: '24/7 Support',\n      description: 'Round-the-clock customer service'\n    },\n    {\n      icon: SparklesIcon,\n      title: 'Premium Quality',\n      description: 'Only the finest products'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                Discover Amazing\n                <span className=\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n                  Products\n                </span>\n              </h1>\n              <p className=\"text-xl text-light-orange-100 mb-8 leading-relaxed\">\n                Shop the latest trends with unbeatable prices and premium quality. \n                Your perfect shopping experience starts here.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link to=\"/products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                  >\n                    Shop Now\n                  </motion.button>\n                </Link>\n                <Link to=\"/digital-products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\"\n                  >\n                    Digital Products\n                  </motion.button>\n                </Link>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\"\n                  alt=\"Shopping Experience\"\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section\n        ref={featuresRef}\n        initial={{ opacity: 0, y: 50 }}\n        animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We're committed to providing you with the best shopping experience possible\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <feature.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Products Section */}\n      <motion.section\n        ref={productsRef}\n        initial={{ opacity: 0 }}\n        animate={productsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of premium products\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                animate={productsInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images ? product.images[0] : product.image}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      {product.badge}\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n                    >\n                      <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-light-orange-600\">\n                        ${product.price}\n                      </span>\n                      <span className=\"text-lg text-gray-500 line-through\">\n                        ${product.originalPrice}\n                      </span>\n                    </div>\n                    <span className=\"text-sm text-green-600 font-semibold\">\n                      Save ${(product.originalPrice - product.price).toFixed(2)}\n                    </span>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <ShoppingBagIcon className=\"w-5 h-5\" />\n                    <span>Add to Cart</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Digital Products Section */}\n      <motion.section\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Digital Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Instant access to software, games, and digital content\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {digitalProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images[0]}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      Digital\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n                      <CloudDownloadIcon className=\"w-3 h-3 mr-1\" />\n                      Instant\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-blue-600\">\n                        ${product.price}\n                      </span>\n                      {product.originalPrice && product.originalPrice > product.price && (\n                        <span className=\"text-lg text-gray-500 line-through\">\n                          ${product.originalPrice}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <CloudDownloadIcon className=\"w-5 h-5\" />\n                    <span>Get Instantly</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link to=\"/digital-products\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\"\n              >\n                <ComputerDesktopIcon className=\"w-6 h-6\" />\n                <span>View All Digital Products</span>\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Customers Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it - hear from our satisfied customers\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <img\n                    src={testimonial.avatar}\n                    alt={testimonial.name}\n                    className=\"w-12 h-12 rounded-full mr-4\"\n                  />\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <StarIconSolid key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-700 italic\">\"{testimonial.content}\"</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,eAAe,EACfC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,YAAY,EACZC,iBAAiB,IAAIC,iBAAiB,EACtCC,mBAAmB,QACd,6BAA6B;AACpC,SAASP,QAAQ,IAAIQ,aAAa,QAAQ,2BAA2B;AACrE,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,SAAS,CAAC;IAAEoB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,SAAS,CAAC;IAAEoB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EACtF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGzB,SAAS,CAAC;IAAEoB,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAEtF,MAAMK,gBAAgB,GAAGd,mBAAmB,CAAC,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGf,kBAAkB,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExD,MAAME,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,4FAA4F;IACrGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,0FAA0F;IACnGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,gGAAgG;IACzGC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEjC,SAAS;IACfkC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhC,eAAe;IACrBiC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/B,SAAS;IACfgC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE9B,YAAY;IAClB+B,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B1B,OAAA,CAAChB,MAAM,CAAC2C,OAAO;MACbC,GAAG,EAAEzB,OAAQ;MACb0B,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE3B,UAAU,GAAG;QAAE0B,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAE;MAC5BR,SAAS,EAAC,2GAA2G;MAAAC,QAAA,gBAGrH1B,OAAA;QAAKyB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1B,OAAA;UAAKyB,SAAS,EAAC;QAAqF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3GrC,OAAA;UAAKyB,SAAS,EAAC;QAAuF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7GrC,OAAA;UAAKyB,SAAS,EAAC;QAAwF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eAENrC,OAAA;QAAKyB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7E1B,OAAA;UAAKyB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClE1B,OAAA,CAAChB,MAAM,CAACsD,GAAG;YACTT,OAAO,EAAE;cAAEU,CAAC,EAAE,CAAC,GAAG;cAAET,OAAO,EAAE;YAAE,CAAE;YACjCC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAAAd,QAAA,gBAE1C1B,OAAA;cAAIyB,SAAS,EAAC,8DAA8D;cAAAC,QAAA,GAAC,kBAE3E,eAAA1B,OAAA;gBAAMyB,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAErG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLrC,OAAA;cAAGyB,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAGlE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrC,OAAA;cAAKyB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C1B,OAAA,CAACd,IAAI;gBAACuD,EAAE,EAAC,WAAW;gBAAAf,QAAA,eAClB1B,OAAA,CAAChB,MAAM,CAAC0D,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BnB,SAAS,EAAC,mIAAmI;kBAAAC,QAAA,EAC9I;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACPrC,OAAA,CAACd,IAAI;gBAACuD,EAAE,EAAC,mBAAmB;gBAAAf,QAAA,eAC1B1B,OAAA,CAAChB,MAAM,CAAC0D,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BnB,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EACjK;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrC,OAAA,CAAChB,MAAM,CAACsD,GAAG;YACTT,OAAO,EAAE;cAAEU,CAAC,EAAE,GAAG;cAAET,OAAO,EAAE;YAAE,CAAE;YAChCC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAC1Cf,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB1B,OAAA;cAAKyB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B1B,OAAA;gBACE8C,GAAG,EAAC,oEAAoE;gBACxEC,GAAG,EAAC,qBAAqB;gBACzBtB,SAAS,EAAC;cAAwB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrC,OAAA;cAAKyB,SAAS,EAAC;YAA+G;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAAChB,MAAM,CAAC2C,OAAO;MACbC,GAAG,EAAErB,WAAY;MACjBsB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEkB,CAAC,EAAE;MAAG,CAAE;MAC/BjB,OAAO,EAAEvB,cAAc,GAAG;QAAEsB,OAAO,EAAE,CAAC;QAAEkB,CAAC,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MACpDhB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAE1B1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEL,QAAQ,CAAC4B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BnD,OAAA,CAAChB,MAAM,CAACsD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAG,CAAE;YAC/BjB,OAAO,EAAEvB,cAAc,GAAG;cAAEsB,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDhB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClD1B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B1B,OAAA;cAAKyB,SAAS,EAAC,0LAA0L;cAAAC,QAAA,eACvM1B,OAAA,CAACkD,OAAO,CAAC5B,IAAI;gBAACG,SAAS,EAAC;cAAiC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNrC,OAAA;cAAIyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEwB,OAAO,CAAC3B;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ErC,OAAA;cAAGyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEwB,OAAO,CAAC1B;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAVjDc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAAChB,MAAM,CAAC2C,OAAO;MACbC,GAAG,EAAEnB,WAAY;MACjBoB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAErB,cAAc,GAAG;QAAEoB,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC9CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eAEjE1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEf,gBAAgB,CAACsC,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBACnCnD,OAAA,CAAChB,MAAM,CAACsD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAG,CAAE;YAC/BjB,OAAO,EAAErB,cAAc,GAAG;cAAEoB,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACpDhB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDR,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBvB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,gBAE/E1B,OAAA;cAAKyB,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1B,OAAA;gBACE8C,GAAG,EAAEM,OAAO,CAACC,MAAM,GAAGD,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACE,KAAM;gBACxDP,GAAG,EAAEK,OAAO,CAACpC,IAAK;gBAClBS,SAAS,EAAC;cAAkF;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACFrC,OAAA;gBAAKyB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpC1B,OAAA;kBAAMyB,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAC1F0B,OAAO,CAACG;gBAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrC1B,OAAA,CAAChB,MAAM,CAAC0D,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAI,CAAE;kBAC3BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAI,CAAE;kBACzBnB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAE7D1B,OAAA,CAACT,SAAS;oBAACkC,SAAS,EAAC;kBAAuB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKyB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB1B,OAAA;gBAAIyB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAE0B,OAAO,CAACpC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5ErC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC1B,OAAA;kBAAKyB,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,GAAG,CAAC,CAACQ,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACR,OAAO,CAACjC,MAAM,CAAC,gBAC5BnB,OAAA,CAACJ,aAAa;oBAAS6B,SAAS,EAAC;kBAAyB,GAAtCiC,CAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7DrC,OAAA,CAACZ,QAAQ;oBAASqC,SAAS,EAAC;kBAAuB,GAApCiC,CAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrC,OAAA;kBAAMyB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzC0B,OAAO,CAACjC,MAAM,EAAC,IAAE,EAACiC,OAAO,CAACS,OAAO,EAAC,WACrC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA;gBAAKyB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1B,OAAA;kBAAKyB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1B,OAAA;oBAAMyB,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,GACxD,EAAC0B,OAAO,CAACU,KAAK;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACPrC,OAAA;oBAAMyB,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAC0B,OAAO,CAACW,aAAa;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNrC,OAAA;kBAAMyB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GAAC,QAC/C,EAAC,CAAC0B,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACU,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA,CAAChB,MAAM,CAAC0D,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BnB,SAAS,EAAC,yOAAyO;gBAAAC,QAAA,gBAEnP1B,OAAA,CAACb,eAAe;kBAACsC,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCrC,OAAA;kBAAA0B,QAAA,EAAM;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,GArEDe,OAAO,CAACrC,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsEL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAAChB,MAAM,CAAC2C,OAAO;MACbE,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBmC,WAAW,EAAE;QAAEnC,OAAO,EAAE;MAAE,CAAE;MAC5BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BiC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAE;MACzB1C,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAE7D1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDb,eAAe,CAACoC,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBAClCnD,OAAA,CAAChB,MAAM,CAACsD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAG,CAAE;YAC/BiB,WAAW,EAAE;cAAEnC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAE,CAAE;YAClChB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClDe,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,UAAU,EAAE;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YACvBvB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,gBAE/E1B,OAAA;cAAKyB,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1B,OAAA;gBACE8C,GAAG,EAAEM,OAAO,CAACC,MAAM,CAAC,CAAC,CAAE;gBACvBN,GAAG,EAAEK,OAAO,CAACpC,IAAK;gBAClBS,SAAS,EAAC;cAAkF;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,eACFrC,OAAA;gBAAKyB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpC1B,OAAA;kBAAMyB,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAEtF;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrC1B,OAAA;kBAAMyB,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,gBACjG1B,OAAA,CAACN,iBAAiB;oBAAC+B,SAAS,EAAC;kBAAc;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKyB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB1B,OAAA;gBAAIyB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAE0B,OAAO,CAACpC;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE5ErC,OAAA;gBAAKyB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC1B,OAAA;kBAAKyB,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAAC,GAAG8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,GAAG,CAAC,CAACQ,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACR,OAAO,CAACjC,MAAM,CAAC,gBAC5BnB,OAAA,CAACJ,aAAa;oBAAS6B,SAAS,EAAC;kBAAyB,GAAtCiC,CAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuC,CAAC,gBAE7DrC,OAAA,CAACZ,QAAQ;oBAASqC,SAAS,EAAC;kBAAuB,GAApCiC,CAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAExD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNrC,OAAA;kBAAMyB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzC0B,OAAO,CAACjC,MAAM,EAAC,IAAE,EAACiC,OAAO,CAACS,OAAO,EAAC,WACrC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAENrC,OAAA;gBAAKyB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrD1B,OAAA;kBAAKyB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1B,OAAA;oBAAMyB,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,GAAC,GAChD,EAAC0B,OAAO,CAACU,KAAK;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,EACNe,OAAO,CAACW,aAAa,IAAIX,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACU,KAAK,iBAC7D9D,OAAA;oBAAMyB,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAClD,EAAC0B,OAAO,CAACW,aAAa;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrC,OAAA,CAAChB,MAAM,CAAC0D,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BnB,SAAS,EAAC,yMAAyM;gBAAAC,QAAA,gBAEnN1B,OAAA,CAACN,iBAAiB;kBAAC+B,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzCrC,OAAA;kBAAA0B,QAAA,EAAM;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,GAlEDe,OAAO,CAACrC,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmEL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC1B,OAAA,CAACd,IAAI;YAACuD,EAAE,EAAC,mBAAmB;YAAAf,QAAA,eAC1B1B,OAAA,CAAChB,MAAM,CAAC0D,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BnB,SAAS,EAAC,gMAAgM;cAAAC,QAAA,gBAE1M1B,OAAA,CAACL,mBAAmB;gBAAC8B,SAAS,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CrC,OAAA;gBAAA0B,QAAA,EAAM;cAAyB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA;MAASyB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDZ,YAAY,CAACmC,GAAG,CAAC,CAACmB,WAAW,EAAEjB,KAAK,kBACnCnD,OAAA,CAAChB,MAAM,CAACsD,GAAG;YAETT,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAG,CAAE;YAC/BiB,WAAW,EAAE;cAAEnC,OAAO,EAAE,CAAC;cAAEkB,CAAC,EAAE;YAAE,CAAE;YAClChB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClD1B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBAErF1B,OAAA;cAAKyB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC1B,OAAA;gBACE8C,GAAG,EAAEsB,WAAW,CAAChD,MAAO;gBACxB2B,GAAG,EAAEqB,WAAW,CAACpD,IAAK;gBACtBS,SAAS,EAAC;cAA6B;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACFrC,OAAA;gBAAA0B,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAE0C,WAAW,CAACpD;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErC,OAAA;kBAAGyB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE0C,WAAW,CAACnD;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA;cAAKyB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,GAAG8B,KAAK,CAACY,WAAW,CAACjD,MAAM,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAACQ,CAAC,EAAEC,CAAC,kBACvC1D,OAAA,CAACJ,aAAa;gBAAS6B,SAAS,EAAC;cAAyB,GAAtCiC,CAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuC,CAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrC,OAAA;cAAGyB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,IAAC,EAAC0C,WAAW,CAAClD,OAAO,EAAC,IAAC;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAxB1D+B,WAAW,CAACrD,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBT,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CAtaID,QAAQ;EAAA,QACkBhB,SAAS,EACDA,SAAS,EACTA,SAAS;AAAA;AAAAoF,EAAA,GAH3CpE,QAAQ;AAwad,eAAeA,QAAQ;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}