{"ast": null, "code": "const isCSSVar = name => name.startsWith(\"--\");\nexport { isCSSVar };", "map": {"version": 3, "names": ["isCSSVar", "name", "startsWith"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/render/dom/is-css-var.mjs"], "sourcesContent": ["const isCSSVar = (name) => name.startsWith(\"--\");\n\nexport { isCSSVar };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,IAAI,IAAKA,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;AAEhD,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}