import React, { useState } from 'react';
import { ViewGridIcon, ViewListIcon } from '@heroicons/react/solid';
import PropTypes from 'prop-types';

const products = [
  {
    id: 1,
    name: 'Wireless Headphones',
    price: 199.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.5,
    description: 'Premium noise-cancelling wireless headphones',
    category: 'Audio',
    fileType: 'MP3'
  },
  {
    id: 2,
    name: 'Smart Watch',
    price: 249.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.2,
    description: 'Fitness tracking smart watch with heart rate monitor',
    category: 'Wearables',
    fileType: 'PDF'
  },
  {
    id: 3,
    name: 'Bluetooth Speaker',
    price: 129.99,
    image: 'https://via.placeholder.com/300',
    rating: 4.7,
    description: 'Portable waterproof Bluetooth speaker',
    category: 'Audio',
    fileType: 'MP4'
  }
];

const ProductList = ({ products = [], isLoading = false }) => {
  const [viewType, setViewType] = useState('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 12;

  const handleViewChange = (type) => {
    setViewType(type);
  };

  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = products.slice(indexOfFirstProduct, indexOfLastProduct);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-8">
        <h3 className="text-xl text-gray-500">No products found</h3>
      </div>
    );
  }

  return (
    <section className="space-y-8 p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-light-orange-800">Featured Products</h2>
        <div className="flex space-x-3">
          <button
            onClick={() => handleViewChange('grid')}
            className={`p-3 rounded-lg ${
              viewType === 'grid'
                ? 'bg-light-orange-100 text-light-orange-800'
                : 'bg-white text-gray-500'
            } hover:bg-light-orange-200 transition-colors shadow-sm`}
          >
            <ViewGridIcon className="h-6 w-6" />
          </button>
          <button
            onClick={() => handleViewChange('list')}
            className={`p-3 rounded-lg ${
              viewType === 'list'
                ? 'bg-light-orange-100 text-light-orange-800'
                : 'bg-white text-gray-500'
            } hover:bg-light-orange-200 transition-colors shadow-sm`}
          >
            <ViewListIcon className="h-6 w-6" />
          </button>
        </div>
      </div>

      <div className={`${
        viewType === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
      }`}>
        {currentProducts.map((product) => (
          <div key={product.id} className={`${
            viewType === 'grid'
              ? 'bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow'
              : 'flex bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow'
          }`}>
            <img 
              src={product.image} 
              alt={product.name} 
              className={`${
                viewType === 'grid'
                  ? 'w-full h-48 object-cover'
                  : 'w-48 h-48 object-cover flex-none'
              }`}
            />
            <div className={`p-4 ${
              viewType === 'list' ? 'flex flex-col justify-between flex-grow' : ''
            }`}>
              <div>
                <h3 className="text-xl font-bold text-light-orange-800">{product.name}</h3>
                <p className="mt-2 text-sm text-light-orange-600">{product.description}</p>
              </div>
              <div className="flex items-center mt-3">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className={`h-5 w-5 ${i < Math.floor(product.rating) ? 'text-light-orange-400' : 'text-light-orange-100'}`} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="mt-3 text-2xl font-bold text-light-orange-700">${product.price.toFixed(2)}</p>
              <div className="mt-5 flex space-x-3">
                <button className="w-full bg-light-orange-500 text-white py-3 px-6 rounded-lg hover:bg-light-orange-600 transition-colors shadow-md">
                  Add to Cart
                </button>
                <button
                  className="p-3 bg-light-orange-50 rounded-lg hover:bg-light-orange-100 transition-colors shadow-sm"
                  title="Add to Wishlist"
                >
                  <svg className="h-6 w-6 text-light-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-center mt-10">
        <nav className="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px">
          <button
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="relative inline-flex items-center px-4 py-2 rounded-l-lg border border-light-orange-300 bg-white text-sm font-medium text-light-orange-700 hover:bg-light-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === Math.ceil(products.length / productsPerPage)}
            className="relative inline-flex items-center px-4 py-2 rounded-r-lg border border-light-orange-300 bg-white text-sm font-medium text-light-orange-700 hover:bg-light-orange-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </section>
  );
};

ProductList.propTypes = {
  products: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      price: PropTypes.number.isRequired,
      image: PropTypes.string.isRequired,
      rating: PropTypes.number.isRequired,
      description: PropTypes.string.isRequired,
      category: PropTypes.string.isRequired,
      fileType: PropTypes.string.isRequired
    })
  ),
  isLoading: PropTypes.bool
};

export default ProductList;
