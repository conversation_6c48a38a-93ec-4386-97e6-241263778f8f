{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"label\",\"error\",\"helperText\",\"required\",\"className\",\"containerClassName\",\"labelClassName\",\"type\"];import React,{forwardRef}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Input=/*#__PURE__*/forwardRef((_ref,ref)=>{let{label,error,helperText,required=false,className='',containerClassName='',labelClassName='',type='text'}=_ref,props=_objectWithoutProperties(_ref,_excluded);const baseInputClasses='w-full px-4 py-3 border rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400';const inputClasses=error?\"\".concat(baseInputClasses,\" border-red-300 text-red-900 placeholder-red-300 focus:ring-red-300 focus:border-red-400\"):\"\".concat(baseInputClasses,\" border-gray-300 text-gray-900 placeholder-gray-500 \").concat(className);return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2 \".concat(containerClassName),children:[label&&/*#__PURE__*/_jsxs(\"label\",{className:\"block text-sm font-medium text-gray-700 \".concat(labelClassName),children:[label,required&&/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500 ml-1\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",_objectSpread({ref:ref,type:type,className:inputClasses},props)),error&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-red-600 flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4 mr-1\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",clipRule:\"evenodd\"})}),error]}),helperText&&!error&&/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:helperText})]});});Input.displayName='Input';export default Input;", "map": {"version": 3, "names": ["React", "forwardRef", "jsx", "_jsx", "jsxs", "_jsxs", "Input", "_ref", "ref", "label", "error", "helperText", "required", "className", "containerClassName", "labelClassName", "type", "props", "_objectWithoutProperties", "_excluded", "baseInputClasses", "inputClasses", "concat", "children", "_objectSpread", "fill", "viewBox", "fillRule", "d", "clipRule", "displayName"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Input.js"], "sourcesContent": ["import React, { forwardRef } from 'react';\n\nconst Input = forwardRef(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  labelClassName = '',\n  type = 'text',\n  ...props\n}, ref) => {\n  const baseInputClasses = 'w-full px-4 py-3 border rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400';\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 text-red-900 placeholder-red-300 focus:ring-red-300 focus:border-red-400`\n    : `${baseInputClasses} border-gray-300 text-gray-900 placeholder-gray-500 ${className}`;\n\n  return (\n    <div className={`space-y-2 ${containerClassName}`}>\n      {label && (\n        <label className={`block text-sm font-medium text-gray-700 ${labelClassName}`}>\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        type={type}\n        className={inputClasses}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 flex items-center\">\n          <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500\">{helperText}</p>\n      )}\n    </div>\n  );\n});\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "mappings": "gcAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,KAAK,cAAGL,UAAU,CAAC,CAAAM,IAAA,CAUtBC,GAAG,GAAK,IAVe,CACxBC,KAAK,CACLC,KAAK,CACLC,UAAU,CACVC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,EAAE,CACdC,kBAAkB,CAAG,EAAE,CACvBC,cAAc,CAAG,EAAE,CACnBC,IAAI,CAAG,MAET,CAAC,CAAAT,IAAA,CADIU,KAAK,CAAAC,wBAAA,CAAAX,IAAA,CAAAY,SAAA,EAER,KAAM,CAAAC,gBAAgB,CAAG,6JAA6J,CAEtL,KAAM,CAAAC,YAAY,CAAGX,KAAK,IAAAY,MAAA,CACnBF,gBAAgB,gGAAAE,MAAA,CAChBF,gBAAgB,yDAAAE,MAAA,CAAuDT,SAAS,CAAE,CAEzF,mBACER,KAAA,QAAKQ,SAAS,cAAAS,MAAA,CAAeR,kBAAkB,CAAG,CAAAS,QAAA,EAC/Cd,KAAK,eACJJ,KAAA,UAAOQ,SAAS,4CAAAS,MAAA,CAA6CP,cAAc,CAAG,CAAAQ,QAAA,EAC3Ed,KAAK,CACLG,QAAQ,eAAIT,IAAA,SAAMU,SAAS,CAAC,mBAAmB,CAAAU,QAAA,CAAC,GAAC,CAAM,CAAC,EACpD,CACR,cAEDpB,IAAA,SAAAqB,aAAA,EACEhB,GAAG,CAAEA,GAAI,CACTQ,IAAI,CAAEA,IAAK,CACXH,SAAS,CAAEQ,YAAa,EACpBJ,KAAK,CACV,CAAC,CAEDP,KAAK,eACJL,KAAA,MAAGQ,SAAS,CAAC,wCAAwC,CAAAU,QAAA,eACnDpB,IAAA,QAAKU,SAAS,CAAC,cAAc,CAACY,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAH,QAAA,cACnEpB,IAAA,SAAMwB,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,mHAAmH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACjK,CAAC,CACLnB,KAAK,EACL,CACJ,CAEAC,UAAU,EAAI,CAACD,KAAK,eACnBP,IAAA,MAAGU,SAAS,CAAC,uBAAuB,CAAAU,QAAA,CAAEZ,UAAU,CAAI,CACrD,EACE,CAAC,CAEV,CAAC,CAAC,CAEFL,KAAK,CAACwB,WAAW,CAAG,OAAO,CAE3B,cAAe,CAAAxB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}