{"ast": null, "code": "import { warning } from 'motion-utils';\nimport { isGenerator } from '../generators/utils/is-generator.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\nfunction hasKeyframesChanged(keyframes) {\n  const current = keyframes[0];\n  if (keyframes.length === 1) return true;\n  for (let i = 0; i < keyframes.length; i++) {\n    if (keyframes[i] !== current) return true;\n  }\n}\nfunction canAnimate(keyframes, name, type, velocity) {\n  /**\n   * Check if we're able to animate between the start and end keyframes,\n   * and throw a warning if we're attempting to animate between one that's\n   * animatable and another that isn't.\n   */\n  const originKeyframe = keyframes[0];\n  if (originKeyframe === null) return false;\n  /**\n   * These aren't traditionally animatable but we do support them.\n   * In future we could look into making this more generic or replacing\n   * this function with mix() === mixImmediate\n   */\n  if (name === \"display\" || name === \"visibility\") return true;\n  const targetKeyframe = keyframes[keyframes.length - 1];\n  const isOriginAnimatable = isAnimatable(originKeyframe, name);\n  const isTargetAnimatable = isAnimatable(targetKeyframe, name);\n  warning(isOriginAnimatable === isTargetAnimatable, \"You are trying to animate \".concat(name, \" from \\\"\").concat(originKeyframe, \"\\\" to \\\"\").concat(targetKeyframe, \"\\\". \").concat(originKeyframe, \" is not an animatable value - to enable this animation set \").concat(originKeyframe, \" to a value animatable to \").concat(targetKeyframe, \" via the `style` property.\"));\n  // Always skip if any of these are true\n  if (!isOriginAnimatable || !isTargetAnimatable) {\n    return false;\n  }\n  return hasKeyframesChanged(keyframes) || (type === \"spring\" || isGenerator(type)) && velocity;\n}\nexport { canAnimate };", "map": {"version": 3, "names": ["warning", "isGenerator", "isAnimatable", "hasKeyframesChanged", "keyframes", "current", "length", "i", "canAnimate", "name", "type", "velocity", "originKeyframe", "targetKeyframe", "isOriginAnimatable", "isTargetAnimatable", "concat"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/utils/can-animate.mjs"], "sourcesContent": ["import { warning } from 'motion-utils';\nimport { isGenerator } from '../generators/utils/is-generator.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\n\nfunction hasKeyframesChanged(keyframes) {\n    const current = keyframes[0];\n    if (keyframes.length === 1)\n        return true;\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] !== current)\n            return true;\n    }\n}\nfunction canAnimate(keyframes, name, type, velocity) {\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    if (originKeyframe === null)\n        return false;\n    /**\n     * These aren't traditionally animatable but we do support them.\n     * In future we could look into making this more generic or replacing\n     * this function with mix() === mixImmediate\n     */\n    if (name === \"display\" || name === \"visibility\")\n        return true;\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(originKeyframe, name);\n    const isTargetAnimatable = isAnimatable(targetKeyframe, name);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${name} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n    // Always skip if any of these are true\n    if (!isOriginAnimatable || !isTargetAnimatable) {\n        return false;\n    }\n    return (hasKeyframesChanged(keyframes) ||\n        ((type === \"spring\" || isGenerator(type)) && velocity));\n}\n\nexport { canAnimate };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EACpC,MAAMC,OAAO,GAAGD,SAAS,CAAC,CAAC,CAAC;EAC5B,IAAIA,SAAS,CAACE,MAAM,KAAK,CAAC,EACtB,OAAO,IAAI;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACvC,IAAIH,SAAS,CAACG,CAAC,CAAC,KAAKF,OAAO,EACxB,OAAO,IAAI;EACnB;AACJ;AACA,SAASG,UAAUA,CAACJ,SAAS,EAAEK,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACjD;AACJ;AACA;AACA;AACA;EACI,MAAMC,cAAc,GAAGR,SAAS,CAAC,CAAC,CAAC;EACnC,IAAIQ,cAAc,KAAK,IAAI,EACvB,OAAO,KAAK;EAChB;AACJ;AACA;AACA;AACA;EACI,IAAIH,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,EAC3C,OAAO,IAAI;EACf,MAAMI,cAAc,GAAGT,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;EACtD,MAAMQ,kBAAkB,GAAGZ,YAAY,CAACU,cAAc,EAAEH,IAAI,CAAC;EAC7D,MAAMM,kBAAkB,GAAGb,YAAY,CAACW,cAAc,EAAEJ,IAAI,CAAC;EAC7DT,OAAO,CAACc,kBAAkB,KAAKC,kBAAkB,+BAAAC,MAAA,CAA+BP,IAAI,cAAAO,MAAA,CAAUJ,cAAc,cAAAI,MAAA,CAASH,cAAc,UAAAG,MAAA,CAAMJ,cAAc,iEAAAI,MAAA,CAA8DJ,cAAc,gCAAAI,MAAA,CAA6BH,cAAc,+BAA8B,CAAC;EAC7S;EACA,IAAI,CAACC,kBAAkB,IAAI,CAACC,kBAAkB,EAAE;IAC5C,OAAO,KAAK;EAChB;EACA,OAAQZ,mBAAmB,CAACC,SAAS,CAAC,IACjC,CAACM,IAAI,KAAK,QAAQ,IAAIT,WAAW,CAACS,IAAI,CAAC,KAAKC,QAAS;AAC9D;AAEA,SAASH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}