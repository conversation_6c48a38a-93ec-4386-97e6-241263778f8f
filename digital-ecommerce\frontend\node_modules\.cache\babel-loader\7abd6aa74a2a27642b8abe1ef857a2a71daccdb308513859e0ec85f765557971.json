{"ast": null, "code": "import { parseValueFromTransform } from '../../../render/dom/parse-transform.mjs';\nimport { transformPropOrder } from '../../../render/utils/keys-transform.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nconst isNumOrPxType = v => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: (_ref, _ref2) => {\n    let {\n      x\n    } = _ref;\n    let {\n      paddingLeft = \"0\",\n      paddingRight = \"0\"\n    } = _ref2;\n    return x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight);\n  },\n  height: (_ref3, _ref4) => {\n    let {\n      y\n    } = _ref3;\n    let {\n      paddingTop = \"0\",\n      paddingBottom = \"0\"\n    } = _ref4;\n    return y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom);\n  },\n  top: (_bbox, _ref5) => {\n    let {\n      top\n    } = _ref5;\n    return parseFloat(top);\n  },\n  left: (_bbox, _ref6) => {\n    let {\n      left\n    } = _ref6;\n    return parseFloat(left);\n  },\n  bottom: (_ref7, _ref8) => {\n    let {\n      y\n    } = _ref7;\n    let {\n      top\n    } = _ref8;\n    return parseFloat(top) + (y.max - y.min);\n  },\n  right: (_ref9, _ref0) => {\n    let {\n      x\n    } = _ref9;\n    let {\n      left\n    } = _ref0;\n    return parseFloat(left) + (x.max - x.min);\n  },\n  // Transform\n  x: (_bbox, _ref1) => {\n    let {\n      transform\n    } = _ref1;\n    return parseValueFromTransform(transform, \"x\");\n  },\n  y: (_bbox, _ref10) => {\n    let {\n      transform\n    } = _ref10;\n    return parseValueFromTransform(transform, \"y\");\n  }\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };", "map": {"version": 3, "names": ["parseValueFromTransform", "transformPropOrder", "number", "px", "isNumOrPxType", "v", "transformKeys", "Set", "nonTranslationalTransformKeys", "filter", "key", "has", "removeNonTranslationalTransform", "visualElement", "removedTransforms", "for<PERSON>ach", "value", "getValue", "undefined", "push", "get", "set", "startsWith", "positionalV<PERSON>ues", "width", "_ref", "_ref2", "x", "paddingLeft", "paddingRight", "max", "min", "parseFloat", "height", "_ref3", "_ref4", "y", "paddingTop", "paddingBottom", "top", "_bbox", "_ref5", "left", "_ref6", "bottom", "_ref7", "_ref8", "right", "_ref9", "_ref0", "_ref1", "transform", "_ref10", "translateX", "translateY"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs"], "sourcesContent": ["import { parseValueFromTransform } from '../../../render/dom/parse-transform.mjs';\nimport { transformPropOrder } from '../../../render/utils/keys-transform.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst isNumOrPxType = (v) => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: (_bbox, { transform }) => parseValueFromTransform(transform, \"x\"),\n    y: (_bbox, { transform }) => parseValueFromTransform(transform, \"y\"),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\n\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,aAAa,GAAIC,CAAC,IAAKA,CAAC,KAAKH,MAAM,IAAIG,CAAC,KAAKF,EAAE;AACrD,MAAMG,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAMC,6BAA6B,GAAGP,kBAAkB,CAACQ,MAAM,CAAEC,GAAG,IAAK,CAACJ,aAAa,CAACK,GAAG,CAACD,GAAG,CAAC,CAAC;AACjG,SAASE,+BAA+BA,CAACC,aAAa,EAAE;EACpD,MAAMC,iBAAiB,GAAG,EAAE;EAC5BN,6BAA6B,CAACO,OAAO,CAAEL,GAAG,IAAK;IAC3C,MAAMM,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAACP,GAAG,CAAC;IACzC,IAAIM,KAAK,KAAKE,SAAS,EAAE;MACrBJ,iBAAiB,CAACK,IAAI,CAAC,CAACT,GAAG,EAAEM,KAAK,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1CJ,KAAK,CAACK,GAAG,CAACX,GAAG,CAACY,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;EACJ,CAAC,CAAC;EACF,OAAOR,iBAAiB;AAC5B;AACA,MAAMS,gBAAgB,GAAG;EACrB;EACAC,KAAK,EAAEA,CAAAC,IAAA,EAAAC,KAAA;IAAA,IAAC;MAAEC;IAAE,CAAC,GAAAF,IAAA;IAAA,IAAE;MAAEG,WAAW,GAAG,GAAG;MAAEC,YAAY,GAAG;IAAI,CAAC,GAAAH,KAAA;IAAA,OAAKC,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,GAAGC,UAAU,CAACJ,WAAW,CAAC,GAAGI,UAAU,CAACH,YAAY,CAAC;EAAA;EAC/HI,MAAM,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEC;IAAE,CAAC,GAAAF,KAAA;IAAA,IAAE;MAAEG,UAAU,GAAG,GAAG;MAAEC,aAAa,GAAG;IAAI,CAAC,GAAAH,KAAA;IAAA,OAAKC,CAAC,CAACN,GAAG,GAAGM,CAAC,CAACL,GAAG,GAAGC,UAAU,CAACK,UAAU,CAAC,GAAGL,UAAU,CAACM,aAAa,CAAC;EAAA;EAChIC,GAAG,EAAEA,CAACC,KAAK,EAAAC,KAAA;IAAA,IAAE;MAAEF;IAAI,CAAC,GAAAE,KAAA;IAAA,OAAKT,UAAU,CAACO,GAAG,CAAC;EAAA;EACxCG,IAAI,EAAEA,CAACF,KAAK,EAAAG,KAAA;IAAA,IAAE;MAAED;IAAK,CAAC,GAAAC,KAAA;IAAA,OAAKX,UAAU,CAACU,IAAI,CAAC;EAAA;EAC3CE,MAAM,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEV;IAAE,CAAC,GAAAS,KAAA;IAAA,IAAE;MAAEN;IAAI,CAAC,GAAAO,KAAA;IAAA,OAAKd,UAAU,CAACO,GAAG,CAAC,IAAIH,CAAC,CAACN,GAAG,GAAGM,CAAC,CAACL,GAAG,CAAC;EAAA;EAC7DgB,KAAK,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEtB;IAAE,CAAC,GAAAqB,KAAA;IAAA,IAAE;MAAEN;IAAK,CAAC,GAAAO,KAAA;IAAA,OAAKjB,UAAU,CAACU,IAAI,CAAC,IAAIf,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,CAAC;EAAA;EAC9D;EACAJ,CAAC,EAAEA,CAACa,KAAK,EAAAU,KAAA;IAAA,IAAE;MAAEC;IAAU,CAAC,GAAAD,KAAA;IAAA,OAAKlD,uBAAuB,CAACmD,SAAS,EAAE,GAAG,CAAC;EAAA;EACpEf,CAAC,EAAEA,CAACI,KAAK,EAAAY,MAAA;IAAA,IAAE;MAAED;IAAU,CAAC,GAAAC,MAAA;IAAA,OAAKpD,uBAAuB,CAACmD,SAAS,EAAE,GAAG,CAAC;EAAA;AACxE,CAAC;AACD;AACA5B,gBAAgB,CAAC8B,UAAU,GAAG9B,gBAAgB,CAACI,CAAC;AAChDJ,gBAAgB,CAAC+B,UAAU,GAAG/B,gBAAgB,CAACa,CAAC;AAEhD,SAAShC,aAAa,EAAEmB,gBAAgB,EAAEX,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}