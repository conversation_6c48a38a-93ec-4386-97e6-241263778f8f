{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { EyeIcon, EyeSlashIcon, UserIcon, LockClosedIcon, ArrowRightIcon } from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    rememberMe: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const {\n    login,\n    isLoading\n  } = useUser();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    const result = await login(formData.email, formData.password, formData.rememberMe);\n    if (result.success) {\n      toast.success('Welcome back!');\n      navigate(from, {\n        replace: true\n      });\n    } else {\n      toast.error(result.error);\n    }\n  };\n  const handleSocialLogin = provider => {\n    toast.info(`${provider} login coming soon!`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            delay: 0.2,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: \"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(UserIcon, {\n            className: \"h-8 w-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-3xl font-bold text-gray-900\",\n          children: \"Welcome back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: \"Sign in to your account to continue shopping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-8 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email Address\",\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            error: errors.email,\n            placeholder: \"Enter your email\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Password\",\n              type: showPassword ? 'text' : 'password',\n              name: \"password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              error: errors.password,\n              placeholder: \"Enter your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              className: \"absolute right-3 top-9 text-gray-400 hover:text-gray-600\",\n              children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"rememberMe\",\n                checked: formData.rememberMe,\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/reset-password\",\n              className: \"text-sm text-light-orange-600 hover:text-light-orange-500 font-medium\",\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            loading: isLoading,\n            fullWidth: true,\n            icon: ArrowRightIcon,\n            iconPosition: \"right\",\n            size: \"large\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full border-t border-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex justify-center text-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 bg-white text-gray-500\",\n              children: \"Or continue with\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: () => handleSocialLogin('Google'),\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-2\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"#4285F4\",\n                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"#34A853\",\n                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"#FBBC05\",\n                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"#EA4335\",\n                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), \"Google\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: () => handleSocialLogin('Facebook'),\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-2\",\n              fill: \"#1877F2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), \"Facebook\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"font-medium text-light-orange-600 hover:text-light-orange-500\",\n              children: \"Sign up now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.5\n        },\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-blue-800 mb-2\",\n          children: \"Demo Credentials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-blue-600\",\n          children: [\"Email: <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 36\n          }, this), \"Password: password123\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"3+tTyksb/vWTlGOTS9Va6/Zswgk=\", false, function () {\n  return [useUser, useNavigate, useLocation];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "motion", "EyeIcon", "EyeSlashIcon", "UserIcon", "LockClosedIcon", "ArrowRightIcon", "useUser", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "_location$state", "_location$state$from", "formData", "setFormData", "email", "password", "rememberMe", "showPassword", "setShowPassword", "errors", "setErrors", "login", "isLoading", "navigate", "location", "from", "state", "pathname", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "validateForm", "newErrors", "test", "length", "Object", "keys", "handleSubmit", "preventDefault", "result", "success", "replace", "error", "handleSocialLogin", "provider", "info", "className", "children", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "stiffness", "onSubmit", "label", "onChange", "placeholder", "required", "onClick", "to", "loading", "fullWidth", "icon", "iconPosition", "size", "variant", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { \n  EyeIcon, \n  EyeSlashIcon, \n  UserIcon,\n  LockClosedIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst LoginPage = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    rememberMe: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  \n  const { login, isLoading } = useUser();\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  const from = location.state?.from?.pathname || '/';\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    \n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    const result = await login(formData.email, formData.password, formData.rememberMe);\n    \n    if (result.success) {\n      toast.success('Welcome back!');\n      navigate(from, { replace: true });\n    } else {\n      toast.error(result.error);\n    }\n  };\n\n  const handleSocialLogin = (provider) => {\n    toast.info(`${provider} login coming soon!`);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <Toaster position=\"top-right\" />\n      \n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"max-w-md w-full space-y-8\"\n      >\n        {/* Header */}\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n          >\n            <UserIcon className=\"h-8 w-8 text-white\" />\n          </motion.div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Welcome back\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Sign in to your account to continue shopping\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-2xl shadow-xl p-8 space-y-6\"\n        >\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Email Field */}\n            <Input\n              label=\"Email Address\"\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              error={errors.email}\n              placeholder=\"Enter your email\"\n              required\n            />\n\n            {/* Password Field */}\n            <div className=\"relative\">\n              <Input\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                placeholder=\"Enter your password\"\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5\" />\n                )}\n              </button>\n            </div>\n\n            {/* Remember Me & Forgot Password */}\n            <div className=\"flex items-center justify-between\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  name=\"rememberMe\"\n                  checked={formData.rememberMe}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">Remember me</span>\n              </label>\n              <Link\n                to=\"/reset-password\"\n                className=\"text-sm text-light-orange-600 hover:text-light-orange-500 font-medium\"\n              >\n                Forgot password?\n              </Link>\n            </div>\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              loading={isLoading}\n              fullWidth\n              icon={ArrowRightIcon}\n              iconPosition=\"right\"\n              size=\"large\"\n            >\n              Sign In\n            </Button>\n          </form>\n\n          {/* Divider */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n            </div>\n          </div>\n\n          {/* Social Login */}\n          <div className=\"grid grid-cols-2 gap-3\">\n            <Button\n              variant=\"outline\"\n              onClick={() => handleSocialLogin('Google')}\n              className=\"flex items-center justify-center\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n              </svg>\n              Google\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={() => handleSocialLogin('Facebook')}\n              className=\"flex items-center justify-center\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" fill=\"#1877F2\" viewBox=\"0 0 24 24\">\n                <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n              </svg>\n              Facebook\n            </Button>\n          </div>\n\n          {/* Sign Up Link */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Don't have an account?{' '}\n              <Link\n                to=\"/register\"\n                className=\"font-medium text-light-orange-600 hover:text-light-orange-500\"\n              >\n                Sign up now\n              </Link>\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Demo Credentials */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5 }}\n          className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\"\n        >\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials</h3>\n          <p className=\"text-xs text-blue-600\">\n            Email: <EMAIL><br />\n            Password: password123\n          </p>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,cAAc,QACT,6BAA6B;AACpC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAM;IAAE8B,KAAK;IAAEC;EAAU,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACtC,MAAMsB,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B,MAAM+B,IAAI,GAAG,EAAAf,eAAA,GAAAc,QAAQ,CAACE,KAAK,cAAAhB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBe,IAAI,cAAAd,oBAAA,uBAApBA,oBAAA,CAAsBgB,QAAQ,KAAI,GAAG;EAElD,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/CrB,WAAW,CAACsB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIZ,MAAM,CAACW,IAAI,CAAC,EAAE;MAChBV,SAAS,CAACe,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACL,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACzB,QAAQ,CAACE,KAAK,EAAE;MACnBuB,SAAS,CAACvB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACwB,IAAI,CAAC1B,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/CuB,SAAS,CAACvB,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBsB,SAAS,CAACtB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACtB,QAAQ,GAAG,wCAAwC;IAC/D;IAEAK,SAAS,CAACiB,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;IAErB,MAAMQ,MAAM,GAAG,MAAMvB,KAAK,CAACT,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,EAAEH,QAAQ,CAACI,UAAU,CAAC;IAElF,IAAI4B,MAAM,CAACC,OAAO,EAAE;MAClBzC,KAAK,CAACyC,OAAO,CAAC,eAAe,CAAC;MAC9BtB,QAAQ,CAACE,IAAI,EAAE;QAAEqB,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,MAAM;MACL1C,KAAK,CAAC2C,KAAK,CAACH,MAAM,CAACG,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;IACtC7C,KAAK,CAAC8C,IAAI,CAAC,GAAGD,QAAQ,qBAAqB,CAAC;EAC9C,CAAC;EAED,oBACE1C,OAAA;IAAK4C,SAAS,EAAC,0HAA0H;IAAAC,QAAA,gBACvI7C,OAAA,CAACF,OAAO;MAACgD,QAAQ,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhClD,OAAA,CAACZ,MAAM,CAAC+D,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9Bb,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAGrC7C,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7C,OAAA,CAACZ,MAAM,CAAC+D,GAAG;UACTC,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,OAAO,EAAE;YAAEG,KAAK,EAAE;UAAE,CAAE;UACtBF,UAAU,EAAE;YAAEG,KAAK,EAAE,GAAG;YAAElC,IAAI,EAAE,QAAQ;YAAEmC,SAAS,EAAE;UAAI,CAAE;UAC3DhB,SAAS,EAAC,sIAAsI;UAAAC,QAAA,eAEhJ7C,OAAA,CAACT,QAAQ;YAACqD,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACblD,OAAA;UAAI4C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlD,OAAA;UAAG4C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlD,OAAA,CAACZ,MAAM,CAAC+D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC3Bf,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAExD7C,OAAA;UAAM6D,QAAQ,EAAE1B,YAAa;UAACS,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjD7C,OAAA,CAACJ,KAAK;YACJkE,KAAK,EAAC,eAAe;YACrBrC,IAAI,EAAC,OAAO;YACZF,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEnB,QAAQ,CAACE,KAAM;YACtBwD,QAAQ,EAAE1C,iBAAkB;YAC5BmB,KAAK,EAAE5B,MAAM,CAACL,KAAM;YACpByD,WAAW,EAAC,kBAAkB;YAC9BC,QAAQ;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGFlD,OAAA;YAAK4C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7C,OAAA,CAACJ,KAAK;cACJkE,KAAK,EAAC,UAAU;cAChBrC,IAAI,EAAEf,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCa,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnB,QAAQ,CAACG,QAAS;cACzBuD,QAAQ,EAAE1C,iBAAkB;cAC5BmB,KAAK,EAAE5B,MAAM,CAACJ,QAAS;cACvBwD,WAAW,EAAC,qBAAqB;cACjCC,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFlD,OAAA;cACEyB,IAAI,EAAC,QAAQ;cACbyC,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CkC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAEnEnC,YAAY,gBACXV,OAAA,CAACV,YAAY;gBAACsD,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpClD,OAAA,CAACX,OAAO;gBAACuD,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlD,OAAA;YAAK4C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD7C,OAAA;cAAO4C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC7C,OAAA;gBACEyB,IAAI,EAAC,UAAU;gBACfF,IAAI,EAAC,YAAY;gBACjBG,OAAO,EAAErB,QAAQ,CAACI,UAAW;gBAC7BsD,QAAQ,EAAE1C,iBAAkB;gBAC5BuB,SAAS,EAAC;cAAmF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC,eACFlD,OAAA;gBAAM4C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACRlD,OAAA,CAACf,IAAI;cACHkF,EAAE,EAAC,iBAAiB;cACpBvB,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EAClF;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlD,OAAA,CAACL,MAAM;YACL8B,IAAI,EAAC,QAAQ;YACb2C,OAAO,EAAErD,SAAU;YACnBsD,SAAS;YACTC,IAAI,EAAE7E,cAAe;YACrB8E,YAAY,EAAC,OAAO;YACpBC,IAAI,EAAC,OAAO;YAAA3B,QAAA,EACb;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGPlD,OAAA;UAAK4C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB7C,OAAA;YAAK4C,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD7C,OAAA;cAAK4C,SAAS,EAAC;YAAiC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNlD,OAAA;YAAK4C,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnD7C,OAAA;cAAM4C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlD,OAAA;UAAK4C,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7C,OAAA,CAACL,MAAM;YACL8E,OAAO,EAAC,SAAS;YACjBP,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAAC,QAAQ,CAAE;YAC3CG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAE5C7C,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAC8B,OAAO,EAAC,WAAW;cAAA7B,QAAA,gBAC/C7C,OAAA;gBAAM2E,IAAI,EAAC,SAAS;gBAACC,CAAC,EAAC;cAAyH;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAClJlD,OAAA;gBAAM2E,IAAI,EAAC,SAAS;gBAACC,CAAC,EAAC;cAAuI;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAChKlD,OAAA;gBAAM2E,IAAI,EAAC,SAAS;gBAACC,CAAC,EAAC;cAA+H;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxJlD,OAAA;gBAAM2E,IAAI,EAAC,SAAS;gBAACC,CAAC,EAAC;cAAqI;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J,CAAC,UAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlD,OAAA,CAACL,MAAM;YACL8E,OAAO,EAAC,SAAS;YACjBP,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAAC,UAAU,CAAE;YAC7CG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAE5C7C,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAC+B,IAAI,EAAC,SAAS;cAACD,OAAO,EAAC,WAAW;cAAA7B,QAAA,eAC9D7C,OAAA;gBAAM4E,CAAC,EAAC;cAAgS;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvS,CAAC,YAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNlD,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B7C,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wBACb,EAAC,GAAG,eAC1B7C,OAAA,CAACf,IAAI;cACHkF,EAAE,EAAC,WAAW;cACdvB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGblD,OAAA,CAACZ,MAAM,CAAC+D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC3Bf,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAE5D7C,OAAA;UAAI4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5ElD,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,yBACZ,eAAA7C,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yBAE/B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAChD,EAAA,CA/OID,SAAS;EAAA,QASgBP,OAAO,EACnBR,WAAW,EACXC,WAAW;AAAA;AAAA0F,EAAA,GAXxB5E,SAAS;AAiPf,eAAeA,SAAS;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}