{"ast": null, "code": "import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\nlet now;\nfunction clearTime() {\n  now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n  now: () => {\n    if (now === undefined) {\n      time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming ? frameData.timestamp : performance.now());\n    }\n    return now;\n  },\n  set: newTime => {\n    now = newTime;\n    queueMicrotask(clearTime);\n  }\n};\nexport { time };", "map": {"version": 3, "names": ["MotionGlobalConfig", "frameData", "now", "clearTime", "undefined", "time", "set", "isProcessing", "useManualTiming", "timestamp", "performance", "newTime", "queueMicrotask"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAASC,SAAS,QAAQ,aAAa;AAEvC,IAAIC,GAAG;AACP,SAASC,SAASA,CAAA,EAAG;EACjBD,GAAG,GAAGE,SAAS;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG;EACTH,GAAG,EAAEA,CAAA,KAAM;IACP,IAAIA,GAAG,KAAKE,SAAS,EAAE;MACnBC,IAAI,CAACC,GAAG,CAACL,SAAS,CAACM,YAAY,IAAIP,kBAAkB,CAACQ,eAAe,GAC/DP,SAAS,CAACQ,SAAS,GACnBC,WAAW,CAACR,GAAG,CAAC,CAAC,CAAC;IAC5B;IACA,OAAOA,GAAG;EACd,CAAC;EACDI,GAAG,EAAGK,OAAO,IAAK;IACdT,GAAG,GAAGS,OAAO;IACbC,cAAc,CAACT,SAAS,CAAC;EAC7B;AACJ,CAAC;AAED,SAASE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}