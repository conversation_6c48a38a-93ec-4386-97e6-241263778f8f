{"ast": null, "code": "function resolveElements(elementOrSelector, scope, selectorCache) {\n  if (elementOrSelector instanceof EventTarget) {\n    return [elementOrSelector];\n  } else if (typeof elementOrSelector === \"string\") {\n    var _selectorCache$elemen;\n    let root = document;\n    if (scope) {\n      root = scope.current;\n    }\n    const elements = (_selectorCache$elemen = selectorCache === null || selectorCache === void 0 ? void 0 : selectorCache[elementOrSelector]) !== null && _selectorCache$elemen !== void 0 ? _selectorCache$elemen : root.querySelectorAll(elementOrSelector);\n    return elements ? Array.from(elements) : [];\n  }\n  return Array.from(elementOrSelector);\n}\nexport { resolveElements };", "map": {"version": 3, "names": ["resolveElements", "elementOrSelector", "scope", "selectorCache", "EventTarget", "_selectorCache$elemen", "root", "document", "current", "elements", "querySelectorAll", "Array", "from"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs"], "sourcesContent": ["function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,iBAAiB,EAAEC,KAAK,EAAEC,aAAa,EAAE;EAC9D,IAAIF,iBAAiB,YAAYG,WAAW,EAAE;IAC1C,OAAO,CAACH,iBAAiB,CAAC;EAC9B,CAAC,MACI,IAAI,OAAOA,iBAAiB,KAAK,QAAQ,EAAE;IAAA,IAAAI,qBAAA;IAC5C,IAAIC,IAAI,GAAGC,QAAQ;IACnB,IAAIL,KAAK,EAAE;MACPI,IAAI,GAAGJ,KAAK,CAACM,OAAO;IACxB;IACA,MAAMC,QAAQ,IAAAJ,qBAAA,GAAGF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGF,iBAAiB,CAAC,cAAAI,qBAAA,cAAAA,qBAAA,GAC/CC,IAAI,CAACI,gBAAgB,CAACT,iBAAiB,CAAC;IAC5C,OAAOQ,QAAQ,GAAGE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,GAAG,EAAE;EAC/C;EACA,OAAOE,KAAK,CAACC,IAAI,CAACX,iBAAiB,CAAC;AACxC;AAEA,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}