import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import Button from '../components/Button';

const PlaceholderPage = ({ title, description, icon: Icon = ExclamationTriangleIcon }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-md mx-auto px-4"
      >
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <Icon className="w-16 h-16 text-light-orange-500 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="text-gray-600 mb-8 leading-relaxed">
            {description || `The ${title.toLowerCase()} page is coming soon. We're working hard to bring you this feature.`}
          </p>
          <div className="space-y-4">
            <Link to="/">
              <Button fullWidth icon={ArrowLeftIcon}>
                Back to Home
              </Button>
            </Link>
            <Link to="/contact">
              <Button variant="outline" fullWidth>
                Contact Support
              </Button>
            </Link>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

// Specific placeholder pages
export const HelpPage = () => (
  <PlaceholderPage 
    title="Help Center" 
    description="Our comprehensive help center is being developed. For immediate assistance, please contact our support team."
  />
);

export const ReturnsPage = () => (
  <PlaceholderPage 
    title="Returns & Exchanges" 
    description="Learn about our hassle-free return policy and how to exchange your items. This page is currently under construction."
  />
);

export const ShippingPage = () => (
  <PlaceholderPage 
    title="Shipping Information" 
    description="Find detailed information about our shipping options, delivery times, and tracking. Coming soon!"
  />
);

export const TrackOrderPage = () => (
  <PlaceholderPage 
    title="Track Your Order" 
    description="Track your order status and delivery progress. This feature is being developed and will be available soon."
  />
);

export const PrivacyPage = () => (
  <PlaceholderPage 
    title="Privacy Policy" 
    description="Your privacy is important to us. Our detailed privacy policy is being finalized and will be available soon."
  />
);

export const TermsPage = () => (
  <PlaceholderPage 
    title="Terms of Service" 
    description="Review our terms and conditions for using our platform. This page is currently being updated."
  />
);

export const CookiesPage = () => (
  <PlaceholderPage 
    title="Cookie Policy" 
    description="Learn about how we use cookies to improve your browsing experience. Policy details coming soon."
  />
);

export const OrdersPage = () => (
  <PlaceholderPage 
    title="Order History" 
    description="View your past orders and track current purchases. This feature is being developed."
  />
);

export default PlaceholderPage;
