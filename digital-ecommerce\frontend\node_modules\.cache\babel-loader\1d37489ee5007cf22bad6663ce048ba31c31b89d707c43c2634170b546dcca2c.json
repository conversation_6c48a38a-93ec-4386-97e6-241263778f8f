{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AboutPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { HeartIcon, LightBulbIcon, ShieldCheckIcon, GlobeAltIcon\n// UsersIcon,\n// TrophyIcon\n} from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = () => {\n  _s();\n  const [heroRef, heroInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [valuesRef, valuesInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [teamRef, teamInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const [statsRef, statsInView] = useInView({\n    threshold: 0.1,\n    triggerOnce: true\n  });\n  const values = [{\n    icon: HeartIcon,\n    title: 'Customer First',\n    description: 'We put our customers at the heart of everything we do, ensuring exceptional service and satisfaction.'\n  }, {\n    icon: LightBulbIcon,\n    title: 'Innovation',\n    description: 'We constantly innovate to bring you the latest and greatest products with cutting-edge technology.'\n  }, {\n    icon: ShieldCheckIcon,\n    title: 'Quality Assurance',\n    description: 'Every product goes through rigorous quality checks to ensure you receive only the best.'\n  }, {\n    icon: GlobeAltIcon,\n    title: 'Global Reach',\n    description: 'We serve customers worldwide with fast, reliable shipping and local support.'\n  }];\n  const team = [{\n    name: 'Sarah Johnson',\n    role: 'CEO & Founder',\n    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300',\n    bio: 'Visionary leader with 15+ years in e-commerce and retail innovation.'\n  }, {\n    name: 'Michael Chen',\n    role: 'CTO',\n    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300',\n    bio: 'Tech expert passionate about creating seamless digital experiences.'\n  }, {\n    name: 'Emily Rodriguez',\n    role: 'Head of Design',\n    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300',\n    bio: 'Creative designer focused on user-centered design and accessibility.'\n  }, {\n    name: 'David Kim',\n    role: 'VP of Operations',\n    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300',\n    bio: 'Operations specialist ensuring smooth logistics and customer satisfaction.'\n  }];\n  const stats = [{\n    number: '1M+',\n    label: 'Happy Customers'\n  }, {\n    number: '50K+',\n    label: 'Products Sold'\n  }, {\n    number: '99.9%',\n    label: 'Uptime'\n  }, {\n    number: '24/7',\n    label: 'Support'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(motion.section, {\n      ref: heroRef,\n      initial: {\n        opacity: 0\n      },\n      animate: heroInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 1\n      },\n      className: \"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full animate-float\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-20 right-20 w-24 h-24 bg-white bg-opacity-10 rounded-full animate-float-delayed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              y: 50,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              y: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"text-4xl lg:text-6xl font-bold text-white mb-6\",\n            children: \"About ShopHub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              y: 50,\n              opacity: 0\n            },\n            animate: heroInView ? {\n              y: 0,\n              opacity: 1\n            } : {},\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"text-xl lg:text-2xl text-light-orange-100 max-w-3xl mx-auto leading-relaxed\",\n            children: \"We're passionate about bringing you the best products with exceptional service. Our journey started with a simple mission: to make online shopping delightful and accessible for everyone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: -50,\n              opacity: 0\n            },\n            whileInView: {\n              x: 0,\n              opacity: 1\n            },\n            transition: {\n              duration: 0.8\n            },\n            viewport: {\n              once: true\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\",\n              children: \"Our Story\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 text-lg text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Founded in 2020, ShopHub began as a small startup with big dreams. We noticed that online shopping could be so much better \\u2013 more personal, more reliable, and more enjoyable.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Today, we've grown into a trusted platform serving millions of customers worldwide. But our core values remain the same: putting customers first, maintaining the highest quality standards, and continuously innovating.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Every product we sell, every feature we build, and every interaction we have is guided by our commitment to excellence and customer satisfaction.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              x: 50,\n              opacity: 0\n            },\n            whileInView: {\n              x: 0,\n              opacity: 1\n            },\n            transition: {\n              duration: 0.8\n            },\n            viewport: {\n              once: true\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600\",\n              alt: \"Our team working together\",\n              className: \"rounded-2xl shadow-2xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-6 -left-6 w-full h-full bg-gradient-to-br from-light-orange-200 to-light-orange-300 rounded-2xl -z-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: valuesRef,\n      initial: {\n        opacity: 0\n      },\n      animate: valuesInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-gradient-to-br from-light-orange-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Our Values\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"These core principles guide everything we do and shape our company culture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: values.map((value, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: valuesInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: /*#__PURE__*/_jsxDEV(value.icon, {\n                className: \"w-10 h-10 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: value.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: value.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: statsRef,\n      initial: {\n        opacity: 0\n      },\n      animate: statsInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-white mb-4\",\n            children: \"Our Impact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n            children: \"Numbers that reflect our commitment to excellence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.5\n            },\n            animate: statsInView ? {\n              opacity: 1,\n              scale: 1\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl lg:text-5xl font-bold text-light-orange-400 mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-300\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.section, {\n      ref: teamRef,\n      initial: {\n        opacity: 0\n      },\n      animate: teamInView ? {\n        opacity: 1\n      } : {},\n      transition: {\n        duration: 0.8\n      },\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n            children: \"Meet Our Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"The passionate people behind ShopHub's success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: team.map((member, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: teamInView ? {\n              opacity: 1,\n              y: 0\n            } : {},\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            className: \"text-center group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: member.image,\n                alt: member.name,\n                className: \"w-32 h-32 rounded-full mx-auto object-cover group-hover:scale-105 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-light-orange-400 to-light-orange-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-1\",\n              children: member.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-light-orange-600 font-medium mb-3\",\n              children: member.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: member.bio\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-r from-light-orange-500 to-light-orange-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl lg:text-4xl font-bold text-white mb-6\",\n            children: \"Ready to Start Shopping?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-light-orange-100 mb-8 max-w-2xl mx-auto\",\n            children: \"Join millions of satisfied customers and discover amazing products at unbeatable prices.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            className: \"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: \"Shop Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(AboutPage, \"4QUmIuq0ImAZMpsFdigq1A9u/LQ=\", false, function () {\n  return [useInView, useInView, useInView, useInView];\n});\n_c = AboutPage;\nexport default AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");", "map": {"version": 3, "names": ["React", "motion", "useInView", "HeartIcon", "LightBulbIcon", "ShieldCheckIcon", "GlobeAltIcon", "jsxDEV", "_jsxDEV", "AboutPage", "_s", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "valuesRef", "valuesInView", "teamRef", "teamInView", "statsRef", "statsInView", "values", "icon", "title", "description", "team", "name", "role", "image", "bio", "stats", "number", "label", "className", "children", "section", "ref", "initial", "opacity", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "h1", "y", "delay", "p", "div", "x", "whileInView", "viewport", "once", "src", "alt", "map", "value", "index", "stat", "scale", "member", "button", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AboutPage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  HeartIcon, \n  LightBulbIcon, \n  ShieldCheckIcon,\n  GlobeAltIcon,\n  // UsersIcon,\n  // TrophyIcon\n} from '@heroicons/react/24/outline';\n\nconst AboutPage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [valuesRef, valuesInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [teamRef, teamInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [statsRef, statsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n\n  const values = [\n    {\n      icon: HeartIcon,\n      title: 'Customer First',\n      description: 'We put our customers at the heart of everything we do, ensuring exceptional service and satisfaction.'\n    },\n    {\n      icon: LightBulbIcon,\n      title: 'Innovation',\n      description: 'We constantly innovate to bring you the latest and greatest products with cutting-edge technology.'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Quality Assurance',\n      description: 'Every product goes through rigorous quality checks to ensure you receive only the best.'\n    },\n    {\n      icon: GlobeAltIcon,\n      title: 'Global Reach',\n      description: 'We serve customers worldwide with fast, reliable shipping and local support.'\n    }\n  ];\n\n  const team = [\n    {\n      name: 'Sarah Johnson',\n      role: 'CEO & Founder',\n      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300',\n      bio: 'Visionary leader with 15+ years in e-commerce and retail innovation.'\n    },\n    {\n      name: 'Michael Chen',\n      role: 'CTO',\n      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300',\n      bio: 'Tech expert passionate about creating seamless digital experiences.'\n    },\n    {\n      name: 'Emily Rodriguez',\n      role: 'Head of Design',\n      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300',\n      bio: 'Creative designer focused on user-centered design and accessibility.'\n    },\n    {\n      name: 'David Kim',\n      role: 'VP of Operations',\n      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300',\n      bio: 'Operations specialist ensuring smooth logistics and customer satisfaction.'\n    }\n  ];\n\n  const stats = [\n    { number: '1M+', label: 'Happy Customers' },\n    { number: '50K+', label: 'Products Sold' },\n    { number: '99.9%', label: 'Uptime' },\n    { number: '24/7', label: 'Support' }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full animate-float\"></div>\n          <div className=\"absolute bottom-20 right-20 w-24 h-24 bg-white bg-opacity-10 rounded-full animate-float-delayed\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"text-center\">\n            <motion.h1\n              initial={{ y: 50, opacity: 0 }}\n              animate={heroInView ? { y: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-4xl lg:text-6xl font-bold text-white mb-6\"\n            >\n              About ShopHub\n            </motion.h1>\n            <motion.p\n              initial={{ y: 50, opacity: 0 }}\n              animate={heroInView ? { y: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"text-xl lg:text-2xl text-light-orange-100 max-w-3xl mx-auto leading-relaxed\"\n            >\n              We're passionate about bringing you the best products with exceptional service. \n              Our journey started with a simple mission: to make online shopping delightful and accessible for everyone.\n            </motion.p>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Story Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -50, opacity: 0 }}\n              whileInView={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\">\n                Our Story\n              </h2>\n              <div className=\"space-y-4 text-lg text-gray-600\">\n                <p>\n                  Founded in 2020, ShopHub began as a small startup with big dreams. \n                  We noticed that online shopping could be so much better – more personal, \n                  more reliable, and more enjoyable.\n                </p>\n                <p>\n                  Today, we've grown into a trusted platform serving millions of customers \n                  worldwide. But our core values remain the same: putting customers first, \n                  maintaining the highest quality standards, and continuously innovating.\n                </p>\n                <p>\n                  Every product we sell, every feature we build, and every interaction we have \n                  is guided by our commitment to excellence and customer satisfaction.\n                </p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 50, opacity: 0 }}\n              whileInView={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <img\n                src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600\"\n                alt=\"Our team working together\"\n                className=\"rounded-2xl shadow-2xl\"\n              />\n              <div className=\"absolute -bottom-6 -left-6 w-full h-full bg-gradient-to-br from-light-orange-200 to-light-orange-300 rounded-2xl -z-10\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Values Section */}\n      <motion.section\n        ref={valuesRef}\n        initial={{ opacity: 0 }}\n        animate={valuesInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Our Values\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              These core principles guide everything we do and shape our company culture\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {values.map((value, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={valuesInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <value.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{value.title}</h3>\n                <p className=\"text-gray-600\">{value.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section\n        ref={statsRef}\n        initial={{ opacity: 0 }}\n        animate={statsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gray-900\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Our Impact\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n              Numbers that reflect our commitment to excellence\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, scale: 0.5 }}\n                animate={statsInView ? { opacity: 1, scale: 1 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-4xl lg:text-5xl font-bold text-light-orange-400 mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-lg text-gray-300\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Team Section */}\n      <motion.section\n        ref={teamRef}\n        initial={{ opacity: 0 }}\n        animate={teamInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Meet Our Team\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              The passionate people behind ShopHub's success\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {team.map((member, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={teamInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"relative mb-6\">\n                  <img\n                    src={member.image}\n                    alt={member.name}\n                    className=\"w-32 h-32 rounded-full mx-auto object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-light-orange-400 to-light-orange-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"></div>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-1\">{member.name}</h3>\n                <p className=\"text-light-orange-600 font-medium mb-3\">{member.role}</p>\n                <p className=\"text-gray-600 text-sm\">{member.bio}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-light-orange-500 to-light-orange-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-6\">\n              Ready to Start Shopping?\n            </h2>\n            <p className=\"text-xl text-light-orange-100 mb-8 max-w-2xl mx-auto\">\n              Join millions of satisfied customers and discover amazing products at unbeatable prices.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              Shop Now\n            </motion.button>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SACEC,SAAS,EACTC,aAAa,EACbC,eAAe,EACfC;AACA;AACA;AAAA,OACK,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,SAAS,CAAC;IAAEW,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,SAAS,CAAC;IAAEW,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAClF,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGhB,SAAS,CAAC;IAAEW,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAC9E,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,SAAS,CAAC;IAAEW,SAAS,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAK,CAAC,CAAC;EAEhF,MAAMO,MAAM,GAAG,CACb;IACEC,IAAI,EAAEnB,SAAS;IACfoB,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAElB,aAAa;IACnBmB,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEjB,eAAe;IACrBkB,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhB,YAAY;IAClBiB,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,IAAI,GAAG,CACX;IACEC,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,oEAAoE;IAC3EC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,oEAAoE;IAC3EC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,oEAAoE;IAC3EC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,oEAAoE;IAC3EC,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC3C;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC1C;IAAED,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,CACrC;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B1B,OAAA,CAACP,MAAM,CAACkC,OAAO;MACbC,GAAG,EAAEzB,OAAQ;MACb0B,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE3B,UAAU,GAAG;QAAE0B,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAE,CAAE;MAC5BR,SAAS,EAAC,2GAA2G;MAAAC,QAAA,gBAErH1B,OAAA;QAAKyB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1B,OAAA;UAAKyB,SAAS,EAAC;QAAqF;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3GrC,OAAA;UAAKyB,SAAS,EAAC;QAAiG;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC,eAENrC,OAAA;QAAKyB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7E1B,OAAA;UAAKyB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1B,OAAA,CAACP,MAAM,CAAC6C,EAAE;YACRT,OAAO,EAAE;cAAEU,CAAC,EAAE,EAAE;cAAET,OAAO,EAAE;YAAE,CAAE;YAC/BC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAC1Cf,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAC3D;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZrC,OAAA,CAACP,MAAM,CAACgD,CAAC;YACPZ,OAAO,EAAE;cAAEU,CAAC,EAAE,EAAE;cAAET,OAAO,EAAE;YAAE,CAAE;YAC/BC,OAAO,EAAE3B,UAAU,GAAG;cAAEmC,CAAC,EAAE,CAAC;cAAET,OAAO,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAE;YAAI,CAAE;YAC1Cf,SAAS,EAAC,6EAA6E;YAAAC,QAAA,EACxF;UAGD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA;MAASyB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD1B,OAAA;UAAKyB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClE1B,OAAA,CAACP,MAAM,CAACiD,GAAG;YACTb,OAAO,EAAE;cAAEc,CAAC,EAAE,CAAC,EAAE;cAAEb,OAAO,EAAE;YAAE,CAAE;YAChCc,WAAW,EAAE;cAAED,CAAC,EAAE,CAAC;cAAEb,OAAO,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BY,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAApB,QAAA,gBAEzB1B,OAAA;cAAIyB,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAElE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrC,OAAA;cAAKyB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C1B,OAAA;gBAAA0B,QAAA,EAAG;cAIH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJrC,OAAA;gBAAA0B,QAAA,EAAG;cAIH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJrC,OAAA;gBAAA0B,QAAA,EAAG;cAGH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbrC,OAAA,CAACP,MAAM,CAACiD,GAAG;YACTb,OAAO,EAAE;cAAEc,CAAC,EAAE,EAAE;cAAEb,OAAO,EAAE;YAAE,CAAE;YAC/Bc,WAAW,EAAE;cAAED,CAAC,EAAE,CAAC;cAAEb,OAAO,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BY,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBrB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB1B,OAAA;cACE+C,GAAG,EAAC,oEAAoE;cACxEC,GAAG,EAAC,2BAA2B;cAC/BvB,SAAS,EAAC;YAAwB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACFrC,OAAA;cAAKyB,SAAS,EAAC;YAAwH;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA,CAACP,MAAM,CAACkC,OAAO;MACbC,GAAG,EAAErB,SAAU;MACfsB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAEvB,YAAY,GAAG;QAAEsB,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC5CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eAEjE1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEb,MAAM,CAACoC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBnD,OAAA,CAACP,MAAM,CAACiD,GAAG;YAETb,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BR,OAAO,EAAEvB,YAAY,GAAG;cAAEsB,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAClDP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClD1B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B1B,OAAA;cAAKyB,SAAS,EAAC,0LAA0L;cAAAC,QAAA,eACvM1B,OAAA,CAACkD,KAAK,CAACpC,IAAI;gBAACW,SAAS,EAAC;cAAiC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNrC,OAAA;cAAIyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEwB,KAAK,CAACnC;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ErC,OAAA;cAAGyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEwB,KAAK,CAAClC;YAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAV/Cc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACP,MAAM,CAACkC,OAAO;MACbC,GAAG,EAAEjB,QAAS;MACdkB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAEnB,WAAW,GAAG;QAAEkB,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC3CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7B1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAE/D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDJ,KAAK,CAAC2B,GAAG,CAAC,CAACG,IAAI,EAAED,KAAK,kBACrBnD,OAAA,CAACP,MAAM,CAACiD,GAAG;YAETb,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAI,CAAE;YACpCtB,OAAO,EAAEnB,WAAW,GAAG;cAAEkB,OAAO,EAAE,CAAC;cAAEuB,KAAK,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YACrDrB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClD1B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAEvB1B,OAAA;cAAKyB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EACvE0B,IAAI,CAAC7B;YAAM;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNrC,OAAA;cAAKyB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE0B,IAAI,CAAC5B;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GATpDc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA,CAACP,MAAM,CAACkC,OAAO;MACbC,GAAG,EAAEnB,OAAQ;MACboB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAErB,UAAU,GAAG;QAAEoB,OAAO,EAAE;MAAE,CAAC,GAAG,CAAC,CAAE;MAC1CE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAE1B1B,OAAA;QAAKyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD1B,OAAA;UAAKyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1B,OAAA;YAAIyB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrC,OAAA;UAAKyB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClET,IAAI,CAACgC,GAAG,CAAC,CAACK,MAAM,EAAEH,KAAK,kBACtBnD,OAAA,CAACP,MAAM,CAACiD,GAAG;YAETb,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAG,CAAE;YAC/BR,OAAO,EAAErB,UAAU,GAAG;cAAEoB,OAAO,EAAE,CAAC;cAAES,CAAC,EAAE;YAAE,CAAC,GAAG,CAAC,CAAE;YAChDP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEO,KAAK,EAAEW,KAAK,GAAG;YAAI,CAAE;YAClD1B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B1B,OAAA;cAAKyB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B1B,OAAA;gBACE+C,GAAG,EAAEO,MAAM,CAAClC,KAAM;gBAClB4B,GAAG,EAAEM,MAAM,CAACpC,IAAK;gBACjBO,SAAS,EAAC;cAAqG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACFrC,OAAA;gBAAKyB,SAAS,EAAC;cAA4J;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/K,CAAC,eACNrC,OAAA;cAAIyB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAE4B,MAAM,CAACpC;YAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ErC,OAAA;cAAGyB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAE4B,MAAM,CAACnC;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvErC,OAAA;cAAGyB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE4B,MAAM,CAACjC;YAAG;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAhBhDc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGjBrC,OAAA;MAASyB,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eACnF1B,OAAA;QAAKyB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjE1B,OAAA,CAACP,MAAM,CAACiD,GAAG;UACTb,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BK,WAAW,EAAE;YAAEd,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCP,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BY,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAApB,QAAA,gBAEzB1B,OAAA;YAAIyB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAE/D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAGyB,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJrC,OAAA,CAACP,MAAM,CAAC8D,MAAM;YACZC,UAAU,EAAE;cAAEH,KAAK,EAAE;YAAK,CAAE;YAC5BI,QAAQ,EAAE;cAAEJ,KAAK,EAAE;YAAK,CAAE;YAC1B5B,SAAS,EAAC,mIAAmI;YAAAC,QAAA,EAC9I;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnC,EAAA,CAxSID,SAAS;EAAA,QACiBP,SAAS,EACLA,SAAS,EACbA,SAAS,EACPA,SAAS;AAAA;AAAAgE,EAAA,GAJrCzD,SAAS;AA0Sf,eAAeA,SAAS;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}