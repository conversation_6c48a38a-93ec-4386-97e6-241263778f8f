{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{useInView}from'react-intersection-observer';import{HeartIcon,LightBulbIcon,ShieldCheckIcon,GlobeAltIcon// UsersIcon,\n// TrophyIcon\n}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AboutPage=()=>{const[heroRef,heroInView]=useInView({threshold:0.1,triggerOnce:true});const[valuesRef,valuesInView]=useInView({threshold:0.1,triggerOnce:true});const[teamRef,teamInView]=useInView({threshold:0.1,triggerOnce:true});const[statsRef,statsInView]=useInView({threshold:0.1,triggerOnce:true});const values=[{icon:HeartIcon,title:'Customer First',description:'We put our customers at the heart of everything we do, ensuring exceptional service and satisfaction.'},{icon:LightBulbIcon,title:'Innovation',description:'We constantly innovate to bring you the latest and greatest products with cutting-edge technology.'},{icon:ShieldCheckIcon,title:'Quality Assurance',description:'Every product goes through rigorous quality checks to ensure you receive only the best.'},{icon:GlobeAltIcon,title:'Global Reach',description:'We serve customers worldwide with fast, reliable shipping and local support.'}];const team=[{name:'Sarah Johnson',role:'CEO & Founder',image:'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300',bio:'Visionary leader with 15+ years in e-commerce and retail innovation.'},{name:'Michael Chen',role:'CTO',image:'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300',bio:'Tech expert passionate about creating seamless digital experiences.'},{name:'Emily Rodriguez',role:'Head of Design',image:'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300',bio:'Creative designer focused on user-centered design and accessibility.'},{name:'David Kim',role:'VP of Operations',image:'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300',bio:'Operations specialist ensuring smooth logistics and customer satisfaction.'}];const stats=[{number:'1M+',label:'Happy Customers'},{number:'50K+',label:'Products Sold'},{number:'99.9%',label:'Uptime'},{number:'24/7',label:'Support'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen\",children:[/*#__PURE__*/_jsxs(motion.section,{ref:heroRef,initial:{opacity:0},animate:heroInView?{opacity:1}:{},transition:{duration:1},className:\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full animate-float\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-20 right-20 w-24 h-24 bg-white bg-opacity-10 rounded-full animate-float-delayed\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(motion.h1,{initial:{y:50,opacity:0},animate:heroInView?{y:0,opacity:1}:{},transition:{duration:0.8,delay:0.2},className:\"text-4xl lg:text-6xl font-bold text-white mb-6\",children:\"About ShopHub\"}),/*#__PURE__*/_jsx(motion.p,{initial:{y:50,opacity:0},animate:heroInView?{y:0,opacity:1}:{},transition:{duration:0.8,delay:0.4},className:\"text-xl lg:text-2xl text-light-orange-100 max-w-3xl mx-auto leading-relaxed\",children:\"We're passionate about bringing you the best products with exceptional service. Our journey started with a simple mission: to make online shopping delightful and accessible for everyone.\"})]})})]}),/*#__PURE__*/_jsx(\"section\",{className:\"py-20 bg-white\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{x:-50,opacity:0},whileInView:{x:0,opacity:1},transition:{duration:0.8},viewport:{once:true},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\",children:\"Our Story\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4 text-lg text-gray-600\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Founded in 2020, ShopHub began as a small startup with big dreams. We noticed that online shopping could be so much better \\u2013 more personal, more reliable, and more enjoyable.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Today, we've grown into a trusted platform serving millions of customers worldwide. But our core values remain the same: putting customers first, maintaining the highest quality standards, and continuously innovating.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Every product we sell, every feature we build, and every interaction we have is guided by our commitment to excellence and customer satisfaction.\"})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{x:50,opacity:0},whileInView:{x:0,opacity:1},transition:{duration:0.8},viewport:{once:true},className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600\",alt:\"Our team working together\",className:\"rounded-2xl shadow-2xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -bottom-6 -left-6 w-full h-full bg-gradient-to-br from-light-orange-200 to-light-orange-300 rounded-2xl -z-10\"})]})]})})}),/*#__PURE__*/_jsx(motion.section,{ref:valuesRef,initial:{opacity:0},animate:valuesInView?{opacity:1}:{},transition:{duration:0.8},className:\"py-20 bg-gradient-to-br from-light-orange-50 to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"Our Values\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"These core principles guide everything we do and shape our company culture\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:values.map((value,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},animate:valuesInView?{opacity:1,y:0}:{},transition:{duration:0.6,delay:index*0.1},className:\"text-center group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",children:/*#__PURE__*/_jsx(value.icon,{className:\"w-10 h-10 text-light-orange-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-3\",children:value.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:value.description})]},index))})]})}),/*#__PURE__*/_jsx(motion.section,{ref:statsRef,initial:{opacity:0},animate:statsInView?{opacity:1}:{},transition:{duration:0.8},className:\"py-20 bg-gray-900\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-white mb-4\",children:\"Our Impact\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-300 max-w-2xl mx-auto\",children:\"Numbers that reflect our commitment to excellence\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 lg:grid-cols-4 gap-8\",children:stats.map((stat,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.5},animate:statsInView?{opacity:1,scale:1}:{},transition:{duration:0.6,delay:index*0.1},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl lg:text-5xl font-bold text-light-orange-400 mb-2\",children:stat.number}),/*#__PURE__*/_jsx(\"div\",{className:\"text-lg text-gray-300\",children:stat.label})]},index))})]})}),/*#__PURE__*/_jsx(motion.section,{ref:teamRef,initial:{opacity:0},animate:teamInView?{opacity:1}:{},transition:{duration:0.8},className:\"py-20 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"Meet Our Team\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"The passionate people behind ShopHub's success\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:team.map((member,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},animate:teamInView?{opacity:1,y:0}:{},transition:{duration:0.6,delay:index*0.1},className:\"text-center group\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative mb-6\",children:[/*#__PURE__*/_jsx(\"img\",{src:member.image,alt:member.name,className:\"w-32 h-32 rounded-full mx-auto object-cover group-hover:scale-105 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-br from-light-orange-400 to-light-orange-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"})]}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-1\",children:member.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-light-orange-600 font-medium mb-3\",children:member.role}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:member.bio})]},index))})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"py-20 bg-gradient-to-r from-light-orange-500 to-light-orange-600\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:0.8},viewport:{once:true},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-white mb-6\",children:\"Ready to Start Shopping?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-light-orange-100 mb-8 max-w-2xl mx-auto\",children:\"Join millions of satisfied customers and discover amazing products at unbeatable prices.\"}),/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",children:\"Shop Now\"})]})})})]});};export default AboutPage;", "map": {"version": 3, "names": ["React", "motion", "useInView", "HeartIcon", "LightBulbIcon", "ShieldCheckIcon", "GlobeAltIcon", "jsx", "_jsx", "jsxs", "_jsxs", "AboutPage", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "valuesRef", "valuesInView", "teamRef", "teamInView", "statsRef", "statsInView", "values", "icon", "title", "description", "team", "name", "role", "image", "bio", "stats", "number", "label", "className", "children", "section", "ref", "initial", "opacity", "animate", "transition", "duration", "h1", "y", "delay", "p", "div", "x", "whileInView", "viewport", "once", "src", "alt", "map", "value", "index", "stat", "scale", "member", "button", "whileHover", "whileTap"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AboutPage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  HeartIcon, \n  LightBulbIcon, \n  ShieldCheckIcon,\n  GlobeAltIcon,\n  // UsersIcon,\n  // TrophyIcon\n} from '@heroicons/react/24/outline';\n\nconst AboutPage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [valuesRef, valuesInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [teamRef, teamInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [statsRef, statsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n\n  const values = [\n    {\n      icon: HeartIcon,\n      title: 'Customer First',\n      description: 'We put our customers at the heart of everything we do, ensuring exceptional service and satisfaction.'\n    },\n    {\n      icon: LightBulbIcon,\n      title: 'Innovation',\n      description: 'We constantly innovate to bring you the latest and greatest products with cutting-edge technology.'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Quality Assurance',\n      description: 'Every product goes through rigorous quality checks to ensure you receive only the best.'\n    },\n    {\n      icon: GlobeAltIcon,\n      title: 'Global Reach',\n      description: 'We serve customers worldwide with fast, reliable shipping and local support.'\n    }\n  ];\n\n  const team = [\n    {\n      name: 'Sarah Johnson',\n      role: 'CEO & Founder',\n      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300',\n      bio: 'Visionary leader with 15+ years in e-commerce and retail innovation.'\n    },\n    {\n      name: 'Michael Chen',\n      role: 'CTO',\n      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300',\n      bio: 'Tech expert passionate about creating seamless digital experiences.'\n    },\n    {\n      name: 'Emily Rodriguez',\n      role: 'Head of Design',\n      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300',\n      bio: 'Creative designer focused on user-centered design and accessibility.'\n    },\n    {\n      name: 'David Kim',\n      role: 'VP of Operations',\n      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300',\n      bio: 'Operations specialist ensuring smooth logistics and customer satisfaction.'\n    }\n  ];\n\n  const stats = [\n    { number: '1M+', label: 'Happy Customers' },\n    { number: '50K+', label: 'Products Sold' },\n    { number: '99.9%', label: 'Uptime' },\n    { number: '24/7', label: 'Support' }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-white bg-opacity-10 rounded-full animate-float\"></div>\n          <div className=\"absolute bottom-20 right-20 w-24 h-24 bg-white bg-opacity-10 rounded-full animate-float-delayed\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"text-center\">\n            <motion.h1\n              initial={{ y: 50, opacity: 0 }}\n              animate={heroInView ? { y: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-4xl lg:text-6xl font-bold text-white mb-6\"\n            >\n              About ShopHub\n            </motion.h1>\n            <motion.p\n              initial={{ y: 50, opacity: 0 }}\n              animate={heroInView ? { y: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"text-xl lg:text-2xl text-light-orange-100 max-w-3xl mx-auto leading-relaxed\"\n            >\n              We're passionate about bringing you the best products with exceptional service. \n              Our journey started with a simple mission: to make online shopping delightful and accessible for everyone.\n            </motion.p>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Story Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -50, opacity: 0 }}\n              whileInView={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\">\n                Our Story\n              </h2>\n              <div className=\"space-y-4 text-lg text-gray-600\">\n                <p>\n                  Founded in 2020, ShopHub began as a small startup with big dreams. \n                  We noticed that online shopping could be so much better – more personal, \n                  more reliable, and more enjoyable.\n                </p>\n                <p>\n                  Today, we've grown into a trusted platform serving millions of customers \n                  worldwide. But our core values remain the same: putting customers first, \n                  maintaining the highest quality standards, and continuously innovating.\n                </p>\n                <p>\n                  Every product we sell, every feature we build, and every interaction we have \n                  is guided by our commitment to excellence and customer satisfaction.\n                </p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 50, opacity: 0 }}\n              whileInView={{ x: 0, opacity: 1 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <img\n                src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600\"\n                alt=\"Our team working together\"\n                className=\"rounded-2xl shadow-2xl\"\n              />\n              <div className=\"absolute -bottom-6 -left-6 w-full h-full bg-gradient-to-br from-light-orange-200 to-light-orange-300 rounded-2xl -z-10\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Values Section */}\n      <motion.section\n        ref={valuesRef}\n        initial={{ opacity: 0 }}\n        animate={valuesInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Our Values\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              These core principles guide everything we do and shape our company culture\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {values.map((value, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={valuesInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <value.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{value.title}</h3>\n                <p className=\"text-gray-600\">{value.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section\n        ref={statsRef}\n        initial={{ opacity: 0 }}\n        animate={statsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gray-900\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\n              Our Impact\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n              Numbers that reflect our commitment to excellence\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, scale: 0.5 }}\n                animate={statsInView ? { opacity: 1, scale: 1 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-4xl lg:text-5xl font-bold text-light-orange-400 mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-lg text-gray-300\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Team Section */}\n      <motion.section\n        ref={teamRef}\n        initial={{ opacity: 0 }}\n        animate={teamInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Meet Our Team\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              The passionate people behind ShopHub's success\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {team.map((member, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={teamInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"relative mb-6\">\n                  <img\n                    src={member.image}\n                    alt={member.name}\n                    className=\"w-32 h-32 rounded-full mx-auto object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-light-orange-400 to-light-orange-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300\"></div>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-1\">{member.name}</h3>\n                <p className=\"text-light-orange-600 font-medium mb-3\">{member.role}</p>\n                <p className=\"text-gray-600 text-sm\">{member.bio}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-light-orange-500 to-light-orange-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-white mb-6\">\n              Ready to Start Shopping?\n            </h2>\n            <p className=\"text-xl text-light-orange-100 mb-8 max-w-2xl mx-auto\">\n              Join millions of satisfied customers and discover amazing products at unbeatable prices.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              Shop Now\n            </motion.button>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OACEC,SAAS,CACTC,aAAa,CACbC,eAAe,CACfC,YACA;AACA;AAAA,KACK,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGX,SAAS,CAAC,CAAEY,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAC9E,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGf,SAAS,CAAC,CAAEY,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAClF,KAAM,CAACG,OAAO,CAAEC,UAAU,CAAC,CAAGjB,SAAS,CAAC,CAAEY,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAC9E,KAAM,CAACK,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,SAAS,CAAC,CAAEY,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAEhF,KAAM,CAAAO,MAAM,CAAG,CACb,CACEC,IAAI,CAAEpB,SAAS,CACfqB,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,uGACf,CAAC,CACD,CACEF,IAAI,CAAEnB,aAAa,CACnBoB,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,oGACf,CAAC,CACD,CACEF,IAAI,CAAElB,eAAe,CACrBmB,KAAK,CAAE,mBAAmB,CAC1BC,WAAW,CAAE,yFACf,CAAC,CACD,CACEF,IAAI,CAAEjB,YAAY,CAClBkB,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,8EACf,CAAC,CACF,CAED,KAAM,CAAAC,IAAI,CAAG,CACX,CACEC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,oEAAoE,CAC3EC,GAAG,CAAE,sEACP,CAAC,CACD,CACEH,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE,KAAK,CACXC,KAAK,CAAE,oEAAoE,CAC3EC,GAAG,CAAE,qEACP,CAAC,CACD,CACEH,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAE,gBAAgB,CACtBC,KAAK,CAAE,oEAAoE,CAC3EC,GAAG,CAAE,sEACP,CAAC,CACD,CACEH,IAAI,CAAE,WAAW,CACjBC,IAAI,CAAE,kBAAkB,CACxBC,KAAK,CAAE,oEAAoE,CAC3EC,GAAG,CAAE,4EACP,CAAC,CACF,CAED,KAAM,CAAAC,KAAK,CAAG,CACZ,CAAEC,MAAM,CAAE,KAAK,CAAEC,KAAK,CAAE,iBAAkB,CAAC,CAC3C,CAAED,MAAM,CAAE,MAAM,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC1C,CAAED,MAAM,CAAE,OAAO,CAAEC,KAAK,CAAE,QAAS,CAAC,CACpC,CAAED,MAAM,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CACrC,CAED,mBACEvB,KAAA,QAAKwB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE3BzB,KAAA,CAACT,MAAM,CAACmC,OAAO,EACbC,GAAG,CAAEzB,OAAQ,CACb0B,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE3B,UAAU,CAAG,CAAE0B,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC1CE,UAAU,CAAE,CAAEC,QAAQ,CAAE,CAAE,CAAE,CAC5BR,SAAS,CAAC,2GAA2G,CAAAC,QAAA,eAErHzB,KAAA,QAAKwB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B3B,IAAA,QAAK0B,SAAS,CAAC,qFAAqF,CAAM,CAAC,cAC3G1B,IAAA,QAAK0B,SAAS,CAAC,iGAAiG,CAAM,CAAC,EACpH,CAAC,cAEN1B,IAAA,QAAK0B,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAC7EzB,KAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3B,IAAA,CAACP,MAAM,CAAC0C,EAAE,EACRL,OAAO,CAAE,CAAEM,CAAC,CAAE,EAAE,CAAEL,OAAO,CAAE,CAAE,CAAE,CAC/BC,OAAO,CAAE3B,UAAU,CAAG,CAAE+B,CAAC,CAAE,CAAC,CAAEL,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAChDE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEG,KAAK,CAAE,GAAI,CAAE,CAC1CX,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAC3D,eAED,CAAW,CAAC,cACZ3B,IAAA,CAACP,MAAM,CAAC6C,CAAC,EACPR,OAAO,CAAE,CAAEM,CAAC,CAAE,EAAE,CAAEL,OAAO,CAAE,CAAE,CAAE,CAC/BC,OAAO,CAAE3B,UAAU,CAAG,CAAE+B,CAAC,CAAE,CAAC,CAAEL,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAChDE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEG,KAAK,CAAE,GAAI,CAAE,CAC1CX,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CACxF,4LAGD,CAAU,CAAC,EACR,CAAC,CACH,CAAC,EACQ,CAAC,cAGjB3B,IAAA,YAAS0B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cACjC3B,IAAA,QAAK0B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDzB,KAAA,QAAKwB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEzB,KAAA,CAACT,MAAM,CAAC8C,GAAG,EACTT,OAAO,CAAE,CAAEU,CAAC,CAAE,CAAC,EAAE,CAAET,OAAO,CAAE,CAAE,CAAE,CAChCU,WAAW,CAAE,CAAED,CAAC,CAAE,CAAC,CAAET,OAAO,CAAE,CAAE,CAAE,CAClCE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BQ,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAE,CAAAhB,QAAA,eAEzB3B,IAAA,OAAI0B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,WAElE,CAAI,CAAC,cACLzB,KAAA,QAAKwB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3B,IAAA,MAAA2B,QAAA,CAAG,qLAIH,CAAG,CAAC,cACJ3B,IAAA,MAAA2B,QAAA,CAAG,2NAIH,CAAG,CAAC,cACJ3B,IAAA,MAAA2B,QAAA,CAAG,mJAGH,CAAG,CAAC,EACD,CAAC,EACI,CAAC,cAEbzB,KAAA,CAACT,MAAM,CAAC8C,GAAG,EACTT,OAAO,CAAE,CAAEU,CAAC,CAAE,EAAE,CAAET,OAAO,CAAE,CAAE,CAAE,CAC/BU,WAAW,CAAE,CAAED,CAAC,CAAE,CAAC,CAAET,OAAO,CAAE,CAAE,CAAE,CAClCE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BQ,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAE,CACzBjB,SAAS,CAAC,UAAU,CAAAC,QAAA,eAEpB3B,IAAA,QACE4C,GAAG,CAAC,oEAAoE,CACxEC,GAAG,CAAC,2BAA2B,CAC/BnB,SAAS,CAAC,wBAAwB,CACnC,CAAC,cACF1B,IAAA,QAAK0B,SAAS,CAAC,wHAAwH,CAAM,CAAC,EACpI,CAAC,EACV,CAAC,CACH,CAAC,CACC,CAAC,cAGV1B,IAAA,CAACP,MAAM,CAACmC,OAAO,EACbC,GAAG,CAAErB,SAAU,CACfsB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAEvB,YAAY,CAAG,CAAEsB,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC5CE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cAEjEzB,KAAA,QAAKwB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzB,KAAA,QAAKwB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3B,IAAA,OAAI0B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,YAElE,CAAI,CAAC,cACL3B,IAAA,MAAG0B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,4EAEvD,CAAG,CAAC,EACD,CAAC,cAEN3B,IAAA,QAAK0B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEb,MAAM,CAACgC,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACvB9C,KAAA,CAACT,MAAM,CAAC8C,GAAG,EAETT,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/BJ,OAAO,CAAEvB,YAAY,CAAG,CAAEsB,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAClDH,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEG,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDtB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7B3B,IAAA,QAAK0B,SAAS,CAAC,0LAA0L,CAAAC,QAAA,cACvM3B,IAAA,CAAC+C,KAAK,CAAChC,IAAI,EAACW,SAAS,CAAC,iCAAiC,CAAE,CAAC,CACvD,CAAC,cACN1B,IAAA,OAAI0B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEoB,KAAK,CAAC/B,KAAK,CAAK,CAAC,cAC3EhB,IAAA,MAAG0B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEoB,KAAK,CAAC9B,WAAW,CAAI,CAAC,GAV/C+B,KAWK,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBhD,IAAA,CAACP,MAAM,CAACmC,OAAO,EACbC,GAAG,CAAEjB,QAAS,CACdkB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAEnB,WAAW,CAAG,CAAEkB,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC3CE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BzB,KAAA,QAAKwB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzB,KAAA,QAAKwB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3B,IAAA,OAAI0B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,YAE/D,CAAI,CAAC,cACL3B,IAAA,MAAG0B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,mDAEvD,CAAG,CAAC,EACD,CAAC,cAEN3B,IAAA,QAAK0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDJ,KAAK,CAACuB,GAAG,CAAC,CAACG,IAAI,CAAED,KAAK,gBACrB9C,KAAA,CAACT,MAAM,CAAC8C,GAAG,EAETT,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEmB,KAAK,CAAE,GAAI,CAAE,CACpClB,OAAO,CAAEnB,WAAW,CAAG,CAAEkB,OAAO,CAAE,CAAC,CAAEmB,KAAK,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CACrDjB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEG,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDtB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEvB3B,IAAA,QAAK0B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CACvEsB,IAAI,CAACzB,MAAM,CACT,CAAC,cACNxB,IAAA,QAAK0B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEsB,IAAI,CAACxB,KAAK,CAAM,CAAC,GATpDuB,KAUK,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBhD,IAAA,CAACP,MAAM,CAACmC,OAAO,EACbC,GAAG,CAAEnB,OAAQ,CACboB,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAErB,UAAU,CAAG,CAAEoB,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC1CE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAE1BzB,KAAA,QAAKwB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzB,KAAA,QAAKwB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3B,IAAA,OAAI0B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,eAElE,CAAI,CAAC,cACL3B,IAAA,MAAG0B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,gDAEvD,CAAG,CAAC,EACD,CAAC,cAEN3B,IAAA,QAAK0B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClET,IAAI,CAAC4B,GAAG,CAAC,CAACK,MAAM,CAAEH,KAAK,gBACtB9C,KAAA,CAACT,MAAM,CAAC8C,GAAG,EAETT,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/BJ,OAAO,CAAErB,UAAU,CAAG,CAAEoB,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAChDH,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEG,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDtB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7BzB,KAAA,QAAKwB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3B,IAAA,QACE4C,GAAG,CAAEO,MAAM,CAAC9B,KAAM,CAClBwB,GAAG,CAAEM,MAAM,CAAChC,IAAK,CACjBO,SAAS,CAAC,qGAAqG,CAChH,CAAC,cACF1B,IAAA,QAAK0B,SAAS,CAAC,4JAA4J,CAAM,CAAC,EAC/K,CAAC,cACN1B,IAAA,OAAI0B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEwB,MAAM,CAAChC,IAAI,CAAK,CAAC,cAC3EnB,IAAA,MAAG0B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAEwB,MAAM,CAAC/B,IAAI,CAAI,CAAC,cACvEpB,IAAA,MAAG0B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEwB,MAAM,CAAC7B,GAAG,CAAI,CAAC,GAhBhD0B,KAiBK,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBhD,IAAA,YAAS0B,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cACnF3B,IAAA,QAAK0B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjEzB,KAAA,CAACT,MAAM,CAAC8C,GAAG,EACTT,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/BK,WAAW,CAAE,CAAEV,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAE,CAClCH,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BQ,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAE,CAAAhB,QAAA,eAEzB3B,IAAA,OAAI0B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,0BAE/D,CAAI,CAAC,cACL3B,IAAA,MAAG0B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAAC,0FAEpE,CAAG,CAAC,cACJ3B,IAAA,CAACP,MAAM,CAAC2D,MAAM,EACZC,UAAU,CAAE,CAAEH,KAAK,CAAE,IAAK,CAAE,CAC5BI,QAAQ,CAAE,CAAEJ,KAAK,CAAE,IAAK,CAAE,CAC1BxB,SAAS,CAAC,mIAAmI,CAAAC,QAAA,CAC9I,UAED,CAAe,CAAC,EACN,CAAC,CACV,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}