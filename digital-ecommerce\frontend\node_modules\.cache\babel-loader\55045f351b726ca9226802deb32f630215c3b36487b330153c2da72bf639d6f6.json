{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"title\", \"titleId\"];\nimport * as React from \"react\";\nfunction Square3Stack3DIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.644 1.59a.75.75 0 0 1 .712 0l9.75 5.25a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.712 0l-9.75-5.25a.75.75 0 0 1 0-1.32l9.75-5.25Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m3.265 10.602 7.668 4.129a2.25 2.25 0 0 0 2.134 0l7.668-4.13 1.37.739a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.71 0l-9.75-5.25a.75.75 0 0 1 0-1.32l1.37-.738Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m10.933 19.231-7.668-4.13-1.37.739a.75.75 0 0 0 0 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 0 0 0-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 0 1-2.134-.001Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(Square3Stack3DIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "Square3Stack3DIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "d", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@heroicons/react/24/solid/esm/Square3Stack3DIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction Square3Stack3DIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.644 1.59a.75.75 0 0 1 .712 0l9.75 5.25a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.712 0l-9.75-5.25a.75.75 0 0 1 0-1.32l9.75-5.25Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m3.265 10.602 7.668 4.129a2.25 2.25 0 0 0 2.134 0l7.668-4.13 1.37.739a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.71 0l-9.75-5.25a.75.75 0 0 1 0-1.32l1.37-.738Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"m10.933 19.231-7.668-4.13-1.37.739a.75.75 0 0 0 0 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 0 0 0-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 0 1-2.134-.001Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(Square3Stack3DIcon);\nexport default ForwardRef;"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkBA,CAAAC,IAAA,EAIxBC,MAAM,EAAE;EAAA,IAJiB;MAC1BC,KAAK;MACLC;IAEF,CAAC,GAAAH,IAAA;IADII,KAAK,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAER,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEZ,MAAM;IACX,iBAAiB,EAAEE;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaJ,KAAK,CAACS,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEX;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaJ,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IACzDQ,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAajB,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC3CQ,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAajB,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC3CQ,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAclB,KAAK,CAACmB,UAAU,CAAClB,kBAAkB,CAAC;AACrE,eAAeiB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}