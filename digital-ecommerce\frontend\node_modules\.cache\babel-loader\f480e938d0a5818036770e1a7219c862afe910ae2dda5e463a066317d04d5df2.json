{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useUser } from '../contexts/UserContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requireAuth = true\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useUser();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If user is authenticated but trying to access auth pages (login, register)\n  if (!requireAuth && isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/account\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"14+cZN6tnAzYr5ztJXtkOd9dhEg=\", false, function () {\n  return [useUser, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useUser", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requireAuth", "_s", "isAuthenticated", "isLoading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useUser } from '../contexts/UserContext';\n\nconst ProtectedRoute = ({ children, requireAuth = true }) => {\n  const { isAuthenticated, isLoading } = useUser();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-light-orange-500\"></div>\n      </div>\n    );\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // If user is authenticated but trying to access auth pages (login, register)\n  if (!requireAuth && isAuthenticated) {\n    return <Navigate to=\"/account\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGR,OAAO,CAAC,CAAC;EAChD,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIS,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKQ,SAAS,EAAC,0DAA0D;MAAAN,QAAA,eACvEF,OAAA;QAAKQ,SAAS,EAAC;MAAwE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAEV;;EAEA;EACA,IAAIT,WAAW,IAAI,CAACE,eAAe,EAAE;IACnC,oBAAOL,OAAA,CAACJ,QAAQ;MAACiB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAI,CAACT,WAAW,IAAIE,eAAe,EAAE;IACnC,oBAAOL,OAAA,CAACJ,QAAQ;MAACiB,EAAE,EAAC,UAAU;MAACG,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3C;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACE,EAAA,CAxBIH,cAAc;EAAA,QACqBH,OAAO,EAC7BD,WAAW;AAAA;AAAAoB,EAAA,GAFxBhB,cAAc;AA0BpB,eAAeA,cAAc;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}