{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, ShoppingBagIcon, MagnifyingGlassIcon, UserIcon, HeartIcon, HomeIcon, TagIcon, PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport ShoppingCart, { useCart } from './ShoppingCart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const {\n    totalItems\n  } = useCart();\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navigationItems = [{\n    name: 'Home',\n    href: '/',\n    icon: HomeIcon\n  }, {\n    name: 'Products',\n    href: '/products',\n    icon: TagIcon\n  }, {\n    name: 'Digital',\n    href: '/digital-products',\n    icon: TagIcon\n  }, {\n    name: 'About',\n    href: '/about',\n    icon: InformationCircleIcon\n  }, {\n    name: 'Contact',\n    href: '/contact',\n    icon: PhoneIcon\n  }];\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                rotate: 360\n              },\n              transition: {\n                duration: 0.5\n              },\n              className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-2xl font-bold transition-colors duration-300 ${isScrolled ? 'text-gray-900' : 'text-white'}`,\n              children: \"ShopHub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:flex items-center space-x-8\",\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${isActive(item.href) ? isScrolled ? 'text-light-orange-600' : 'text-yellow-300' : isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: [item.name, isActive(item.href) && /*#__PURE__*/_jsxDEV(motion.div, {\n                layoutId: \"activeTab\",\n                className: `absolute bottom-0 left-0 right-0 h-0.5 ${isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 21\n              }, this)]\n            }, item.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center flex-1 max-w-md mx-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: `h-5 w-5 ${isScrolled ? 'text-gray-400' : 'text-white/70'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: `w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${isScrolled ? 'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300' : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.1\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n              children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/account\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                className: `p-2 rounded-full transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' : 'text-white hover:text-yellow-300 hover:bg-white/10'}`,\n                children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(!isOpen),\n              className: `lg:hidden p-2 rounded-md transition-colors duration-300 ${isScrolled ? 'text-gray-700 hover:text-light-orange-600' : 'text-white hover:text-yellow-300'}`,\n              children: isOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: \"lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-6 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search products...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full pl-10 pr-4 py-3 bg-gray-100 text-gray-900 placeholder-gray-500 rounded-lg focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setIsOpen(false),\n                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${isActive(item.href) ? 'bg-light-orange-100 text-light-orange-700' : 'text-gray-700 hover:bg-gray-100'}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-around pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Wishlist\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/account\",\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/cart\",\n                className: \"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs\",\n                  children: \"Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 lg:h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navigation, \"QRLclQSPTq6wy5JUbRgtAmdXAdg=\", false, function () {\n  return [useLocation, useCart];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ShoppingCart", "useCart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Navigation", "_s", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "totalItems", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "to", "div", "whileHover", "rotate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "layoutId", "type", "placeholder", "value", "onChange", "e", "target", "button", "scale", "whileTap", "onClick", "opacity", "height", "exit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart, { useCart } from './ShoppingCart';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  const { totalItems } = useCart();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled \n            ? 'bg-white/95 backdrop-blur-md shadow-lg' \n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\"\n              >\n                <ShoppingBagIcon className=\"w-6 h-6 text-white\" />\n              </motion.div>\n              <span className={`text-2xl font-bold transition-colors duration-300 ${\n                isScrolled ? 'text-gray-900' : 'text-white'\n              }`}>\n                ShopHub\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${\n                    isActive(item.href)\n                      ? isScrolled \n                        ? 'text-light-orange-600' \n                        : 'text-yellow-300'\n                      : isScrolled \n                        ? 'text-gray-700 hover:text-light-orange-600' \n                        : 'text-white hover:text-yellow-300'\n                  }`}\n                >\n                  {item.name}\n                  {isActive(item.href) && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${\n                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'\n                      }`}\n                    />\n                  )}\n                </Link>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 ${\n                    isScrolled ? 'text-gray-400' : 'text-white/70'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search products...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${\n                    isScrolled\n                      ? 'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300'\n                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'\n                  }`}\n                />\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                className={`p-2 rounded-full transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' \n                    : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                }`}\n              >\n                <HeartIcon className=\"w-6 h-6\" />\n              </motion.button>\n\n              <Link to=\"/account\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className={`p-2 rounded-full transition-colors duration-300 ${\n                    isScrolled \n                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' \n                      : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                  }`}\n                >\n                  <UserIcon className=\"w-6 h-6\" />\n                </motion.button>\n              </Link>\n\n              <ShoppingCart />\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-200\"\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 bg-gray-100 text-gray-900 placeholder-gray-500 rounded-lg focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-light-orange-100 text-light-orange-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"flex items-center justify-around pt-4 border-t border-gray-200\">\n                  <button className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <Link to=\"/cart\" className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <div className=\"relative\">\n                      <ShoppingBagIcon className=\"w-6 h-6\" />\n                      <span className=\"absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-4 h-4 flex items-center justify-center\">\n                        3\n                      </span>\n                    </div>\n                    <span className=\"text-xs\">Cart</span>\n                  </Link>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,qBAAqB,QAChB,6BAA6B;AACpC,OAAOC,YAAY,IAAIC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM8B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAW,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEhChB,SAAS,CAAC,MAAM;IACd,MAAM+B,YAAY,GAAGA,CAAA,KAAM;MACzBL,aAAa,CAACM,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,EAAE5B;EAAS,CAAC,EAC3C;IAAE0B,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE3B;EAAQ,CAAC,EACtD;IAAEyB,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,EAAE3B;EAAQ,CAAC,EAC7D;IAAEyB,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEzB;EAAsB,CAAC,EAC9D;IAAEuB,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE1B;EAAU,CAAC,CACvD;EAED,MAAM2B,QAAQ,GAAIC,IAAI,IAAKZ,QAAQ,CAACa,QAAQ,KAAKD,IAAI;EAErD,oBACEvB,OAAA,CAAAE,SAAA;IAAAuB,QAAA,gBACEzB,OAAA,CAACf,MAAM,CAACyC,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBE,SAAS,EAAE,+DACTvB,UAAU,GACN,wCAAwC,GACxC,gBAAgB,EACnB;MAAAkB,QAAA,gBAEHzB,OAAA;QAAK8B,SAAS,EAAC,wCAAwC;QAAAL,QAAA,eACrDzB,OAAA;UAAK8B,SAAS,EAAC,gDAAgD;UAAAL,QAAA,gBAE7DzB,OAAA,CAACjB,IAAI;YAACgD,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAClDzB,OAAA,CAACf,MAAM,CAAC+C,GAAG;cACTC,UAAU,EAAE;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAC5BC,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC9BN,SAAS,EAAC,oHAAoH;cAAAL,QAAA,eAE9HzB,OAAA,CAACX,eAAe;gBAACyC,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACbxC,OAAA;cAAM8B,SAAS,EAAE,qDACfvB,UAAU,GAAG,eAAe,GAAG,YAAY,EAC1C;cAAAkB,QAAA,EAAC;YAEJ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPxC,OAAA;YAAK8B,SAAS,EAAC,uCAAuC;YAAAL,QAAA,EACnDP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxB1C,OAAA,CAACjB,IAAI;cAEHgD,EAAE,EAAEW,IAAI,CAACtB,IAAK;cACdU,SAAS,EAAE,yEACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACfb,UAAU,GACR,uBAAuB,GACvB,iBAAiB,GACnBA,UAAU,GACR,2CAA2C,GAC3C,kCAAkC,EACvC;cAAAkB,QAAA,GAEFiB,IAAI,CAACvB,IAAI,EACTG,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,iBAClBpB,OAAA,CAACf,MAAM,CAAC+C,GAAG;gBACTW,QAAQ,EAAC,WAAW;gBACpBb,SAAS,EAAE,0CACTvB,UAAU,GAAG,qBAAqB,GAAG,eAAe;cACnD;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACF;YAAA,GApBIE,IAAI,CAACvB,IAAI;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxC,OAAA;YAAK8B,SAAS,EAAC,kDAAkD;YAAAL,QAAA,eAC/DzB,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAL,QAAA,gBAC9BzB,OAAA;gBAAK8B,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFzB,OAAA,CAACV,mBAAmB;kBAACwC,SAAS,EAAE,WAC9BvB,UAAU,GAAG,eAAe,GAAG,eAAe;gBAC7C;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAErC,WAAY;gBACnBsC,QAAQ,EAAGC,CAAC,IAAKtC,cAAc,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDhB,SAAS,EAAE,mEACTvB,UAAU,GACN,wGAAwG,GACxG,iHAAiH;cACpH;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxC,OAAA;YAAK8B,SAAS,EAAC,6BAA6B;YAAAL,QAAA,gBAC1CzB,OAAA,CAACf,MAAM,CAACiE,MAAM;cACZjB,UAAU,EAAE;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cAC3BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAI,CAAE;cACzBrB,SAAS,EAAE,mDACTvB,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;cAAAkB,QAAA,eAEHzB,OAAA,CAACR,SAAS;gBAACsC,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAEhBxC,OAAA,CAACjB,IAAI;cAACgD,EAAE,EAAC,UAAU;cAAAN,QAAA,eACjBzB,OAAA,CAACf,MAAM,CAACiE,MAAM;gBACZjB,UAAU,EAAE;kBAAEkB,KAAK,EAAE;gBAAI,CAAE;gBAC3BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAI,CAAE;gBACzBrB,SAAS,EAAE,mDACTvB,UAAU,GACN,oEAAoE,GACpE,oDAAoD,EACvD;gBAAAkB,QAAA,eAEHzB,OAAA,CAACT,QAAQ;kBAACuC,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEPxC,OAAA,CAACH,YAAY;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGhBxC,OAAA;cACEqD,OAAO,EAAEA,CAAA,KAAM/C,SAAS,CAAC,CAACD,MAAM,CAAE;cAClCyB,SAAS,EAAE,2DACTvB,UAAU,GACN,2CAA2C,GAC3C,kCAAkC,EACrC;cAAAkB,QAAA,EAEFpB,MAAM,gBACLL,OAAA,CAACZ,SAAS;gBAAC0C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjCxC,OAAA,CAACb,SAAS;gBAAC2C,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA,CAACd,eAAe;QAAAuC,QAAA,EACbpB,MAAM,iBACLL,OAAA,CAACf,MAAM,CAAC+C,GAAG;UACTL,OAAO,EAAE;YAAE2B,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACnC1B,OAAO,EAAE;YAAEyB,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAE;UACxCC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAChCzB,SAAS,EAAC,iEAAiE;UAAAL,QAAA,eAE3EzB,OAAA;YAAK8B,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAElCzB,OAAA;cAAK8B,SAAS,EAAC,UAAU;cAAAL,QAAA,gBACvBzB,OAAA;gBAAK8B,SAAS,EAAC,sEAAsE;gBAAAL,QAAA,eACnFzB,OAAA,CAACV,mBAAmB;kBAACwC,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNxC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCC,KAAK,EAAErC,WAAY;gBACnBsC,QAAQ,EAAGC,CAAC,IAAKtC,cAAc,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDhB,SAAS,EAAC;cAA0I;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxC,OAAA;cAAK8B,SAAS,EAAC,WAAW;cAAAL,QAAA,EACvBP,eAAe,CAACuB,GAAG,CAAEC,IAAI,iBACxB1C,OAAA,CAACjB,IAAI;gBAEHgD,EAAE,EAAEW,IAAI,CAACtB,IAAK;gBACdiC,OAAO,EAAEA,CAAA,KAAM/C,SAAS,CAAC,KAAK,CAAE;gBAChCwB,SAAS,EAAE,mFACTR,QAAQ,CAACoB,IAAI,CAACtB,IAAI,CAAC,GACf,2CAA2C,GAC3C,iCAAiC,EACpC;gBAAAK,QAAA,gBAEHzB,OAAA,CAAC0C,IAAI,CAACrB,IAAI;kBAACS,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxC,OAAA;kBAAM8B,SAAS,EAAC,aAAa;kBAAAL,QAAA,EAAEiB,IAAI,CAACvB;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAV3CE,IAAI,CAACvB,IAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxC,OAAA;cAAK8B,SAAS,EAAC,gEAAgE;cAAAL,QAAA,gBAC7EzB,OAAA;gBAAQ8B,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAChGzB,OAAA,CAACR,SAAS;kBAACsC,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxC,OAAA;kBAAM8B,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACTxC,OAAA,CAACjB,IAAI;gBAACgD,EAAE,EAAC,UAAU;gBAACD,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBAC5GzB,OAAA,CAACT,QAAQ;kBAACuC,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChCxC,OAAA;kBAAM8B,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACPxC,OAAA,CAACjB,IAAI;gBAACgD,EAAE,EAAC,OAAO;gBAACD,SAAS,EAAC,gFAAgF;gBAAAL,QAAA,gBACzGzB,OAAA;kBAAK8B,SAAS,EAAC,UAAU;kBAAAL,QAAA,gBACvBzB,OAAA,CAACX,eAAe;oBAACyC,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCxC,OAAA;oBAAM8B,SAAS,EAAC,iIAAiI;oBAAAL,QAAA,EAAC;kBAElJ;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxC,OAAA;kBAAM8B,SAAS,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGbxC,OAAA;MAAK8B,SAAS,EAAC;IAAc;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACpC,EAAA,CApOID,UAAU;EAAA,QAIGnB,WAAW,EACLc,OAAO;AAAA;AAAA2D,EAAA,GAL1BtD,UAAU;AAsOhB,eAAeA,UAAU;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}