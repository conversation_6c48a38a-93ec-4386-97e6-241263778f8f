{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nconst percentToProgress = percent => percent / 100;\nclass JSAnimation extends WithPromise {\n  constructor(options) {\n    super();\n    this.state = \"idle\";\n    this.startTime = null;\n    this.isStopped = false;\n    /**\n     * The current time of the animation.\n     */\n    this.currentTime = 0;\n    /**\n     * The time at which the animation was paused.\n     */\n    this.holdTime = null;\n    /**\n     * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n     */\n    this.playbackSpeed = 1;\n    /**\n     * This method is bound to the instance to fix a pattern where\n     * animation.stop is returned as a reference from a useEffect.\n     */\n    this.stop = () => {\n      var _this$options$onStop, _this$options;\n      const {\n        motionValue\n      } = this.options;\n      if (motionValue && motionValue.updatedAt !== time.now()) {\n        this.tick(time.now());\n      }\n      this.isStopped = true;\n      if (this.state === \"idle\") return;\n      this.teardown();\n      (_this$options$onStop = (_this$options = this.options).onStop) === null || _this$options$onStop === void 0 || _this$options$onStop.call(_this$options);\n    };\n    activeAnimations.mainThread++;\n    this.options = options;\n    this.initAnimation();\n    this.play();\n    if (options.autoplay === false) this.pause();\n  }\n  initAnimation() {\n    const {\n      options\n    } = this;\n    replaceTransitionType(options);\n    const {\n      type = keyframes,\n      repeat = 0,\n      repeatDelay = 0,\n      repeatType,\n      velocity = 0\n    } = options;\n    let {\n      keyframes: keyframes$1\n    } = options;\n    const generatorFactory = type || keyframes;\n    if (process.env.NODE_ENV !== \"production\" && generatorFactory !== keyframes) {\n      invariant(keyframes$1.length <= 2, \"Only two keyframes currently supported with spring and inertia animations. Trying to animate \".concat(keyframes$1));\n    }\n    if (generatorFactory !== keyframes && typeof keyframes$1[0] !== \"number\") {\n      this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n      keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory(_objectSpread(_objectSpread({}, options), {}, {\n      keyframes: keyframes$1\n    }));\n    /**\n     * If we have a mirror repeat type we need to create a second generator that outputs the\n     * mirrored (not reversed) animation and later ping pong between the two generators.\n     */\n    if (repeatType === \"mirror\") {\n      this.mirroredGenerator = generatorFactory(_objectSpread(_objectSpread({}, options), {}, {\n        keyframes: [...keyframes$1].reverse(),\n        velocity: -velocity\n      }));\n    }\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null) {\n      generator.calculatedDuration = calcGeneratorDuration(generator);\n    }\n    const {\n      calculatedDuration\n    } = generator;\n    this.calculatedDuration = calculatedDuration;\n    this.resolvedDuration = calculatedDuration + repeatDelay;\n    this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n    this.generator = generator;\n  }\n  updateTime(timestamp) {\n    const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n    // Update currentTime\n    if (this.holdTime !== null) {\n      this.currentTime = this.holdTime;\n    } else {\n      // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n      // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n      // example.\n      this.currentTime = animationTime;\n    }\n  }\n  tick(timestamp) {\n    let sample = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    const {\n      generator,\n      totalDuration,\n      mixKeyframes,\n      mirroredGenerator,\n      resolvedDuration,\n      calculatedDuration\n    } = this;\n    if (this.startTime === null) return generator.next(0);\n    const {\n      delay = 0,\n      keyframes,\n      repeat,\n      repeatType,\n      repeatDelay,\n      type,\n      onUpdate,\n      finalKeyframe\n    } = this.options;\n    /**\n     * requestAnimationFrame timestamps can come through as lower than\n     * the startTime as set by performance.now(). Here we prevent this,\n     * though in the future it could be possible to make setting startTime\n     * a pending operation that gets resolved here.\n     */\n    if (this.speed > 0) {\n      this.startTime = Math.min(this.startTime, timestamp);\n    } else if (this.speed < 0) {\n      this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n    }\n    if (sample) {\n      this.currentTime = timestamp;\n    } else {\n      this.updateTime(timestamp);\n    }\n    // Rebase on delay\n    const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n    const isInDelayPhase = this.playbackSpeed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n    this.currentTime = Math.max(timeWithoutDelay, 0);\n    // If this animation has finished, set the current time  to the total duration.\n    if (this.state === \"finished\" && this.holdTime === null) {\n      this.currentTime = totalDuration;\n    }\n    let elapsed = this.currentTime;\n    let frameGenerator = generator;\n    if (repeat) {\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      iterationProgress === 1 && currentIteration--;\n      currentIteration = Math.min(currentIteration, repeat + 1);\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const isOddIteration = Boolean(currentIteration % 2);\n      if (isOddIteration) {\n        if (repeatType === \"reverse\") {\n          iterationProgress = 1 - iterationProgress;\n          if (repeatDelay) {\n            iterationProgress -= repeatDelay / resolvedDuration;\n          }\n        } else if (repeatType === \"mirror\") {\n          frameGenerator = mirroredGenerator;\n        }\n      }\n      elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n    }\n    /**\n     * If we're in negative time, set state as the initial keyframe.\n     * This prevents delay: x, duration: 0 animations from finishing\n     * instantly.\n     */\n    const state = isInDelayPhase ? {\n      done: false,\n      value: keyframes[0]\n    } : frameGenerator.next(elapsed);\n    if (mixKeyframes) {\n      state.value = mixKeyframes(state.value);\n    }\n    let {\n      done\n    } = state;\n    if (!isInDelayPhase && calculatedDuration !== null) {\n      done = this.playbackSpeed >= 0 ? this.currentTime >= totalDuration : this.currentTime <= 0;\n    }\n    const isAnimationFinished = this.holdTime === null && (this.state === \"finished\" || this.state === \"running\" && done);\n    // TODO: The exception for inertia could be cleaner here\n    if (isAnimationFinished && type !== inertia) {\n      state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n    }\n    if (onUpdate) {\n      onUpdate(state.value);\n    }\n    if (isAnimationFinished) {\n      this.finish();\n    }\n    return state;\n  }\n  /**\n   * Allows the returned animation to be awaited or promise-chained. Currently\n   * resolves when the animation finishes at all but in a future update could/should\n   * reject if its cancels.\n   */\n  then(resolve, reject) {\n    return this.finished.then(resolve, reject);\n  }\n  get duration() {\n    return millisecondsToSeconds(this.calculatedDuration);\n  }\n  get time() {\n    return millisecondsToSeconds(this.currentTime);\n  }\n  set time(newTime) {\n    var _this$driver;\n    newTime = secondsToMilliseconds(newTime);\n    this.currentTime = newTime;\n    if (this.startTime === null || this.holdTime !== null || this.playbackSpeed === 0) {\n      this.holdTime = newTime;\n    } else if (this.driver) {\n      this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n    }\n    (_this$driver = this.driver) === null || _this$driver === void 0 || _this$driver.start(false);\n  }\n  get speed() {\n    return this.playbackSpeed;\n  }\n  set speed(newSpeed) {\n    this.updateTime(time.now());\n    const hasChanged = this.playbackSpeed !== newSpeed;\n    this.playbackSpeed = newSpeed;\n    if (hasChanged) {\n      this.time = millisecondsToSeconds(this.currentTime);\n    }\n  }\n  play() {\n    var _this$options$onPlay, _this$options2;\n    if (this.isStopped) return;\n    const {\n      driver = frameloopDriver,\n      startTime\n    } = this.options;\n    if (!this.driver) {\n      this.driver = driver(timestamp => this.tick(timestamp));\n    }\n    (_this$options$onPlay = (_this$options2 = this.options).onPlay) === null || _this$options$onPlay === void 0 || _this$options$onPlay.call(_this$options2);\n    const now = this.driver.now();\n    if (this.state === \"finished\") {\n      this.updateFinished();\n      this.startTime = now;\n    } else if (this.holdTime !== null) {\n      this.startTime = now - this.holdTime;\n    } else if (!this.startTime) {\n      this.startTime = startTime !== null && startTime !== void 0 ? startTime : now;\n    }\n    if (this.state === \"finished\" && this.speed < 0) {\n      this.startTime += this.calculatedDuration;\n    }\n    this.holdTime = null;\n    /**\n     * Set playState to running only after we've used it in\n     * the previous logic.\n     */\n    this.state = \"running\";\n    this.driver.start();\n  }\n  pause() {\n    this.state = \"paused\";\n    this.updateTime(time.now());\n    this.holdTime = this.currentTime;\n  }\n  complete() {\n    if (this.state !== \"running\") {\n      this.play();\n    }\n    this.state = \"finished\";\n    this.holdTime = null;\n  }\n  finish() {\n    var _this$options$onCompl, _this$options3;\n    this.notifyFinished();\n    this.teardown();\n    this.state = \"finished\";\n    (_this$options$onCompl = (_this$options3 = this.options).onComplete) === null || _this$options$onCompl === void 0 || _this$options$onCompl.call(_this$options3);\n  }\n  cancel() {\n    var _this$options$onCance, _this$options4;\n    this.holdTime = null;\n    this.startTime = 0;\n    this.tick(0);\n    this.teardown();\n    (_this$options$onCance = (_this$options4 = this.options).onCancel) === null || _this$options$onCance === void 0 || _this$options$onCance.call(_this$options4);\n  }\n  teardown() {\n    this.state = \"idle\";\n    this.stopDriver();\n    this.startTime = this.holdTime = null;\n    activeAnimations.mainThread--;\n  }\n  stopDriver() {\n    if (!this.driver) return;\n    this.driver.stop();\n    this.driver = undefined;\n  }\n  sample(sampleTime) {\n    this.startTime = 0;\n    return this.tick(sampleTime, true);\n  }\n  attachTimeline(timeline) {\n    var _this$driver2;\n    if (this.options.allowFlatten) {\n      this.options.type = \"keyframes\";\n      this.options.ease = \"linear\";\n      this.initAnimation();\n    }\n    (_this$driver2 = this.driver) === null || _this$driver2 === void 0 || _this$driver2.stop();\n    return timeline.observe(this);\n  }\n}\n// Legacy function support\nfunction animateValue(options) {\n  return new JSAnimation(options);\n}\nexport { JSAnimation, animateValue };", "map": {"version": 3, "names": ["invariant", "pipe", "clamp", "millisecondsToSeconds", "secondsToMilliseconds", "time", "activeAnimations", "mix", "frameloopDriver", "inertia", "keyframes", "calcGeneratorDuration", "getFinalKeyframe", "replaceTransitionType", "WithPromise", "percentToProgress", "percent", "JSAnimation", "constructor", "options", "state", "startTime", "isStopped", "currentTime", "holdTime", "playbackSpeed", "stop", "_this$options$onStop", "_this$options", "motionValue", "updatedAt", "now", "tick", "teardown", "onStop", "call", "mainThread", "initAnimation", "play", "autoplay", "pause", "type", "repeat", "repeatDelay", "repeatType", "velocity", "keyframes$1", "generatorFactory", "process", "env", "NODE_ENV", "length", "concat", "mixKeyframes", "generator", "_objectSpread", "mirroredGenerator", "reverse", "calculatedDuration", "resolvedDuration", "totalDuration", "updateTime", "timestamp", "animationTime", "Math", "round", "sample", "arguments", "undefined", "next", "delay", "onUpdate", "finalKeyframe", "speed", "min", "timeWithoutDelay", "isInDelayPhase", "max", "elapsed", "frameGenerator", "progress", "currentIteration", "floor", "iterationProgress", "isOddIteration", "Boolean", "done", "value", "isAnimationFinished", "finish", "then", "resolve", "reject", "finished", "duration", "newTime", "_this$driver", "driver", "start", "newSpeed", "has<PERSON><PERSON>ed", "_this$options$onPlay", "_this$options2", "onPlay", "updateFinished", "complete", "_this$options$onCompl", "_this$options3", "notifyFinished", "onComplete", "cancel", "_this$options$onCance", "_this$options4", "onCancel", "stopDriver", "sampleTime", "attachTimeline", "timeline", "_this$driver2", "allowFlatten", "ease", "observe", "animateValue"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs"], "sourcesContent": ["import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\n\nconst percentToProgress = (percent) => percent / 100;\nclass JSAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.state = \"idle\";\n        this.startTime = null;\n        this.isStopped = false;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            const { motionValue } = this.options;\n            if (motionValue && motionValue.updatedAt !== time.now()) {\n                this.tick(time.now());\n            }\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            this.options.onStop?.();\n        };\n        activeAnimations.mainThread++;\n        this.options = options;\n        this.initAnimation();\n        this.play();\n        if (options.autoplay === false)\n            this.pause();\n    }\n    initAnimation() {\n        const { options } = this;\n        replaceTransitionType(options);\n        const { type = keyframes, repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = options;\n        let { keyframes: keyframes$1 } = options;\n        const generatorFactory = type || keyframes;\n        if (process.env.NODE_ENV !== \"production\" &&\n            generatorFactory !== keyframes) {\n            invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n        }\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            this.mirroredGenerator = generatorFactory({\n                ...options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        this.calculatedDuration = calculatedDuration;\n        this.resolvedDuration = calculatedDuration + repeatDelay;\n        this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n        this.generator = generator;\n    }\n    updateTime(timestamp) {\n        const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n        // Update currentTime\n        if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime = animationTime;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { generator, totalDuration, mixKeyframes, mirroredGenerator, resolvedDuration, calculatedDuration, } = this;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay = 0, keyframes, repeat, repeatType, repeatDelay, type, onUpdate, finalKeyframe, } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else {\n            this.updateTime(timestamp);\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.playbackSpeed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mixKeyframes) {\n            state.value = mixKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.playbackSpeed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        // TODO: The exception for inertia could be cleaner here\n        if (isAnimationFinished && type !== inertia) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    /**\n     * Allows the returned animation to be awaited or promise-chained. Currently\n     * resolves when the animation finishes at all but in a future update could/should\n     * reject if its cancels.\n     */\n    then(resolve, reject) {\n        return this.finished.then(resolve, reject);\n    }\n    get duration() {\n        return millisecondsToSeconds(this.calculatedDuration);\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.startTime === null ||\n            this.holdTime !== null ||\n            this.playbackSpeed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n        }\n        this.driver?.start(false);\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        this.updateTime(time.now());\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, startTime } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        this.options.onPlay?.();\n        const now = this.driver.now();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n            this.startTime = now;\n        }\n        else if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = startTime ?? now;\n        }\n        if (this.state === \"finished\" && this.speed < 0) {\n            this.startTime += this.calculatedDuration;\n        }\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        this.state = \"paused\";\n        this.updateTime(time.now());\n        this.holdTime = this.currentTime;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.notifyFinished();\n        this.teardown();\n        this.state = \"finished\";\n        this.options.onComplete?.();\n    }\n    cancel() {\n        this.holdTime = null;\n        this.startTime = 0;\n        this.tick(0);\n        this.teardown();\n        this.options.onCancel?.();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.startTime = this.holdTime = null;\n        activeAnimations.mainThread--;\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(sampleTime) {\n        this.startTime = 0;\n        return this.tick(sampleTime, true);\n    }\n    attachTimeline(timeline) {\n        if (this.options.allowFlatten) {\n            this.options.type = \"keyframes\";\n            this.options.ease = \"linear\";\n            this.initAnimation();\n        }\n        this.driver?.stop();\n        return timeline.observe(this);\n    }\n}\n// Legacy function support\nfunction animateValue(options) {\n    return new JSAnimation(options);\n}\n\nexport { JSAnimation, animateValue };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,qBAAqB,QAAQ,cAAc;AACnG,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,GAAG,QAAQ,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,WAAW,QAAQ,yBAAyB;AAErD,MAAMC,iBAAiB,GAAIC,OAAO,IAAKA,OAAO,GAAG,GAAG;AACpD,MAAMC,WAAW,SAASH,WAAW,CAAC;EAClCI,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MAAA,IAAAC,oBAAA,EAAAC,aAAA;MACd,MAAM;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACV,OAAO;MACpC,IAAIU,WAAW,IAAIA,WAAW,CAACC,SAAS,KAAKzB,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE;QACrD,IAAI,CAACC,IAAI,CAAC3B,IAAI,CAAC0B,GAAG,CAAC,CAAC,CAAC;MACzB;MACA,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAACF,KAAK,KAAK,MAAM,EACrB;MACJ,IAAI,CAACa,QAAQ,CAAC,CAAC;MACf,CAAAN,oBAAA,IAAAC,aAAA,OAAI,CAACT,OAAO,EAACe,MAAM,cAAAP,oBAAA,eAAnBA,oBAAA,CAAAQ,IAAA,CAAAP,aAAsB,CAAC;IAC3B,CAAC;IACDtB,gBAAgB,CAAC8B,UAAU,EAAE;IAC7B,IAAI,CAACjB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAInB,OAAO,CAACoB,QAAQ,KAAK,KAAK,EAC1B,IAAI,CAACC,KAAK,CAAC,CAAC;EACpB;EACAH,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAElB;IAAQ,CAAC,GAAG,IAAI;IACxBN,qBAAqB,CAACM,OAAO,CAAC;IAC9B,MAAM;MAAEsB,IAAI,GAAG/B,SAAS;MAAEgC,MAAM,GAAG,CAAC;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAG1B,OAAO;IAC5F,IAAI;MAAET,SAAS,EAAEoC;IAAY,CAAC,GAAG3B,OAAO;IACxC,MAAM4B,gBAAgB,GAAGN,IAAI,IAAI/B,SAAS;IAC1C,IAAIsC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCH,gBAAgB,KAAKrC,SAAS,EAAE;MAChCV,SAAS,CAAC8C,WAAW,CAACK,MAAM,IAAI,CAAC,kGAAAC,MAAA,CAAkGN,WAAW,CAAE,CAAC;IACrJ;IACA,IAAIC,gBAAgB,KAAKrC,SAAS,IAC9B,OAAOoC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAI,CAACO,YAAY,GAAGpD,IAAI,CAACc,iBAAiB,EAAER,GAAG,CAACuC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFA,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1B;IACA,MAAMQ,SAAS,GAAGP,gBAAgB,CAAAQ,aAAA,CAAAA,aAAA,KAAMpC,OAAO;MAAET,SAAS,EAAEoC;IAAW,EAAE,CAAC;IAC1E;AACR;AACA;AACA;IACQ,IAAIF,UAAU,KAAK,QAAQ,EAAE;MACzB,IAAI,CAACY,iBAAiB,GAAGT,gBAAgB,CAAAQ,aAAA,CAAAA,aAAA,KAClCpC,OAAO;QACVT,SAAS,EAAE,CAAC,GAAGoC,WAAW,CAAC,CAACW,OAAO,CAAC,CAAC;QACrCZ,QAAQ,EAAE,CAACA;MAAQ,EACtB,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIS,SAAS,CAACI,kBAAkB,KAAK,IAAI,EAAE;MACvCJ,SAAS,CAACI,kBAAkB,GAAG/C,qBAAqB,CAAC2C,SAAS,CAAC;IACnE;IACA,MAAM;MAAEI;IAAmB,CAAC,GAAGJ,SAAS;IACxC,IAAI,CAACI,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,gBAAgB,GAAGD,kBAAkB,GAAGf,WAAW;IACxD,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACD,gBAAgB,IAAIjB,MAAM,GAAG,CAAC,CAAC,GAAGC,WAAW;IACvE,IAAI,CAACW,SAAS,GAAGA,SAAS;EAC9B;EACAO,UAAUA,CAACC,SAAS,EAAE;IAClB,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,GAAG,IAAI,CAACzC,SAAS,CAAC,GAAG,IAAI,CAACI,aAAa;IACjF;IACA,IAAI,IAAI,CAACD,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACD,WAAW,GAAG,IAAI,CAACC,QAAQ;IACpC,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACD,WAAW,GAAGwC,aAAa;IACpC;EACJ;EACA/B,IAAIA,CAAC8B,SAAS,EAAkB;IAAA,IAAhBI,MAAM,GAAAC,SAAA,CAAAhB,MAAA,QAAAgB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAC1B,MAAM;MAAEb,SAAS;MAAEM,aAAa;MAAEP,YAAY;MAAEG,iBAAiB;MAAEG,gBAAgB;MAAED;IAAoB,CAAC,GAAG,IAAI;IACjH,IAAI,IAAI,CAACrC,SAAS,KAAK,IAAI,EACvB,OAAOiC,SAAS,CAACe,IAAI,CAAC,CAAC,CAAC;IAC5B,MAAM;MAAEC,KAAK,GAAG,CAAC;MAAE5D,SAAS;MAAEgC,MAAM;MAAEE,UAAU;MAAED,WAAW;MAAEF,IAAI;MAAE8B,QAAQ;MAAEC;IAAe,CAAC,GAAG,IAAI,CAACrD,OAAO;IAC9G;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACsD,KAAK,GAAG,CAAC,EAAE;MAChB,IAAI,CAACpD,SAAS,GAAG2C,IAAI,CAACU,GAAG,CAAC,IAAI,CAACrD,SAAS,EAAEyC,SAAS,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACW,KAAK,GAAG,CAAC,EAAE;MACrB,IAAI,CAACpD,SAAS,GAAG2C,IAAI,CAACU,GAAG,CAACZ,SAAS,GAAGF,aAAa,GAAG,IAAI,CAACa,KAAK,EAAE,IAAI,CAACpD,SAAS,CAAC;IACrF;IACA,IAAI6C,MAAM,EAAE;MACR,IAAI,CAAC3C,WAAW,GAAGuC,SAAS;IAChC,CAAC,MACI;MACD,IAAI,CAACD,UAAU,CAACC,SAAS,CAAC;IAC9B;IACA;IACA,MAAMa,gBAAgB,GAAG,IAAI,CAACpD,WAAW,GAAG+C,KAAK,IAAI,IAAI,CAAC7C,aAAa,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtF,MAAMmD,cAAc,GAAG,IAAI,CAACnD,aAAa,IAAI,CAAC,GACxCkD,gBAAgB,GAAG,CAAC,GACpBA,gBAAgB,GAAGf,aAAa;IACtC,IAAI,CAACrC,WAAW,GAAGyC,IAAI,CAACa,GAAG,CAACF,gBAAgB,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAACvD,KAAK,KAAK,UAAU,IAAI,IAAI,CAACI,QAAQ,KAAK,IAAI,EAAE;MACrD,IAAI,CAACD,WAAW,GAAGqC,aAAa;IACpC;IACA,IAAIkB,OAAO,GAAG,IAAI,CAACvD,WAAW;IAC9B,IAAIwD,cAAc,GAAGzB,SAAS;IAC9B,IAAIZ,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;MACY,MAAMsC,QAAQ,GAAGhB,IAAI,CAACU,GAAG,CAAC,IAAI,CAACnD,WAAW,EAAEqC,aAAa,CAAC,GAAGD,gBAAgB;MAC7E;AACZ;AACA;AACA;MACY,IAAIsB,gBAAgB,GAAGjB,IAAI,CAACkB,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC;AACZ;AACA;AACA;MACY,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACAA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7CA,gBAAgB,GAAGjB,IAAI,CAACU,GAAG,CAACO,gBAAgB,EAAEvC,MAAM,GAAG,CAAC,CAAC;MACzD;AACZ;AACA;MACY,MAAM0C,cAAc,GAAGC,OAAO,CAACJ,gBAAgB,GAAG,CAAC,CAAC;MACpD,IAAIG,cAAc,EAAE;QAChB,IAAIxC,UAAU,KAAK,SAAS,EAAE;UAC1BuC,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;UACzC,IAAIxC,WAAW,EAAE;YACbwC,iBAAiB,IAAIxC,WAAW,GAAGgB,gBAAgB;UACvD;QACJ,CAAC,MACI,IAAIf,UAAU,KAAK,QAAQ,EAAE;UAC9BmC,cAAc,GAAGvB,iBAAiB;QACtC;MACJ;MACAsB,OAAO,GAAG5E,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEiF,iBAAiB,CAAC,GAAGxB,gBAAgB;IAC/D;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMvC,KAAK,GAAGwD,cAAc,GACtB;MAAEU,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE7E,SAAS,CAAC,CAAC;IAAE,CAAC,GACpCqE,cAAc,CAACV,IAAI,CAACS,OAAO,CAAC;IAClC,IAAIzB,YAAY,EAAE;MACdjC,KAAK,CAACmE,KAAK,GAAGlC,YAAY,CAACjC,KAAK,CAACmE,KAAK,CAAC;IAC3C;IACA,IAAI;MAAED;IAAK,CAAC,GAAGlE,KAAK;IACpB,IAAI,CAACwD,cAAc,IAAIlB,kBAAkB,KAAK,IAAI,EAAE;MAChD4B,IAAI,GACA,IAAI,CAAC7D,aAAa,IAAI,CAAC,GACjB,IAAI,CAACF,WAAW,IAAIqC,aAAa,GACjC,IAAI,CAACrC,WAAW,IAAI,CAAC;IACnC;IACA,MAAMiE,mBAAmB,GAAG,IAAI,CAAChE,QAAQ,KAAK,IAAI,KAC7C,IAAI,CAACJ,KAAK,KAAK,UAAU,IAAK,IAAI,CAACA,KAAK,KAAK,SAAS,IAAIkE,IAAK,CAAC;IACrE;IACA,IAAIE,mBAAmB,IAAI/C,IAAI,KAAKhC,OAAO,EAAE;MACzCW,KAAK,CAACmE,KAAK,GAAG3E,gBAAgB,CAACF,SAAS,EAAE,IAAI,CAACS,OAAO,EAAEqD,aAAa,EAAE,IAAI,CAACC,KAAK,CAAC;IACtF;IACA,IAAIF,QAAQ,EAAE;MACVA,QAAQ,CAACnD,KAAK,CAACmE,KAAK,CAAC;IACzB;IACA,IAAIC,mBAAmB,EAAE;MACrB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,OAAOrE,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIsE,IAAIA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAClB,OAAO,IAAI,CAACC,QAAQ,CAACH,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EAC9C;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO3F,qBAAqB,CAAC,IAAI,CAACuD,kBAAkB,CAAC;EACzD;EACA,IAAIrD,IAAIA,CAAA,EAAG;IACP,OAAOF,qBAAqB,CAAC,IAAI,CAACoB,WAAW,CAAC;EAClD;EACA,IAAIlB,IAAIA,CAAC0F,OAAO,EAAE;IAAA,IAAAC,YAAA;IACdD,OAAO,GAAG3F,qBAAqB,CAAC2F,OAAO,CAAC;IACxC,IAAI,CAACxE,WAAW,GAAGwE,OAAO;IAC1B,IAAI,IAAI,CAAC1E,SAAS,KAAK,IAAI,IACvB,IAAI,CAACG,QAAQ,KAAK,IAAI,IACtB,IAAI,CAACC,aAAa,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACD,QAAQ,GAAGuE,OAAO;IAC3B,CAAC,MACI,IAAI,IAAI,CAACE,MAAM,EAAE;MAClB,IAAI,CAAC5E,SAAS,GAAG,IAAI,CAAC4E,MAAM,CAAClE,GAAG,CAAC,CAAC,GAAGgE,OAAO,GAAG,IAAI,CAACtE,aAAa;IACrE;IACA,CAAAuE,YAAA,OAAI,CAACC,MAAM,cAAAD,YAAA,eAAXA,YAAA,CAAaE,KAAK,CAAC,KAAK,CAAC;EAC7B;EACA,IAAIzB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChD,aAAa;EAC7B;EACA,IAAIgD,KAAKA,CAAC0B,QAAQ,EAAE;IAChB,IAAI,CAACtC,UAAU,CAACxD,IAAI,CAAC0B,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAMqE,UAAU,GAAG,IAAI,CAAC3E,aAAa,KAAK0E,QAAQ;IAClD,IAAI,CAAC1E,aAAa,GAAG0E,QAAQ;IAC7B,IAAIC,UAAU,EAAE;MACZ,IAAI,CAAC/F,IAAI,GAAGF,qBAAqB,CAAC,IAAI,CAACoB,WAAW,CAAC;IACvD;EACJ;EACAe,IAAIA,CAAA,EAAG;IAAA,IAAA+D,oBAAA,EAAAC,cAAA;IACH,IAAI,IAAI,CAAChF,SAAS,EACd;IACJ,MAAM;MAAE2E,MAAM,GAAGzF,eAAe;MAAEa;IAAU,CAAC,GAAG,IAAI,CAACF,OAAO;IAC5D,IAAI,CAAC,IAAI,CAAC8E,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAEnC,SAAS,IAAK,IAAI,CAAC9B,IAAI,CAAC8B,SAAS,CAAC,CAAC;IAC7D;IACA,CAAAuC,oBAAA,IAAAC,cAAA,OAAI,CAACnF,OAAO,EAACoF,MAAM,cAAAF,oBAAA,eAAnBA,oBAAA,CAAAlE,IAAA,CAAAmE,cAAsB,CAAC;IACvB,MAAMvE,GAAG,GAAG,IAAI,CAACkE,MAAM,CAAClE,GAAG,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACX,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACoF,cAAc,CAAC,CAAC;MACrB,IAAI,CAACnF,SAAS,GAAGU,GAAG;IACxB,CAAC,MACI,IAAI,IAAI,CAACP,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACH,SAAS,GAAGU,GAAG,GAAG,IAAI,CAACP,QAAQ;IACxC,CAAC,MACI,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAIU,GAAG;IACrC;IACA,IAAI,IAAI,CAACX,KAAK,KAAK,UAAU,IAAI,IAAI,CAACqD,KAAK,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACpD,SAAS,IAAI,IAAI,CAACqC,kBAAkB;IAC7C;IACA,IAAI,CAAClC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACJ,KAAK,GAAG,SAAS;IACtB,IAAI,CAAC6E,MAAM,CAACC,KAAK,CAAC,CAAC;EACvB;EACA1D,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpB,KAAK,GAAG,QAAQ;IACrB,IAAI,CAACyC,UAAU,CAACxD,IAAI,CAAC0B,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACD,WAAW;EACpC;EACAkF,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACrF,KAAK,KAAK,SAAS,EAAE;MAC1B,IAAI,CAACkB,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAAClB,KAAK,GAAG,UAAU;IACvB,IAAI,CAACI,QAAQ,GAAG,IAAI;EACxB;EACAiE,MAAMA,CAAA,EAAG;IAAA,IAAAiB,qBAAA,EAAAC,cAAA;IACL,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC3E,QAAQ,CAAC,CAAC;IACf,IAAI,CAACb,KAAK,GAAG,UAAU;IACvB,CAAAsF,qBAAA,IAAAC,cAAA,OAAI,CAACxF,OAAO,EAAC0F,UAAU,cAAAH,qBAAA,eAAvBA,qBAAA,CAAAvE,IAAA,CAAAwE,cAA0B,CAAC;EAC/B;EACAG,MAAMA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,cAAA;IACL,IAAI,CAACxF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACH,SAAS,GAAG,CAAC;IAClB,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC;IACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,CAAA8E,qBAAA,IAAAC,cAAA,OAAI,CAAC7F,OAAO,EAAC8F,QAAQ,cAAAF,qBAAA,eAArBA,qBAAA,CAAA5E,IAAA,CAAA6E,cAAwB,CAAC;EAC7B;EACA/E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,KAAK,GAAG,MAAM;IACnB,IAAI,CAAC8F,UAAU,CAAC,CAAC;IACjB,IAAI,CAAC7F,SAAS,GAAG,IAAI,CAACG,QAAQ,GAAG,IAAI;IACrClB,gBAAgB,CAAC8B,UAAU,EAAE;EACjC;EACA8E,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACjB,MAAM,EACZ;IACJ,IAAI,CAACA,MAAM,CAACvE,IAAI,CAAC,CAAC;IAClB,IAAI,CAACuE,MAAM,GAAG7B,SAAS;EAC3B;EACAF,MAAMA,CAACiD,UAAU,EAAE;IACf,IAAI,CAAC9F,SAAS,GAAG,CAAC;IAClB,OAAO,IAAI,CAACW,IAAI,CAACmF,UAAU,EAAE,IAAI,CAAC;EACtC;EACAC,cAAcA,CAACC,QAAQ,EAAE;IAAA,IAAAC,aAAA;IACrB,IAAI,IAAI,CAACnG,OAAO,CAACoG,YAAY,EAAE;MAC3B,IAAI,CAACpG,OAAO,CAACsB,IAAI,GAAG,WAAW;MAC/B,IAAI,CAACtB,OAAO,CAACqG,IAAI,GAAG,QAAQ;MAC5B,IAAI,CAACnF,aAAa,CAAC,CAAC;IACxB;IACA,CAAAiF,aAAA,OAAI,CAACrB,MAAM,cAAAqB,aAAA,eAAXA,aAAA,CAAa5F,IAAI,CAAC,CAAC;IACnB,OAAO2F,QAAQ,CAACI,OAAO,CAAC,IAAI,CAAC;EACjC;AACJ;AACA;AACA,SAASC,YAAYA,CAACvG,OAAO,EAAE;EAC3B,OAAO,IAAIF,WAAW,CAACE,OAAO,CAAC;AACnC;AAEA,SAASF,WAAW,EAAEyG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}