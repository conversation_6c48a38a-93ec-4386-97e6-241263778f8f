{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transition\", \"transitionEnd\"];\nimport { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { getVariantContext } from './get-variant-context.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n  return animations => Promise.all(animations.map(_ref => {\n    let {\n      animation,\n      options\n    } = _ref;\n    return animateVisualElement(visualElement, animation, options);\n  }));\n}\nfunction createAnimationState(visualElement) {\n  let animate = animateList(visualElement);\n  let state = createState();\n  let isInitialRender = true;\n  /**\n   * This function will be used to reduce the animation definitions for\n   * each active animation type into an object of resolved values for it.\n   */\n  const buildResolvedTypeValues = type => (acc, definition) => {\n    var _visualElement$presen;\n    const resolved = resolveVariant(visualElement, definition, type === \"exit\" ? (_visualElement$presen = visualElement.presenceContext) === null || _visualElement$presen === void 0 ? void 0 : _visualElement$presen.custom : undefined);\n    if (resolved) {\n      const {\n          transition,\n          transitionEnd\n        } = resolved,\n        target = _objectWithoutProperties(resolved, _excluded);\n      acc = _objectSpread(_objectSpread(_objectSpread({}, acc), target), transitionEnd);\n    }\n    return acc;\n  };\n  /**\n   * This just allows us to inject mocked animation functions\n   * @internal\n   */\n  function setAnimateFunction(makeAnimator) {\n    animate = makeAnimator(visualElement);\n  }\n  /**\n   * When we receive new props, we need to:\n   * 1. Create a list of protected keys for each type. This is a directory of\n   *    value keys that are currently being \"handled\" by types of a higher priority\n   *    so that whenever an animation is played of a given type, these values are\n   *    protected from being animated.\n   * 2. Determine if an animation type needs animating.\n   * 3. Determine if any values have been removed from a type and figure out\n   *    what to animate those to.\n   */\n  function animateChanges(changedActiveType) {\n    const {\n      props\n    } = visualElement;\n    const context = getVariantContext(visualElement.parent) || {};\n    /**\n     * A list of animations that we'll build into as we iterate through the animation\n     * types. This will get executed at the end of the function.\n     */\n    const animations = [];\n    /**\n     * Keep track of which values have been removed. Then, as we hit lower priority\n     * animation types, we can check if they contain removed values and animate to that.\n     */\n    const removedKeys = new Set();\n    /**\n     * A dictionary of all encountered keys. This is an object to let us build into and\n     * copy it without iteration. Each time we hit an animation type we set its protected\n     * keys - the keys its not allowed to animate - to the latest version of this object.\n     */\n    let encounteredKeys = {};\n    /**\n     * If a variant has been removed at a given index, and this component is controlling\n     * variant animations, we want to ensure lower-priority variants are forced to animate.\n     */\n    let removedVariantIndex = Infinity;\n    /**\n     * Iterate through all animation types in reverse priority order. For each, we want to\n     * detect which values it's handling and whether or not they've changed (and therefore\n     * need to be animated). If any values have been removed, we want to detect those in\n     * lower priority props and flag for animation.\n     */\n    for (let i = 0; i < numAnimationTypes; i++) {\n      const type = reversePriorityOrder[i];\n      const typeState = state[type];\n      const prop = props[type] !== undefined ? props[type] : context[type];\n      const propIsVariant = isVariantLabel(prop);\n      /**\n       * If this type has *just* changed isActive status, set activeDelta\n       * to that status. Otherwise set to null.\n       */\n      const activeDelta = type === changedActiveType ? typeState.isActive : null;\n      if (activeDelta === false) removedVariantIndex = i;\n      /**\n       * If this prop is an inherited variant, rather than been set directly on the\n       * component itself, we want to make sure we allow the parent to trigger animations.\n       *\n       * TODO: Can probably change this to a !isControllingVariants check\n       */\n      let isInherited = prop === context[type] && prop !== props[type] && propIsVariant;\n      /**\n       *\n       */\n      if (isInherited && isInitialRender && visualElement.manuallyAnimateOnMount) {\n        isInherited = false;\n      }\n      /**\n       * Set all encountered keys so far as the protected keys for this type. This will\n       * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n       */\n      typeState.protectedKeys = _objectSpread({}, encounteredKeys);\n      // Check if we can skip analysing this prop early\n      if (\n      // If it isn't active and hasn't *just* been set as inactive\n      !typeState.isActive && activeDelta === null ||\n      // If we didn't and don't have any defined prop for this animation type\n      !prop && !typeState.prevProp ||\n      // Or if the prop doesn't define an animation\n      isAnimationControls(prop) || typeof prop === \"boolean\") {\n        continue;\n      }\n      /**\n       * As we go look through the values defined on this type, if we detect\n       * a changed value or a value that was removed in a higher priority, we set\n       * this to true and add this prop to the animation list.\n       */\n      const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n      let shouldAnimateType = variantDidChange ||\n      // If we're making this variant active, we want to always make it active\n      type === changedActiveType && typeState.isActive && !isInherited && propIsVariant ||\n      // If we removed a higher-priority variant (i is in reverse order)\n      i > removedVariantIndex && propIsVariant;\n      let handledRemovedValues = false;\n      /**\n       * As animations can be set as variant lists, variants or target objects, we\n       * coerce everything to an array if it isn't one already\n       */\n      const definitionList = Array.isArray(prop) ? prop : [prop];\n      /**\n       * Build an object of all the resolved values. We'll use this in the subsequent\n       * animateChanges calls to determine whether a value has changed.\n       */\n      let resolvedValues = definitionList.reduce(buildResolvedTypeValues(type), {});\n      if (activeDelta === false) resolvedValues = {};\n      /**\n       * Now we need to loop through all the keys in the prev prop and this prop,\n       * and decide:\n       * 1. If the value has changed, and needs animating\n       * 2. If it has been removed, and needs adding to the removedKeys set\n       * 3. If it has been removed in a higher priority type and needs animating\n       * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n       *    needs adding to the type's protectedKeys list.\n       */\n      const {\n        prevResolvedValues = {}\n      } = typeState;\n      const allKeys = _objectSpread(_objectSpread({}, prevResolvedValues), resolvedValues);\n      const markToAnimate = key => {\n        shouldAnimateType = true;\n        if (removedKeys.has(key)) {\n          handledRemovedValues = true;\n          removedKeys.delete(key);\n        }\n        typeState.needsAnimating[key] = true;\n        const motionValue = visualElement.getValue(key);\n        if (motionValue) motionValue.liveStyle = false;\n      };\n      for (const key in allKeys) {\n        const next = resolvedValues[key];\n        const prev = prevResolvedValues[key];\n        // If we've already handled this we can just skip ahead\n        if (encounteredKeys.hasOwnProperty(key)) continue;\n        /**\n         * If the value has changed, we probably want to animate it.\n         */\n        let valueHasChanged = false;\n        if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n          valueHasChanged = !shallowCompare(next, prev);\n        } else {\n          valueHasChanged = next !== prev;\n        }\n        if (valueHasChanged) {\n          if (next !== undefined && next !== null) {\n            // If next is defined and doesn't equal prev, it needs animating\n            markToAnimate(key);\n          } else {\n            // If it's undefined, it's been removed.\n            removedKeys.add(key);\n          }\n        } else if (next !== undefined && removedKeys.has(key)) {\n          /**\n           * If next hasn't changed and it isn't undefined, we want to check if it's\n           * been removed by a higher priority\n           */\n          markToAnimate(key);\n        } else {\n          /**\n           * If it hasn't changed, we add it to the list of protected values\n           * to ensure it doesn't get animated.\n           */\n          typeState.protectedKeys[key] = true;\n        }\n      }\n      /**\n       * Update the typeState so next time animateChanges is called we can compare the\n       * latest prop and resolvedValues to these.\n       */\n      typeState.prevProp = prop;\n      typeState.prevResolvedValues = resolvedValues;\n      /**\n       *\n       */\n      if (typeState.isActive) {\n        encounteredKeys = _objectSpread(_objectSpread({}, encounteredKeys), resolvedValues);\n      }\n      if (isInitialRender && visualElement.blockInitialAnimation) {\n        shouldAnimateType = false;\n      }\n      /**\n       * If this is an inherited prop we want to skip this animation\n       * unless the inherited variants haven't changed on this render.\n       */\n      const willAnimateViaParent = isInherited && variantDidChange;\n      const needsAnimating = !willAnimateViaParent || handledRemovedValues;\n      if (shouldAnimateType && needsAnimating) {\n        animations.push(...definitionList.map(animation => ({\n          animation: animation,\n          options: {\n            type\n          }\n        })));\n      }\n    }\n    /**\n     * If there are some removed value that haven't been dealt with,\n     * we need to create a new animation that falls back either to the value\n     * defined in the style prop, or the last read value.\n     */\n    if (removedKeys.size) {\n      const fallbackAnimation = {};\n      /**\n       * If the initial prop contains a transition we can use that, otherwise\n       * allow the animation function to use the visual element's default.\n       */\n      if (typeof props.initial !== \"boolean\") {\n        const initialTransition = resolveVariant(visualElement, Array.isArray(props.initial) ? props.initial[0] : props.initial);\n        if (initialTransition && initialTransition.transition) {\n          fallbackAnimation.transition = initialTransition.transition;\n        }\n      }\n      removedKeys.forEach(key => {\n        const fallbackTarget = visualElement.getBaseTarget(key);\n        const motionValue = visualElement.getValue(key);\n        if (motionValue) motionValue.liveStyle = true;\n        // @ts-expect-error - @mattgperry to figure if we should do something here\n        fallbackAnimation[key] = fallbackTarget !== null && fallbackTarget !== void 0 ? fallbackTarget : null;\n      });\n      animations.push({\n        animation: fallbackAnimation\n      });\n    }\n    let shouldAnimate = Boolean(animations.length);\n    if (isInitialRender && (props.initial === false || props.initial === props.animate) && !visualElement.manuallyAnimateOnMount) {\n      shouldAnimate = false;\n    }\n    isInitialRender = false;\n    return shouldAnimate ? animate(animations) : Promise.resolve();\n  }\n  /**\n   * Change whether a certain animation type is active.\n   */\n  function setActive(type, isActive) {\n    var _visualElement$varian;\n    // If the active state hasn't changed, we can safely do nothing here\n    if (state[type].isActive === isActive) return Promise.resolve();\n    // Propagate active change to children\n    (_visualElement$varian = visualElement.variantChildren) === null || _visualElement$varian === void 0 || _visualElement$varian.forEach(child => {\n      var _child$animationState;\n      return (_child$animationState = child.animationState) === null || _child$animationState === void 0 ? void 0 : _child$animationState.setActive(type, isActive);\n    });\n    state[type].isActive = isActive;\n    const animations = animateChanges(type);\n    for (const key in state) {\n      state[key].protectedKeys = {};\n    }\n    return animations;\n  }\n  return {\n    animateChanges,\n    setActive,\n    setAnimateFunction,\n    getState: () => state,\n    reset: () => {\n      state = createState();\n      isInitialRender = true;\n    }\n  };\n}\nfunction checkVariantsDidChange(prev, next) {\n  if (typeof next === \"string\") {\n    return next !== prev;\n  } else if (Array.isArray(next)) {\n    return !shallowCompare(next, prev);\n  }\n  return false;\n}\nfunction createTypeState() {\n  let isActive = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  return {\n    isActive,\n    protectedKeys: {},\n    needsAnimating: {},\n    prevResolvedValues: {}\n  };\n}\nfunction createState() {\n  return {\n    animate: createTypeState(true),\n    whileInView: createTypeState(),\n    whileHover: createTypeState(),\n    whileTap: createTypeState(),\n    whileDrag: createTypeState(),\n    whileFocus: createTypeState(),\n    exit: createTypeState()\n  };\n}\nexport { checkVariantsDidChange, createAnimationState };", "map": {"version": 3, "names": ["animateVisualElement", "isAnimationControls", "isKeyframesTarget", "shallowCompare", "getVariantContext", "isVariantLabel", "resolveV<PERSON>t", "variantPriorityOrder", "reversePriorityOrder", "reverse", "numAnimationTypes", "length", "animateList", "visualElement", "animations", "Promise", "all", "map", "_ref", "animation", "options", "createAnimationState", "animate", "state", "createState", "isInitialRender", "buildResolvedTypeValues", "type", "acc", "definition", "_visualElement$presen", "resolved", "presenceContext", "custom", "undefined", "transition", "transitionEnd", "target", "_objectWithoutProperties", "_excluded", "_objectSpread", "setAnimateFunction", "makeAnimator", "animateChanges", "changedActiveType", "props", "context", "parent", "<PERSON><PERSON><PERSON><PERSON>", "Set", "<PERSON><PERSON><PERSON><PERSON>", "removedVariantIndex", "Infinity", "i", "typeState", "prop", "propIsVariant", "activeDelta", "isActive", "isInherited", "manuallyAnimateOnMount", "protected<PERSON><PERSON>s", "prevProp", "variantDidChange", "checkVariantsDidChange", "shouldAnimateType", "handledRemovedValues", "definitionList", "Array", "isArray", "resolvedV<PERSON>ues", "reduce", "prevResolvedValues", "allKeys", "markToAnimate", "key", "has", "delete", "needsAnimating", "motionValue", "getValue", "liveStyle", "next", "prev", "hasOwnProperty", "valueHasChanged", "add", "blockInitialAnimation", "willAnimateViaParent", "push", "size", "fallbackAnimation", "initial", "initialTransition", "for<PERSON>ach", "fallback<PERSON><PERSON><PERSON>", "getBase<PERSON>arget", "shouldAnimate", "Boolean", "resolve", "setActive", "_visualElement$varian", "variant<PERSON><PERSON><PERSON>n", "child", "_child$animationState", "animationState", "getState", "reset", "createTypeState", "arguments", "whileInView", "whileHover", "whileTap", "whileDrag", "whileFocus", "exit"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs"], "sourcesContent": ["import { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { getVariantContext } from './get-variant-context.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\n\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => animateVisualElement(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    let state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (type) => (acc, definition) => {\n        const resolved = resolveVariant(visualElement, definition, type === \"exit\"\n            ? visualElement.presenceContext?.custom\n            : undefined);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(changedActiveType) {\n        const { props } = visualElement;\n        const context = getVariantContext(visualElement.parent) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined\n                ? props[type]\n                : context[type];\n            const propIsVariant = isVariantLabel(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] &&\n                prop !== props[type] &&\n                propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                isAnimationControls(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues(type), {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = false;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n                    valueHasChanged = !shallowCompare(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined && next !== null) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to skip this animation\n             * unless the inherited variants haven't changed on this render.\n             */\n            const willAnimateViaParent = isInherited && variantDidChange;\n            const needsAnimating = !willAnimateViaParent || handledRemovedValues;\n            if (shouldAnimateType && needsAnimating) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            /**\n             * If the initial prop contains a transition we can use that, otherwise\n             * allow the animation function to use the visual element's default.\n             */\n            if (typeof props.initial !== \"boolean\") {\n                const initialTransition = resolveVariant(visualElement, Array.isArray(props.initial)\n                    ? props.initial[0]\n                    : props.initial);\n                if (initialTransition && initialTransition.transition) {\n                    fallbackAnimation.transition = initialTransition.transition;\n                }\n            }\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = true;\n                // @ts-expect-error - @mattgperry to figure if we should do something here\n                fallbackAnimation[key] = fallbackTarget ?? null;\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive) {\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        visualElement.variantChildren?.forEach((child) => child.animationState?.setActive(type, isActive));\n        state[type].isActive = isActive;\n        const animations = animateChanges(type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n        reset: () => {\n            state = createState();\n            isInitialRender = true;\n        },\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !shallowCompare(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\nexport { checkVariantsDidChange, createAnimationState };\n"], "mappings": ";;;AAAA,SAASA,oBAAoB,QAAQ,+CAA+C;AACpF,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,oBAAoB,QAAQ,qBAAqB;AAE1D,MAAMC,oBAAoB,GAAG,CAAC,GAAGD,oBAAoB,CAAC,CAACE,OAAO,CAAC,CAAC;AAChE,MAAMC,iBAAiB,GAAGH,oBAAoB,CAACI,MAAM;AACrD,SAASC,WAAWA,CAACC,aAAa,EAAE;EAChC,OAAQC,UAAU,IAAKC,OAAO,CAACC,GAAG,CAACF,UAAU,CAACG,GAAG,CAACC,IAAA;IAAA,IAAC;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAAF,IAAA;IAAA,OAAKlB,oBAAoB,CAACa,aAAa,EAAEM,SAAS,EAAEC,OAAO,CAAC;EAAA,EAAC,CAAC;AAC3I;AACA,SAASC,oBAAoBA,CAACR,aAAa,EAAE;EACzC,IAAIS,OAAO,GAAGV,WAAW,CAACC,aAAa,CAAC;EACxC,IAAIU,KAAK,GAAGC,WAAW,CAAC,CAAC;EACzB,IAAIC,eAAe,GAAG,IAAI;EAC1B;AACJ;AACA;AACA;EACI,MAAMC,uBAAuB,GAAIC,IAAI,IAAK,CAACC,GAAG,EAAEC,UAAU,KAAK;IAAA,IAAAC,qBAAA;IAC3D,MAAMC,QAAQ,GAAGzB,cAAc,CAACO,aAAa,EAAEgB,UAAU,EAAEF,IAAI,KAAK,MAAM,IAAAG,qBAAA,GACpEjB,aAAa,CAACmB,eAAe,cAAAF,qBAAA,uBAA7BA,qBAAA,CAA+BG,MAAM,GACrCC,SAAS,CAAC;IAChB,IAAIH,QAAQ,EAAE;MACV,MAAM;UAAEI,UAAU;UAAEC;QAAyB,CAAC,GAAGL,QAAQ;QAAnBM,MAAM,GAAAC,wBAAA,CAAKP,QAAQ,EAAAQ,SAAA;MACzDX,GAAG,GAAAY,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAQZ,GAAG,GAAKS,MAAM,GAAKD,aAAa,CAAE;IACjD;IACA,OAAOR,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,SAASa,kBAAkBA,CAACC,YAAY,EAAE;IACtCpB,OAAO,GAAGoB,YAAY,CAAC7B,aAAa,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS8B,cAAcA,CAACC,iBAAiB,EAAE;IACvC,MAAM;MAAEC;IAAM,CAAC,GAAGhC,aAAa;IAC/B,MAAMiC,OAAO,GAAG1C,iBAAiB,CAACS,aAAa,CAACkC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7D;AACR;AACA;AACA;IACQ,MAAMjC,UAAU,GAAG,EAAE;IACrB;AACR;AACA;AACA;IACQ,MAAMkC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,IAAIC,mBAAmB,GAAGC,QAAQ;IAClC;AACR;AACA;AACA;AACA;AACA;IACQ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,iBAAiB,EAAE2C,CAAC,EAAE,EAAE;MACxC,MAAM1B,IAAI,GAAGnB,oBAAoB,CAAC6C,CAAC,CAAC;MACpC,MAAMC,SAAS,GAAG/B,KAAK,CAACI,IAAI,CAAC;MAC7B,MAAM4B,IAAI,GAAGV,KAAK,CAAClB,IAAI,CAAC,KAAKO,SAAS,GAChCW,KAAK,CAAClB,IAAI,CAAC,GACXmB,OAAO,CAACnB,IAAI,CAAC;MACnB,MAAM6B,aAAa,GAAGnD,cAAc,CAACkD,IAAI,CAAC;MAC1C;AACZ;AACA;AACA;MACY,MAAME,WAAW,GAAG9B,IAAI,KAAKiB,iBAAiB,GAAGU,SAAS,CAACI,QAAQ,GAAG,IAAI;MAC1E,IAAID,WAAW,KAAK,KAAK,EACrBN,mBAAmB,GAAGE,CAAC;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIM,WAAW,GAAGJ,IAAI,KAAKT,OAAO,CAACnB,IAAI,CAAC,IACpC4B,IAAI,KAAKV,KAAK,CAAClB,IAAI,CAAC,IACpB6B,aAAa;MACjB;AACZ;AACA;MACY,IAAIG,WAAW,IACXlC,eAAe,IACfZ,aAAa,CAAC+C,sBAAsB,EAAE;QACtCD,WAAW,GAAG,KAAK;MACvB;MACA;AACZ;AACA;AACA;MACYL,SAAS,CAACO,aAAa,GAAArB,aAAA,KAAQU,eAAe,CAAE;MAChD;MACA;MACA;MACC,CAACI,SAAS,CAACI,QAAQ,IAAID,WAAW,KAAK,IAAI;MACxC;MACC,CAACF,IAAI,IAAI,CAACD,SAAS,CAACQ,QAAS;MAC9B;MACA7D,mBAAmB,CAACsD,IAAI,CAAC,IACzB,OAAOA,IAAI,KAAK,SAAS,EAAE;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMQ,gBAAgB,GAAGC,sBAAsB,CAACV,SAAS,CAACQ,QAAQ,EAAEP,IAAI,CAAC;MACzE,IAAIU,iBAAiB,GAAGF,gBAAgB;MACpC;MACCpC,IAAI,KAAKiB,iBAAiB,IACvBU,SAAS,CAACI,QAAQ,IAClB,CAACC,WAAW,IACZH,aAAc;MAClB;MACCH,CAAC,GAAGF,mBAAmB,IAAIK,aAAc;MAC9C,IAAIU,oBAAoB,GAAG,KAAK;MAChC;AACZ;AACA;AACA;MACY,MAAMC,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACd,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;MAC1D;AACZ;AACA;AACA;MACY,IAAIe,cAAc,GAAGH,cAAc,CAACI,MAAM,CAAC7C,uBAAuB,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7E,IAAI8B,WAAW,KAAK,KAAK,EACrBa,cAAc,GAAG,CAAC,CAAC;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM;QAAEE,kBAAkB,GAAG,CAAC;MAAE,CAAC,GAAGlB,SAAS;MAC7C,MAAMmB,OAAO,GAAAjC,aAAA,CAAAA,aAAA,KACNgC,kBAAkB,GAClBF,cAAc,CACpB;MACD,MAAMI,aAAa,GAAIC,GAAG,IAAK;QAC3BV,iBAAiB,GAAG,IAAI;QACxB,IAAIjB,WAAW,CAAC4B,GAAG,CAACD,GAAG,CAAC,EAAE;UACtBT,oBAAoB,GAAG,IAAI;UAC3BlB,WAAW,CAAC6B,MAAM,CAACF,GAAG,CAAC;QAC3B;QACArB,SAAS,CAACwB,cAAc,CAACH,GAAG,CAAC,GAAG,IAAI;QACpC,MAAMI,WAAW,GAAGlE,aAAa,CAACmE,QAAQ,CAACL,GAAG,CAAC;QAC/C,IAAII,WAAW,EACXA,WAAW,CAACE,SAAS,GAAG,KAAK;MACrC,CAAC;MACD,KAAK,MAAMN,GAAG,IAAIF,OAAO,EAAE;QACvB,MAAMS,IAAI,GAAGZ,cAAc,CAACK,GAAG,CAAC;QAChC,MAAMQ,IAAI,GAAGX,kBAAkB,CAACG,GAAG,CAAC;QACpC;QACA,IAAIzB,eAAe,CAACkC,cAAc,CAACT,GAAG,CAAC,EACnC;QACJ;AAChB;AACA;QACgB,IAAIU,eAAe,GAAG,KAAK;QAC3B,IAAInF,iBAAiB,CAACgF,IAAI,CAAC,IAAIhF,iBAAiB,CAACiF,IAAI,CAAC,EAAE;UACpDE,eAAe,GAAG,CAAClF,cAAc,CAAC+E,IAAI,EAAEC,IAAI,CAAC;QACjD,CAAC,MACI;UACDE,eAAe,GAAGH,IAAI,KAAKC,IAAI;QACnC;QACA,IAAIE,eAAe,EAAE;UACjB,IAAIH,IAAI,KAAKhD,SAAS,IAAIgD,IAAI,KAAK,IAAI,EAAE;YACrC;YACAR,aAAa,CAACC,GAAG,CAAC;UACtB,CAAC,MACI;YACD;YACA3B,WAAW,CAACsC,GAAG,CAACX,GAAG,CAAC;UACxB;QACJ,CAAC,MACI,IAAIO,IAAI,KAAKhD,SAAS,IAAIc,WAAW,CAAC4B,GAAG,CAACD,GAAG,CAAC,EAAE;UACjD;AACpB;AACA;AACA;UACoBD,aAAa,CAACC,GAAG,CAAC;QACtB,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoBrB,SAAS,CAACO,aAAa,CAACc,GAAG,CAAC,GAAG,IAAI;QACvC;MACJ;MACA;AACZ;AACA;AACA;MACYrB,SAAS,CAACQ,QAAQ,GAAGP,IAAI;MACzBD,SAAS,CAACkB,kBAAkB,GAAGF,cAAc;MAC7C;AACZ;AACA;MACY,IAAIhB,SAAS,CAACI,QAAQ,EAAE;QACpBR,eAAe,GAAAV,aAAA,CAAAA,aAAA,KAAQU,eAAe,GAAKoB,cAAc,CAAE;MAC/D;MACA,IAAI7C,eAAe,IAAIZ,aAAa,CAAC0E,qBAAqB,EAAE;QACxDtB,iBAAiB,GAAG,KAAK;MAC7B;MACA;AACZ;AACA;AACA;MACY,MAAMuB,oBAAoB,GAAG7B,WAAW,IAAII,gBAAgB;MAC5D,MAAMe,cAAc,GAAG,CAACU,oBAAoB,IAAItB,oBAAoB;MACpE,IAAID,iBAAiB,IAAIa,cAAc,EAAE;QACrChE,UAAU,CAAC2E,IAAI,CAAC,GAAGtB,cAAc,CAAClD,GAAG,CAAEE,SAAS,KAAM;UAClDA,SAAS,EAAEA,SAAS;UACpBC,OAAO,EAAE;YAAEO;UAAK;QACpB,CAAC,CAAC,CAAC,CAAC;MACR;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIqB,WAAW,CAAC0C,IAAI,EAAE;MAClB,MAAMC,iBAAiB,GAAG,CAAC,CAAC;MAC5B;AACZ;AACA;AACA;MACY,IAAI,OAAO9C,KAAK,CAAC+C,OAAO,KAAK,SAAS,EAAE;QACpC,MAAMC,iBAAiB,GAAGvF,cAAc,CAACO,aAAa,EAAEuD,KAAK,CAACC,OAAO,CAACxB,KAAK,CAAC+C,OAAO,CAAC,GAC9E/C,KAAK,CAAC+C,OAAO,CAAC,CAAC,CAAC,GAChB/C,KAAK,CAAC+C,OAAO,CAAC;QACpB,IAAIC,iBAAiB,IAAIA,iBAAiB,CAAC1D,UAAU,EAAE;UACnDwD,iBAAiB,CAACxD,UAAU,GAAG0D,iBAAiB,CAAC1D,UAAU;QAC/D;MACJ;MACAa,WAAW,CAAC8C,OAAO,CAAEnB,GAAG,IAAK;QACzB,MAAMoB,cAAc,GAAGlF,aAAa,CAACmF,aAAa,CAACrB,GAAG,CAAC;QACvD,MAAMI,WAAW,GAAGlE,aAAa,CAACmE,QAAQ,CAACL,GAAG,CAAC;QAC/C,IAAII,WAAW,EACXA,WAAW,CAACE,SAAS,GAAG,IAAI;QAChC;QACAU,iBAAiB,CAAChB,GAAG,CAAC,GAAGoB,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI,IAAI;MACnD,CAAC,CAAC;MACFjF,UAAU,CAAC2E,IAAI,CAAC;QAAEtE,SAAS,EAAEwE;MAAkB,CAAC,CAAC;IACrD;IACA,IAAIM,aAAa,GAAGC,OAAO,CAACpF,UAAU,CAACH,MAAM,CAAC;IAC9C,IAAIc,eAAe,KACdoB,KAAK,CAAC+C,OAAO,KAAK,KAAK,IAAI/C,KAAK,CAAC+C,OAAO,KAAK/C,KAAK,CAACvB,OAAO,CAAC,IAC5D,CAACT,aAAa,CAAC+C,sBAAsB,EAAE;MACvCqC,aAAa,GAAG,KAAK;IACzB;IACAxE,eAAe,GAAG,KAAK;IACvB,OAAOwE,aAAa,GAAG3E,OAAO,CAACR,UAAU,CAAC,GAAGC,OAAO,CAACoF,OAAO,CAAC,CAAC;EAClE;EACA;AACJ;AACA;EACI,SAASC,SAASA,CAACzE,IAAI,EAAE+B,QAAQ,EAAE;IAAA,IAAA2C,qBAAA;IAC/B;IACA,IAAI9E,KAAK,CAACI,IAAI,CAAC,CAAC+B,QAAQ,KAAKA,QAAQ,EACjC,OAAO3C,OAAO,CAACoF,OAAO,CAAC,CAAC;IAC5B;IACA,CAAAE,qBAAA,GAAAxF,aAAa,CAACyF,eAAe,cAAAD,qBAAA,eAA7BA,qBAAA,CAA+BP,OAAO,CAAES,KAAK;MAAA,IAAAC,qBAAA;MAAA,QAAAA,qBAAA,GAAKD,KAAK,CAACE,cAAc,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBJ,SAAS,CAACzE,IAAI,EAAE+B,QAAQ,CAAC;IAAA,EAAC;IAClGnC,KAAK,CAACI,IAAI,CAAC,CAAC+B,QAAQ,GAAGA,QAAQ;IAC/B,MAAM5C,UAAU,GAAG6B,cAAc,CAAChB,IAAI,CAAC;IACvC,KAAK,MAAMgD,GAAG,IAAIpD,KAAK,EAAE;MACrBA,KAAK,CAACoD,GAAG,CAAC,CAACd,aAAa,GAAG,CAAC,CAAC;IACjC;IACA,OAAO/C,UAAU;EACrB;EACA,OAAO;IACH6B,cAAc;IACdyD,SAAS;IACT3D,kBAAkB;IAClBiE,QAAQ,EAAEA,CAAA,KAAMnF,KAAK;IACrBoF,KAAK,EAAEA,CAAA,KAAM;MACTpF,KAAK,GAAGC,WAAW,CAAC,CAAC;MACrBC,eAAe,GAAG,IAAI;IAC1B;EACJ,CAAC;AACL;AACA,SAASuC,sBAAsBA,CAACmB,IAAI,EAAED,IAAI,EAAE;EACxC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI,KAAKC,IAAI;EACxB,CAAC,MACI,IAAIf,KAAK,CAACC,OAAO,CAACa,IAAI,CAAC,EAAE;IAC1B,OAAO,CAAC/E,cAAc,CAAC+E,IAAI,EAAEC,IAAI,CAAC;EACtC;EACA,OAAO,KAAK;AAChB;AACA,SAASyB,eAAeA,CAAA,EAAmB;EAAA,IAAlBlD,QAAQ,GAAAmD,SAAA,CAAAlG,MAAA,QAAAkG,SAAA,QAAA3E,SAAA,GAAA2E,SAAA,MAAG,KAAK;EACrC,OAAO;IACHnD,QAAQ;IACRG,aAAa,EAAE,CAAC,CAAC;IACjBiB,cAAc,EAAE,CAAC,CAAC;IAClBN,kBAAkB,EAAE,CAAC;EACzB,CAAC;AACL;AACA,SAAShD,WAAWA,CAAA,EAAG;EACnB,OAAO;IACHF,OAAO,EAAEsF,eAAe,CAAC,IAAI,CAAC;IAC9BE,WAAW,EAAEF,eAAe,CAAC,CAAC;IAC9BG,UAAU,EAAEH,eAAe,CAAC,CAAC;IAC7BI,QAAQ,EAAEJ,eAAe,CAAC,CAAC;IAC3BK,SAAS,EAAEL,eAAe,CAAC,CAAC;IAC5BM,UAAU,EAAEN,eAAe,CAAC,CAAC;IAC7BO,IAAI,EAAEP,eAAe,CAAC;EAC1B,CAAC;AACL;AAEA,SAAS5C,sBAAsB,EAAE3C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}