{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link,useLocation}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import{Bars3Icon,XMarkIcon,ShoppingBagIcon,MagnifyingGlassIcon,UserIcon,HeartIcon,HomeIcon,TagIcon,PhoneIcon,InformationCircleIcon}from'@heroicons/react/24/outline';import ShoppingCart from'./ShoppingCart';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Navigation=()=>{const[isOpen,setIsOpen]=useState(false);const[isScrolled,setIsScrolled]=useState(false);const[searchQuery,setSearchQuery]=useState('');const location=useLocation();// const { totalItems } = useCart(); // Available for future use\nuseEffect(()=>{const handleScroll=()=>{setIsScrolled(window.scrollY>20);};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);const navigationItems=[{name:'Home',href:'/',icon:HomeIcon},{name:'Products',href:'/products',icon:TagIcon},{name:'Digital',href:'/digital-products',icon:TagIcon},{name:'About',href:'/about',icon:InformationCircleIcon},{name:'Contact',href:'/contact',icon:PhoneIcon}];const isActive=path=>location.pathname===path;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(motion.nav,{initial:{y:-100},animate:{y:0},className:\"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled?'bg-white/95 backdrop-blur-md shadow-lg':'bg-transparent'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 lg:h-20\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(motion.div,{whileHover:{rotate:360},transition:{duration:0.5},className:\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold transition-colors duration-300 \".concat(isScrolled?'text-gray-900':'text-white'),children:\"ShopHub\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:flex items-center space-x-8\",children:navigationItems.map(item=>/*#__PURE__*/_jsxs(Link,{to:item.href,className:\"relative px-3 py-2 text-sm font-medium transition-colors duration-300 \".concat(isActive(item.href)?isScrolled?'text-light-orange-600':'text-yellow-300':isScrolled?'text-gray-700 hover:text-light-orange-600':'text-white hover:text-yellow-300'),children:[item.name,isActive(item.href)&&/*#__PURE__*/_jsx(motion.div,{layoutId:\"activeTab\",className:\"absolute bottom-0 left-0 right-0 h-0.5 \".concat(isScrolled?'bg-light-orange-600':'bg-yellow-300')})]},item.name))}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex items-center flex-1 max-w-md mx-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative w-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 \".concat(isScrolled?'text-gray-400':'text-white/70')})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search products...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 \".concat(isScrolled?'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300':'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50')})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},className:\"p-2 rounded-full transition-colors duration-300 \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50':'text-white hover:text-yellow-300 hover:bg-white/10'),children:/*#__PURE__*/_jsx(HeartIcon,{className:\"w-6 h-6\"})}),/*#__PURE__*/_jsx(Link,{to:\"/account\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},className:\"p-2 rounded-full transition-colors duration-300 \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50':'text-white hover:text-yellow-300 hover:bg-white/10'),children:/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6\"})})}),/*#__PURE__*/_jsx(ShoppingCart,{}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsOpen(!isOpen),className:\"lg:hidden p-2 rounded-md transition-colors duration-300 \".concat(isScrolled?'text-gray-700 hover:text-light-orange-600':'text-white hover:text-yellow-300'),children:isOpen?/*#__PURE__*/_jsx(XMarkIcon,{className:\"w-6 h-6\"}):/*#__PURE__*/_jsx(Bars3Icon,{className:\"w-6 h-6\"})})]})]})}),/*#__PURE__*/_jsx(AnimatePresence,{children:isOpen&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},exit:{opacity:0,height:0},className:\"lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-6 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search products...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full pl-10 pr-4 py-3 bg-gray-100 text-gray-900 placeholder-gray-500 rounded-lg focus:bg-white focus:ring-2 focus:ring-light-orange-300\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:navigationItems.map(item=>/*#__PURE__*/_jsxs(Link,{to:item.href,onClick:()=>setIsOpen(false),className:\"flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 \".concat(isActive(item.href)?'bg-light-orange-100 text-light-orange-700':'text-gray-700 hover:bg-gray-100'),children:[/*#__PURE__*/_jsx(item.icon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:item.name})]},item.name))}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-around pt-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",children:[/*#__PURE__*/_jsx(HeartIcon,{className:\"w-6 h-6\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs\",children:\"Wishlist\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/account\",className:\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-6 h-6\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs\",children:\"Account\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center space-y-1\",children:[/*#__PURE__*/_jsx(ShoppingCart,{}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-600\",children:\"Cart\"})]})]})]})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"h-16 lg:h-20\"})]});};export default Navigation;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "motion", "AnimatePresence", "Bars3Icon", "XMarkIcon", "ShoppingBagIcon", "MagnifyingGlassIcon", "UserIcon", "HeartIcon", "HomeIcon", "TagIcon", "PhoneIcon", "InformationCircleIcon", "ShoppingCart", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Navigation", "isOpen", "setIsOpen", "isScrolled", "setIsScrolled", "searchQuery", "setSearch<PERSON>uery", "location", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navigationItems", "name", "href", "icon", "isActive", "path", "pathname", "children", "nav", "initial", "y", "animate", "className", "concat", "to", "div", "whileHover", "rotate", "transition", "duration", "map", "item", "layoutId", "type", "placeholder", "value", "onChange", "e", "target", "button", "scale", "whileTap", "onClick", "opacity", "height", "exit"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  ShoppingBagIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  HeartIcon,\n  HomeIcon,\n  TagIcon,\n  PhoneIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport ShoppingCart from './ShoppingCart';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const location = useLocation();\n  // const { totalItems } = useCart(); // Available for future use\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigationItems = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Products', href: '/products', icon: TagIcon },\n    { name: 'Digital', href: '/digital-products', icon: TagIcon },\n    { name: 'About', href: '/about', icon: InformationCircleIcon },\n    { name: 'Contact', href: '/contact', icon: PhoneIcon }\n  ];\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled \n            ? 'bg-white/95 backdrop-blur-md shadow-lg' \n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <Link to=\"/\" className=\"flex items-center space-x-3\">\n              <motion.div\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n              >\n                <ShoppingBagIcon className=\"w-6 h-6 text-white\" />\n              </motion.div>\n              <span className={`text-2xl font-bold transition-colors duration-300 ${\n                isScrolled ? 'text-gray-900' : 'text-white'\n              }`}>\n                ShopHub\n              </span>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${\n                    isActive(item.href)\n                      ? isScrolled \n                        ? 'text-light-orange-600' \n                        : 'text-yellow-300'\n                      : isScrolled \n                        ? 'text-gray-700 hover:text-light-orange-600' \n                        : 'text-white hover:text-yellow-300'\n                  }`}\n                >\n                  {item.name}\n                  {isActive(item.href) && (\n                    <motion.div\n                      layoutId=\"activeTab\"\n                      className={`absolute bottom-0 left-0 right-0 h-0.5 ${\n                        isScrolled ? 'bg-light-orange-600' : 'bg-yellow-300'\n                      }`}\n                    />\n                  )}\n                </Link>\n              ))}\n            </div>\n\n            {/* Search Bar */}\n            <div className=\"hidden md:flex items-center flex-1 max-w-md mx-8\">\n              <div className=\"relative w-full\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className={`h-5 w-5 ${\n                    isScrolled ? 'text-gray-400' : 'text-white/70'\n                  }`} />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search products...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className={`w-full pl-10 pr-4 py-2 rounded-full transition-all duration-300 ${\n                    isScrolled\n                      ? 'bg-gray-100 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-light-orange-300'\n                      : 'bg-white/20 text-white placeholder-white/70 backdrop-blur-sm focus:bg-white/30 focus:ring-2 focus:ring-white/50'\n                  }`}\n                />\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                className={`p-2 rounded-full transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' \n                    : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                }`}\n              >\n                <HeartIcon className=\"w-6 h-6\" />\n              </motion.button>\n\n              <Link to=\"/account\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className={`p-2 rounded-full transition-colors duration-300 ${\n                    isScrolled \n                      ? 'text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50' \n                      : 'text-white hover:text-yellow-300 hover:bg-white/10'\n                  }`}\n                >\n                  <UserIcon className=\"w-6 h-6\" />\n                </motion.button>\n              </Link>\n\n              <ShoppingCart />\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${\n                  isScrolled \n                    ? 'text-gray-700 hover:text-light-orange-600' \n                    : 'text-white hover:text-yellow-300'\n                }`}\n              >\n                {isOpen ? (\n                  <XMarkIcon className=\"w-6 h-6\" />\n                ) : (\n                  <Bars3Icon className=\"w-6 h-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-200\"\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {/* Mobile Search */}\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search products...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-3 bg-gray-100 text-gray-900 placeholder-gray-500 rounded-lg focus:bg-white focus:ring-2 focus:ring-light-orange-300\"\n                  />\n                </div>\n\n                {/* Mobile Navigation Links */}\n                <div className=\"space-y-2\">\n                  {navigationItems.map((item) => (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setIsOpen(false)}\n                      className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-light-orange-100 text-light-orange-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      }`}\n                    >\n                      <item.icon className=\"w-5 h-5\" />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  ))}\n                </div>\n\n                {/* Mobile Action Buttons */}\n                <div className=\"flex items-center justify-around pt-4 border-t border-gray-200\">\n                  <button className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <HeartIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Wishlist</span>\n                  </button>\n                  <Link to=\"/account\" className=\"flex flex-col items-center space-y-1 text-gray-600 hover:text-light-orange-600\">\n                    <UserIcon className=\"w-6 h-6\" />\n                    <span className=\"text-xs\">Account</span>\n                  </Link>\n                  <div className=\"flex flex-col items-center space-y-1\">\n                    <ShoppingCart />\n                    <span className=\"text-xs text-gray-600\">Cart</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed nav */}\n      <div className=\"h-16 lg:h-20\"></div>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,SAAS,CACTC,SAAS,CACTC,eAAe,CACfC,mBAAmB,CACnBC,QAAQ,CACRC,SAAS,CACTC,QAAQ,CACRC,OAAO,CACPC,SAAS,CACTC,qBAAqB,KAChB,6BAA6B,CACpC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC0B,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC4B,WAAW,CAAEC,cAAc,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAA8B,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B;AAEAF,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8B,YAAY,CAAGA,CAAA,GAAM,CACzBJ,aAAa,CAACK,MAAM,CAACC,OAAO,CAAG,EAAE,CAAC,CACpC,CAAC,CAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAC/C,MAAO,IAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAK,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,GAAG,CAAEC,IAAI,CAAE3B,QAAS,CAAC,CAC3C,CAAEyB,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE1B,OAAQ,CAAC,CACtD,CAAEwB,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,IAAI,CAAE1B,OAAQ,CAAC,CAC7D,CAAEwB,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAExB,qBAAsB,CAAC,CAC9D,CAAEsB,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEzB,SAAU,CAAC,CACvD,CAED,KAAM,CAAA0B,QAAQ,CAAIC,IAAI,EAAKX,QAAQ,CAACY,QAAQ,GAAKD,IAAI,CAErD,mBACErB,KAAA,CAAAE,SAAA,EAAAqB,QAAA,eACEvB,KAAA,CAAChB,MAAM,CAACwC,GAAG,EACTC,OAAO,CAAE,CAAEC,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBC,OAAO,CAAE,CAAED,CAAC,CAAE,CAAE,CAAE,CAClBE,SAAS,gEAAAC,MAAA,CACPvB,UAAU,CACN,wCAAwC,CACxC,gBAAgB,CACnB,CAAAiB,QAAA,eAEHzB,IAAA,QAAK8B,SAAS,CAAC,wCAAwC,CAAAL,QAAA,cACrDvB,KAAA,QAAK4B,SAAS,CAAC,gDAAgD,CAAAL,QAAA,eAE7DvB,KAAA,CAAClB,IAAI,EAACgD,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,6BAA6B,CAAAL,QAAA,eAClDzB,IAAA,CAACd,MAAM,CAAC+C,GAAG,EACTC,UAAU,CAAE,CAAEC,MAAM,CAAE,GAAI,CAAE,CAC5BC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BP,SAAS,CAAC,8HAA8H,CAAAL,QAAA,cAExIzB,IAAA,CAACV,eAAe,EAACwC,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACb9B,IAAA,SAAM8B,SAAS,sDAAAC,MAAA,CACbvB,UAAU,CAAG,eAAe,CAAG,YAAY,CAC1C,CAAAiB,QAAA,CAAC,SAEJ,CAAM,CAAC,EACH,CAAC,cAGPzB,IAAA,QAAK8B,SAAS,CAAC,uCAAuC,CAAAL,QAAA,CACnDP,eAAe,CAACoB,GAAG,CAAEC,IAAI,eACxBrC,KAAA,CAAClB,IAAI,EAEHgD,EAAE,CAAEO,IAAI,CAACnB,IAAK,CACdU,SAAS,0EAAAC,MAAA,CACPT,QAAQ,CAACiB,IAAI,CAACnB,IAAI,CAAC,CACfZ,UAAU,CACR,uBAAuB,CACvB,iBAAiB,CACnBA,UAAU,CACR,2CAA2C,CAC3C,kCAAkC,CACvC,CAAAiB,QAAA,EAEFc,IAAI,CAACpB,IAAI,CACTG,QAAQ,CAACiB,IAAI,CAACnB,IAAI,CAAC,eAClBpB,IAAA,CAACd,MAAM,CAAC+C,GAAG,EACTO,QAAQ,CAAC,WAAW,CACpBV,SAAS,2CAAAC,MAAA,CACPvB,UAAU,CAAG,qBAAqB,CAAG,eAAe,CACnD,CACJ,CACF,GApBI+B,IAAI,CAACpB,IAqBN,CACP,CAAC,CACC,CAAC,cAGNnB,IAAA,QAAK8B,SAAS,CAAC,kDAAkD,CAAAL,QAAA,cAC/DvB,KAAA,QAAK4B,SAAS,CAAC,iBAAiB,CAAAL,QAAA,eAC9BzB,IAAA,QAAK8B,SAAS,CAAC,sEAAsE,CAAAL,QAAA,cACnFzB,IAAA,CAACT,mBAAmB,EAACuC,SAAS,YAAAC,MAAA,CAC5BvB,UAAU,CAAG,eAAe,CAAG,eAAe,CAC7C,CAAE,CAAC,CACH,CAAC,cACNR,IAAA,UACEyC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEjC,WAAY,CACnBkC,QAAQ,CAAGC,CAAC,EAAKlC,cAAc,CAACkC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDb,SAAS,oEAAAC,MAAA,CACPvB,UAAU,CACN,wGAAwG,CACxG,iHAAiH,CACpH,CACJ,CAAC,EACC,CAAC,CACH,CAAC,cAGNN,KAAA,QAAK4B,SAAS,CAAC,6BAA6B,CAAAL,QAAA,eAC1CzB,IAAA,CAACd,MAAM,CAAC6D,MAAM,EACZb,UAAU,CAAE,CAAEc,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBlB,SAAS,oDAAAC,MAAA,CACPvB,UAAU,CACN,oEAAoE,CACpE,oDAAoD,CACvD,CAAAiB,QAAA,cAEHzB,IAAA,CAACP,SAAS,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAC,CACpB,CAAC,cAEhB9B,IAAA,CAAChB,IAAI,EAACgD,EAAE,CAAC,UAAU,CAAAP,QAAA,cACjBzB,IAAA,CAACd,MAAM,CAAC6D,MAAM,EACZb,UAAU,CAAE,CAAEc,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBlB,SAAS,oDAAAC,MAAA,CACPvB,UAAU,CACN,oEAAoE,CACpE,oDAAoD,CACvD,CAAAiB,QAAA,cAEHzB,IAAA,CAACR,QAAQ,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,CACZ,CAAC,cAEP9B,IAAA,CAACF,YAAY,GAAE,CAAC,cAGhBE,IAAA,WACEkD,OAAO,CAAEA,CAAA,GAAM3C,SAAS,CAAC,CAACD,MAAM,CAAE,CAClCwB,SAAS,4DAAAC,MAAA,CACPvB,UAAU,CACN,2CAA2C,CAC3C,kCAAkC,CACrC,CAAAiB,QAAA,CAEFnB,MAAM,cACLN,IAAA,CAACX,SAAS,EAACyC,SAAS,CAAC,SAAS,CAAE,CAAC,cAEjC9B,IAAA,CAACZ,SAAS,EAAC0C,SAAS,CAAC,SAAS,CAAE,CACjC,CACK,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN9B,IAAA,CAACb,eAAe,EAAAsC,QAAA,CACbnB,MAAM,eACLN,IAAA,CAACd,MAAM,CAAC+C,GAAG,EACTN,OAAO,CAAE,CAAEwB,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAE,CACnCvB,OAAO,CAAE,CAAEsB,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,MAAO,CAAE,CACxCC,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAE,CAChCtB,SAAS,CAAC,iEAAiE,CAAAL,QAAA,cAE3EvB,KAAA,QAAK4B,SAAS,CAAC,qBAAqB,CAAAL,QAAA,eAElCvB,KAAA,QAAK4B,SAAS,CAAC,UAAU,CAAAL,QAAA,eACvBzB,IAAA,QAAK8B,SAAS,CAAC,sEAAsE,CAAAL,QAAA,cACnFzB,IAAA,CAACT,mBAAmB,EAACuC,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtD,CAAC,cACN9B,IAAA,UACEyC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEjC,WAAY,CACnBkC,QAAQ,CAAGC,CAAC,EAAKlC,cAAc,CAACkC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDb,SAAS,CAAC,0IAA0I,CACrJ,CAAC,EACC,CAAC,cAGN9B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAL,QAAA,CACvBP,eAAe,CAACoB,GAAG,CAAEC,IAAI,eACxBrC,KAAA,CAAClB,IAAI,EAEHgD,EAAE,CAAEO,IAAI,CAACnB,IAAK,CACd8B,OAAO,CAAEA,CAAA,GAAM3C,SAAS,CAAC,KAAK,CAAE,CAChCuB,SAAS,oFAAAC,MAAA,CACPT,QAAQ,CAACiB,IAAI,CAACnB,IAAI,CAAC,CACf,2CAA2C,CAC3C,iCAAiC,CACpC,CAAAK,QAAA,eAEHzB,IAAA,CAACuC,IAAI,CAAClB,IAAI,EAACS,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC9B,IAAA,SAAM8B,SAAS,CAAC,aAAa,CAAAL,QAAA,CAAEc,IAAI,CAACpB,IAAI,CAAO,CAAC,GAV3CoB,IAAI,CAACpB,IAWN,CACP,CAAC,CACC,CAAC,cAGNjB,KAAA,QAAK4B,SAAS,CAAC,gEAAgE,CAAAL,QAAA,eAC7EvB,KAAA,WAAQ4B,SAAS,CAAC,gFAAgF,CAAAL,QAAA,eAChGzB,IAAA,CAACP,SAAS,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC9B,IAAA,SAAM8B,SAAS,CAAC,SAAS,CAAAL,QAAA,CAAC,UAAQ,CAAM,CAAC,EACnC,CAAC,cACTvB,KAAA,CAAClB,IAAI,EAACgD,EAAE,CAAC,UAAU,CAACF,SAAS,CAAC,gFAAgF,CAAAL,QAAA,eAC5GzB,IAAA,CAACR,QAAQ,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC9B,IAAA,SAAM8B,SAAS,CAAC,SAAS,CAAAL,QAAA,CAAC,SAAO,CAAM,CAAC,EACpC,CAAC,cACPvB,KAAA,QAAK4B,SAAS,CAAC,sCAAsC,CAAAL,QAAA,eACnDzB,IAAA,CAACF,YAAY,GAAE,CAAC,cAChBE,IAAA,SAAM8B,SAAS,CAAC,uBAAuB,CAAAL,QAAA,CAAC,MAAI,CAAM,CAAC,EAChD,CAAC,EACH,CAAC,EACH,CAAC,CACI,CACb,CACc,CAAC,EACR,CAAC,cAGbzB,IAAA,QAAK8B,SAAS,CAAC,cAAc,CAAM,CAAC,EACpC,CAAC,CAEP,CAAC,CAED,cAAe,CAAAzB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}