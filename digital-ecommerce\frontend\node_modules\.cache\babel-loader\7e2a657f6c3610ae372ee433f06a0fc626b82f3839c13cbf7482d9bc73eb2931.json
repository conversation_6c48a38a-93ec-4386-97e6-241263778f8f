{"ast": null, "code": "import { auto } from './auto.mjs';\nimport { number } from './numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from './numbers/units.mjs';\nimport { testValueType } from './test.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = v => dimensionValueTypes.find(testValueType(v));\nexport { dimensionValueTypes, findDimensionValueType };", "map": {"version": 3, "names": ["auto", "number", "px", "percent", "degrees", "vw", "vh", "testValueType", "dimensionValueTypes", "findDimensionValueType", "v", "find"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/value/types/dimensions.mjs"], "sourcesContent": ["import { auto } from './auto.mjs';\nimport { number } from './numbers/index.mjs';\nimport { px, percent, degrees, vw, vh } from './numbers/units.mjs';\nimport { testValueType } from './test.mjs';\n\n/**\n * A list of value types commonly used for dimensions\n */\nconst dimensionValueTypes = [number, px, percent, degrees, vw, vh, auto];\n/**\n * Tests a dimensional value against the list of dimension ValueTypes\n */\nconst findDimensionValueType = (v) => dimensionValueTypes.find(testValueType(v));\n\nexport { dimensionValueTypes, findDimensionValueType };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AACjC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,QAAQ,qBAAqB;AAClE,SAASC,aAAa,QAAQ,YAAY;;AAE1C;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,CAACP,MAAM,EAAEC,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEN,IAAI,CAAC;AACxE;AACA;AACA;AACA,MAAMS,sBAAsB,GAAIC,CAAC,IAAKF,mBAAmB,CAACG,IAAI,CAACJ,aAAa,CAACG,CAAC,CAAC,CAAC;AAEhF,SAASF,mBAAmB,EAAEC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}