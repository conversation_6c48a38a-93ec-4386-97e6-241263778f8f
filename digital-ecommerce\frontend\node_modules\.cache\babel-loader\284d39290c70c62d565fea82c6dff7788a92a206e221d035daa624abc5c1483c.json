{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\ContactPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { PhoneIcon, EnvelopeIcon, MapPinIcon, ClockIcon, ChatBubbleLeftRightIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';\nimport toast, { Toaster } from 'react-hot-toast';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ContactPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const contactInfo = [{\n    icon: PhoneIcon,\n    title: 'Phone',\n    details: '+****************',\n    description: 'Mon-Fri from 8am to 6pm'\n  }, {\n    icon: EnvelopeIcon,\n    title: 'Email',\n    details: '<EMAIL>',\n    description: 'We reply within 24 hours'\n  }, {\n    icon: MapPinIcon,\n    title: 'Office',\n    details: '123 Commerce Street, Tech City, TC 12345',\n    description: 'Visit our headquarters'\n  }, {\n    icon: ClockIcon,\n    title: 'Business Hours',\n    details: 'Monday - Friday: 8am - 6pm',\n    description: 'Saturday: 9am - 4pm'\n  }];\n  const faqs = [{\n    question: 'How can I track my order?',\n    answer: 'You can track your order by logging into your account and visiting the \"Order History\" section, or by using the tracking number sent to your email.'\n  }, {\n    question: 'What is your return policy?',\n    answer: 'We offer a 30-day return policy for most items. Products must be in original condition with tags attached. Some restrictions apply to electronics and personal care items.'\n  }, {\n    question: 'Do you offer international shipping?',\n    answer: 'Yes, we ship to over 50 countries worldwide. Shipping costs and delivery times vary by location. Check our shipping page for detailed information.'\n  }, {\n    question: 'How can I change or cancel my order?',\n    answer: 'Orders can be modified or cancelled within 1 hour of placement. After that, please contact our customer service team for assistance.'\n  }];\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    toast.success('Message sent successfully! We\\'ll get back to you soon.');\n    setFormData({\n      name: '',\n      email: '',\n      subject: '',\n      message: ''\n    });\n    setIsSubmitting(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl lg:text-5xl font-bold text-white mb-6\",\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-light-orange-100 max-w-2xl mx-auto\",\n            children: \"We're here to help! Reach out to us with any questions, concerns, or feedback.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(info.icon, {\n                className: \"w-8 h-8 text-light-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: info.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-light-orange-600 font-medium mb-1\",\n              children: info.details\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: info.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            viewport: {\n              once: true\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                children: \"Send us a Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Your Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      name: \"name\",\n                      value: formData.name,\n                      onChange: handleInputChange,\n                      required: true,\n                      className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n                      placeholder: \"John Doe\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Email Address\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      name: \"email\",\n                      value: formData.email,\n                      onChange: handleInputChange,\n                      required: true,\n                      className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n                      placeholder: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Subject\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"subject\",\n                    value: formData.subject,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",\n                    placeholder: \"How can we help you?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Message\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"message\",\n                    value: formData.message,\n                    onChange: handleInputChange,\n                    required: true,\n                    rows: 6,\n                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors resize-none\",\n                    placeholder: \"Tell us more about your inquiry...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  type: \"submit\",\n                  disabled: isSubmitting,\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Send Message\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-200 rounded-2xl h-64 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                  className: \"w-12 h-12 text-gray-400 mx-auto mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Interactive Map\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"123 Commerce Street, Tech City\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-2xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n                  className: \"w-8 h-8 text-blue-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Live Chat Support\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"Need immediate assistance? Our live chat support is available during business hours.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Start Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-2xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  className: \"w-8 h-8 text-red-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Emergency Support\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-2\",\n                children: \"For urgent order issues or account problems:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-600 font-semibold\",\n                children: \"+1 (555) 911-HELP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Available 24/7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600\",\n            children: \"Quick answers to common questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl p-6 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-3\",\n              children: faq.question\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 leading-relaxed\",\n              children: faq.answer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(ContactPage, \"uWH4rPLSJy+HiTNvrPHQ1reLdfI=\");\n_c = ContactPage;\nexport default ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "PhoneIcon", "EnvelopeIcon", "MapPinIcon", "ClockIcon", "ChatBubbleLeftRightIcon", "PaperAirplaneIcon", "toast", "Toaster", "<PERSON><PERSON>", "Input", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ContactPage", "_s", "formData", "setFormData", "name", "email", "subject", "message", "isSubmitting", "setIsSubmitting", "contactInfo", "icon", "title", "details", "description", "faqs", "question", "answer", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "Promise", "resolve", "setTimeout", "success", "className", "children", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "map", "info", "index", "whileInView", "delay", "viewport", "once", "x", "onSubmit", "type", "onChange", "required", "placeholder", "rows", "button", "disabled", "whileHover", "scale", "whileTap", "faq", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/ContactPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PhoneIcon,\n  EnvelopeIcon,\n  MapPinIcon,\n  ClockIcon,\n  ChatBubbleLeftRightIcon,\n  PaperAirplaneIcon\n} from '@heroicons/react/24/outline';\nimport toast, { Toaster } from 'react-hot-toast';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\n\nconst ContactPage = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const contactInfo = [\n    {\n      icon: PhoneIcon,\n      title: 'Phone',\n      details: '+****************',\n      description: 'Mon-Fri from 8am to 6pm'\n    },\n    {\n      icon: EnvelopeIcon,\n      title: 'Email',\n      details: '<EMAIL>',\n      description: 'We reply within 24 hours'\n    },\n    {\n      icon: MapPinIcon,\n      title: 'Office',\n      details: '123 Commerce Street, Tech City, TC 12345',\n      description: 'Visit our headquarters'\n    },\n    {\n      icon: ClockIcon,\n      title: 'Business Hours',\n      details: 'Monday - Friday: 8am - 6pm',\n      description: 'Saturday: 9am - 4pm'\n    }\n  ];\n\n  const faqs = [\n    {\n      question: 'How can I track my order?',\n      answer: 'You can track your order by logging into your account and visiting the \"Order History\" section, or by using the tracking number sent to your email.'\n    },\n    {\n      question: 'What is your return policy?',\n      answer: 'We offer a 30-day return policy for most items. Products must be in original condition with tags attached. Some restrictions apply to electronics and personal care items.'\n    },\n    {\n      question: 'Do you offer international shipping?',\n      answer: 'Yes, we ship to over 50 countries worldwide. Shipping costs and delivery times vary by location. Check our shipping page for detailed information.'\n    },\n    {\n      question: 'How can I change or cancel my order?',\n      answer: 'Orders can be modified or cancelled within 1 hour of placement. After that, please contact our customer service team for assistance.'\n    }\n  ];\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    toast.success('Message sent successfully! We\\'ll get back to you soon.');\n    setFormData({ name: '', email: '', subject: '', message: '' });\n    setIsSubmitting(false);\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      <Toaster position=\"top-right\" />\n      \n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-6\">\n              Get in Touch\n            </h1>\n            <p className=\"text-xl text-light-orange-100 max-w-2xl mx-auto\">\n              We're here to help! Reach out to us with any questions, concerns, or feedback.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Contact Info Cards */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {contactInfo.map((info, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-shadow duration-300\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <info.icon className=\"w-8 h-8 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{info.title}</h3>\n                <p className=\"text-light-orange-600 font-medium mb-1\">{info.details}</p>\n                <p className=\"text-sm text-gray-600\">{info.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form & Map */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Send us a Message</h2>\n                \n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Your Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\n                        placeholder=\"John Doe\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Email Address\n                      </label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Subject\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\n                      placeholder=\"How can we help you?\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={6}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors resize-none\"\n                      placeholder=\"Tell us more about your inquiry...\"\n                    />\n                  </div>\n\n                  <motion.button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isSubmitting ? (\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                    ) : (\n                      <>\n                        <PaperAirplaneIcon className=\"w-5 h-5\" />\n                        <span>Send Message</span>\n                      </>\n                    )}\n                  </motion.button>\n                </form>\n              </div>\n            </motion.div>\n\n            {/* Map & Additional Info */}\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-8\"\n            >\n              {/* Map Placeholder */}\n              <div className=\"bg-gray-200 rounded-2xl h-64 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <MapPinIcon className=\"w-12 h-12 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-600\">Interactive Map</p>\n                  <p className=\"text-sm text-gray-500\">123 Commerce Street, Tech City</p>\n                </div>\n              </div>\n\n              {/* Live Chat */}\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-2xl\">\n                <div className=\"flex items-center mb-4\">\n                  <ChatBubbleLeftRightIcon className=\"w-8 h-8 text-blue-600 mr-3\" />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Live Chat Support</h3>\n                </div>\n                <p className=\"text-gray-600 mb-4\">\n                  Need immediate assistance? Our live chat support is available during business hours.\n                </p>\n                <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                  Start Chat\n                </button>\n              </div>\n\n              {/* Emergency Contact */}\n              <div className=\"bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-2xl\">\n                <div className=\"flex items-center mb-4\">\n                  <PhoneIcon className=\"w-8 h-8 text-red-600 mr-3\" />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Emergency Support</h3>\n                </div>\n                <p className=\"text-gray-600 mb-2\">\n                  For urgent order issues or account problems:\n                </p>\n                <p className=\"text-red-600 font-semibold\">+1 (555) 911-HELP</p>\n                <p className=\"text-sm text-gray-500\">Available 24/7</p>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-12\"\n          >\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Frequently Asked Questions\n            </h2>\n            <p className=\"text-lg text-gray-600\">\n              Quick answers to common questions\n            </p>\n          </motion.div>\n\n          <div className=\"space-y-6\">\n            {faqs.map((faq, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-white rounded-2xl p-6 shadow-lg\"\n              >\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                  {faq.question}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  {faq.answer}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default ContactPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,SAAS,EACTC,YAAY,EACZC,UAAU,EACVC,SAAS,EACTC,uBAAuB,EACvBC,iBAAiB,QACZ,6BAA6B;AACpC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAChD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM0B,WAAW,GAAG,CAClB;IACEC,IAAI,EAAEzB,SAAS;IACf0B,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,mBAAmB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAExB,YAAY;IAClByB,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,qBAAqB;IAC9BC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAEvB,UAAU;IAChBwB,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,0CAA0C;IACnDC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAEtB,SAAS;IACfuB,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE,4BAA4B;IACrCC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,IAAI,GAAG,CACX;IACEC,QAAQ,EAAE,2BAA2B;IACrCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,6BAA6B;IACvCC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAAChB,IAAI,GAAGe,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBd,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvDjC,KAAK,CAACmC,OAAO,CAAC,yDAAyD,CAAC;IACxExB,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;IAC9DE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEZ,OAAA;IAAK+B,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BhC,OAAA,CAACJ,OAAO;MAACqC,QAAQ,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhCrC,OAAA;MAAS+B,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACzGhC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDhC,OAAA,CAACZ,MAAM,CAACkD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bb,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvBhC,OAAA;YAAI+B,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAG+B,SAAS,EAAC,iDAAiD;YAAAC,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAAS+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnChC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDhC,OAAA;UAAK+B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEnB,WAAW,CAACgC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3B/C,OAAA,CAACZ,MAAM,CAACkD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BO,WAAW,EAAE;cAAER,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEK,KAAK,EAAEF,KAAK,GAAG;YAAI,CAAE;YAClDG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBpB,SAAS,EAAC,+FAA+F;YAAAC,QAAA,gBAEzGhC,OAAA;cAAK+B,SAAS,EAAC,kIAAkI;cAAAC,QAAA,eAC/IhC,OAAA,CAAC8C,IAAI,CAAChC,IAAI;gBAACiB,SAAS,EAAC;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNrC,OAAA;cAAI+B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEc,IAAI,CAAC/B;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ErC,OAAA;cAAG+B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAEc,IAAI,CAAC9B;YAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxErC,OAAA;cAAG+B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEc,IAAI,CAAC7B;YAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAZtDU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAAS+B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjChC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDhC,OAAA;UAAK+B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDhC,OAAA,CAACZ,MAAM,CAACkD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCJ,WAAW,EAAE;cAAER,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAClCT,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YAAAnB,QAAA,eAEzBhC,OAAA;cAAK+B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,gBACxFhC,OAAA;gBAAI+B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE5ErC,OAAA;gBAAMqD,QAAQ,EAAE5B,YAAa;gBAACM,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACjDhC,OAAA;kBAAK+B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDhC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAO+B,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrC,OAAA;sBACEsD,IAAI,EAAC,MAAM;sBACX/C,IAAI,EAAC,MAAM;sBACXiB,KAAK,EAAEnB,QAAQ,CAACE,IAAK;sBACrBgD,QAAQ,EAAElC,iBAAkB;sBAC5BmC,QAAQ;sBACRzB,SAAS,EAAC,6IAA6I;sBACvJ0B,WAAW,EAAC;oBAAU;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA;sBAAO+B,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRrC,OAAA;sBACEsD,IAAI,EAAC,OAAO;sBACZ/C,IAAI,EAAC,OAAO;sBACZiB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;sBACtB+C,QAAQ,EAAElC,iBAAkB;sBAC5BmC,QAAQ;sBACRzB,SAAS,EAAC,6IAA6I;sBACvJ0B,WAAW,EAAC;oBAAkB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAO+B,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrC,OAAA;oBACEsD,IAAI,EAAC,MAAM;oBACX/C,IAAI,EAAC,SAAS;oBACdiB,KAAK,EAAEnB,QAAQ,CAACI,OAAQ;oBACxB8C,QAAQ,EAAElC,iBAAkB;oBAC5BmC,QAAQ;oBACRzB,SAAS,EAAC,6IAA6I;oBACvJ0B,WAAW,EAAC;kBAAsB;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENrC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAO+B,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrC,OAAA;oBACEO,IAAI,EAAC,SAAS;oBACdiB,KAAK,EAAEnB,QAAQ,CAACK,OAAQ;oBACxB6C,QAAQ,EAAElC,iBAAkB;oBAC5BmC,QAAQ;oBACRE,IAAI,EAAE,CAAE;oBACR3B,SAAS,EAAC,yJAAyJ;oBACnK0B,WAAW,EAAC;kBAAoC;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENrC,OAAA,CAACZ,MAAM,CAACuE,MAAM;kBACZL,IAAI,EAAC,QAAQ;kBACbM,QAAQ,EAAEjD,YAAa;kBACvBkD,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1B/B,SAAS,EAAC,8RAA8R;kBAAAC,QAAA,EAEvSrB,YAAY,gBACXX,OAAA;oBAAK+B,SAAS,EAAC;kBAA2D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAEjFrC,OAAA,CAAAE,SAAA;oBAAA8B,QAAA,gBACEhC,OAAA,CAACN,iBAAiB;sBAACqC,SAAS,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzCrC,OAAA;sBAAAgC,QAAA,EAAM;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACzB;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbrC,OAAA,CAACZ,MAAM,CAACkD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAG,CAAE;YAC/BJ,WAAW,EAAE;cAAER,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAClCT,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBpB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAGrBhC,OAAA;cAAK+B,SAAS,EAAC,+DAA+D;cAAAC,QAAA,eAC5EhC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhC,OAAA,CAACT,UAAU;kBAACwC,SAAS,EAAC;gBAAsC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DrC,OAAA;kBAAG+B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChDrC,OAAA;kBAAG+B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrC,OAAA;cAAK+B,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACzEhC,OAAA;gBAAK+B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChC,OAAA,CAACP,uBAAuB;kBAACsC,SAAS,EAAC;gBAA4B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClErC,OAAA;kBAAI+B,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNrC,OAAA;gBAAG+B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJrC,OAAA;gBAAQ+B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAAC;cAEpG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNrC,OAAA;cAAK+B,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACvEhC,OAAA;gBAAK+B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChC,OAAA,CAACX,SAAS;kBAAC0C,SAAS,EAAC;gBAA2B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDrC,OAAA;kBAAI+B,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNrC,OAAA;gBAAG+B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJrC,OAAA;gBAAG+B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/DrC,OAAA;gBAAG+B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAAS+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnChC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhC,OAAA,CAACZ,MAAM,CAACkD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BO,WAAW,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBpB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BhC,OAAA;YAAI+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAG+B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbrC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBd,IAAI,CAAC2B,GAAG,CAAC,CAACmB,GAAG,EAAEjB,KAAK,kBACnB/C,OAAA,CAACZ,MAAM,CAACkD,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BO,WAAW,EAAE;cAAER,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEK,KAAK,EAAEF,KAAK,GAAG;YAAI,CAAE;YAClDG,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBpB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBAE9ChC,OAAA;cAAI+B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrDgC,GAAG,CAAC7C;YAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACLrC,OAAA;cAAG+B,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EACzCgC,GAAG,CAAC5C;YAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA,GAZCU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjC,EAAA,CAnTID,WAAW;AAAA8D,EAAA,GAAX9D,WAAW;AAqTjB,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}