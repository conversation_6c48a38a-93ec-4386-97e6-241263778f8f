{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"title\", \"titleId\"];\nimport * as React from \"react\";\nfunction SwatchIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 4.125c0-1.036.84-1.875 1.875-1.875h5.25c1.036 0 1.875.84 1.875 1.875V17.25a4.5 4.5 0 1 1-9 0V4.125Zm4.5 14.25a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z\",\n    clipRule: \"evenodd\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.719 21.75h9.156c1.036 0 1.875-.84 1.875-1.875v-5.25c0-1.036-.84-1.875-1.875-1.875h-.14l-8.742 8.743c-.09.089-.18.175-.274.257ZM12.738 17.625l6.474-6.474a1.875 1.875 0 0 0 0-2.651L15.5 4.787a1.875 1.875 0 0 0-2.651 0l-.1.099V17.25c0 .126-.003.251-.01.375Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SwatchIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "SwatchIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "fillRule", "d", "clipRule", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@heroicons/react/24/solid/esm/SwatchIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction SwatchIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 4.125c0-1.036.84-1.875 1.875-1.875h5.25c1.036 0 1.875.84 1.875 1.875V17.25a4.5 4.5 0 1 1-9 0V4.125Zm4.5 14.25a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z\",\n    clipRule: \"evenodd\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M10.719 21.75h9.156c1.036 0 1.875-.84 1.875-1.875v-5.25c0-1.036-.84-1.875-1.875-1.875h-.14l-8.742 8.743c-.09.089-.18.175-.274.257ZM12.738 17.625l6.474-6.474a1.875 1.875 0 0 0 0-2.651L15.5 4.787a1.875 1.875 0 0 0-2.651 0l-.1.099V17.25c0 .126-.003.251-.01.375Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(SwatchIcon);\nexport default ForwardRef;"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAUA,CAAAC,IAAA,EAIhBC,MAAM,EAAE;EAAA,IAJS;MAClBC,KAAK;MACLC;IAEF,CAAC,GAAAH,IAAA;IADII,KAAK,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAER,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEZ,MAAM;IACX,iBAAiB,EAAEE;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaJ,KAAK,CAACS,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEX;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaJ,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IACzDQ,QAAQ,EAAE,SAAS;IACnBC,CAAC,EAAE,wKAAwK;IAC3KC,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,aAAanB,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC3CS,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAME,UAAU,GAAG,aAAcpB,KAAK,CAACqB,UAAU,CAACpB,UAAU,CAAC;AAC7D,eAAemB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}