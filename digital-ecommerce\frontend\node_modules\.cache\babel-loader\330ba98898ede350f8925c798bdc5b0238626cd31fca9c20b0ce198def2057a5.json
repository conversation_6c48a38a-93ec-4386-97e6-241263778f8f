{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\nfunction mixNumber(a, b) {\n  return p => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n  if (typeof a === \"number\") {\n    return mixNumber;\n  } else if (typeof a === \"string\") {\n    return isCSSVariableToken(a) ? mixImmediate : color.test(a) ? mixColor : mixComplex;\n  } else if (Array.isArray(a)) {\n    return mixArray;\n  } else if (typeof a === \"object\") {\n    return color.test(a) ? mixColor : mixObject;\n  }\n  return mixImmediate;\n}\nfunction mixArray(a, b) {\n  const output = [...a];\n  const numValues = output.length;\n  const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n  return p => {\n    for (let i = 0; i < numValues; i++) {\n      output[i] = blendValue[i](p);\n    }\n    return output;\n  };\n}\nfunction mixObject(a, b) {\n  const output = _objectSpread(_objectSpread({}, a), b);\n  const blendValue = {};\n  for (const key in output) {\n    if (a[key] !== undefined && b[key] !== undefined) {\n      blendValue[key] = getMixer(a[key])(a[key], b[key]);\n    }\n  }\n  return v => {\n    for (const key in blendValue) {\n      output[key] = blendValue[key](v);\n    }\n    return output;\n  };\n}\nfunction matchOrder(origin, target) {\n  const orderedOrigin = [];\n  const pointers = {\n    color: 0,\n    var: 0,\n    number: 0\n  };\n  for (let i = 0; i < target.values.length; i++) {\n    var _origin$values$origin;\n    const type = target.types[i];\n    const originIndex = origin.indexes[type][pointers[type]];\n    const originValue = (_origin$values$origin = origin.values[originIndex]) !== null && _origin$values$origin !== void 0 ? _origin$values$origin : 0;\n    orderedOrigin[i] = originValue;\n    pointers[type]++;\n  }\n  return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n  const template = complex.createTransformer(target);\n  const originStats = analyseComplexValue(origin);\n  const targetStats = analyseComplexValue(target);\n  const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length && originStats.indexes.color.length === targetStats.indexes.color.length && originStats.indexes.number.length >= targetStats.indexes.number.length;\n  if (canInterpolate) {\n    if (invisibleValues.has(origin) && !targetStats.values.length || invisibleValues.has(target) && !originStats.values.length) {\n      return mixVisibility(origin, target);\n    }\n    return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n  } else {\n    warning(true, \"Complex values '\".concat(origin, \"' and '\").concat(target, \"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.\"));\n    return mixImmediate(origin, target);\n  }\n};\nexport { getMixer, mixArray, mixComplex, mixObject };", "map": {"version": 3, "names": ["pipe", "warning", "isCSSVariableToken", "color", "complex", "analyseComplexValue", "mixColor", "mixImmediate", "mixNumber", "mixNumber$1", "invisibleV<PERSON>ues", "mixVisibility", "a", "b", "p", "getMixer", "test", "mixComplex", "Array", "isArray", "mixArray", "mixObject", "output", "numValues", "length", "blendValue", "map", "v", "i", "_objectSpread", "key", "undefined", "matchOrder", "origin", "target", "<PERSON><PERSON><PERSON><PERSON>", "pointers", "var", "number", "values", "_origin$values$origin", "type", "types", "originIndex", "indexes", "originValue", "template", "createTransformer", "originStats", "targetStats", "canInterpolate", "has", "concat"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/mix/complex.mjs"], "sourcesContent": ["import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\n\nfunction mixNumber(a, b) {\n    return (p) => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return isCSSVariableToken(a)\n            ? mixImmediate\n            : color.test(a)\n                ? mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return color.test(a) ? mixColor : mixObject;\n    }\n    return mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = origin.values[originIndex] ?? 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return mixVisibility(origin, target);\n        }\n        return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { getMixer, mixArray, mixComplex, mixObject };\n"], "mappings": ";AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,cAAc;AAC5C,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,KAAK,QAAQ,mCAAmC;AACzD,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,qCAAqC;AAClF,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,IAAIC,WAAW,QAAQ,cAAc;AACvD,SAASC,eAAe,EAAEC,aAAa,QAAQ,kBAAkB;AAEjE,SAASH,SAASA,CAACI,CAAC,EAAEC,CAAC,EAAE;EACrB,OAAQC,CAAC,IAAKL,WAAW,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACtC;AACA,SAASC,QAAQA,CAACH,CAAC,EAAE;EACjB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAOJ,SAAS;EACpB,CAAC,MACI,IAAI,OAAOI,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOV,kBAAkB,CAACU,CAAC,CAAC,GACtBL,YAAY,GACZJ,KAAK,CAACa,IAAI,CAACJ,CAAC,CAAC,GACTN,QAAQ,GACRW,UAAU;EACxB,CAAC,MACI,IAAIC,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,EAAE;IACvB,OAAOQ,QAAQ;EACnB,CAAC,MACI,IAAI,OAAOR,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOT,KAAK,CAACa,IAAI,CAACJ,CAAC,CAAC,GAAGN,QAAQ,GAAGe,SAAS;EAC/C;EACA,OAAOd,YAAY;AACvB;AACA,SAASa,QAAQA,CAACR,CAAC,EAAEC,CAAC,EAAE;EACpB,MAAMS,MAAM,GAAG,CAAC,GAAGV,CAAC,CAAC;EACrB,MAAMW,SAAS,GAAGD,MAAM,CAACE,MAAM;EAC/B,MAAMC,UAAU,GAAGb,CAAC,CAACc,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKb,QAAQ,CAACY,CAAC,CAAC,CAACA,CAAC,EAAEd,CAAC,CAACe,CAAC,CAAC,CAAC,CAAC;EACxD,OAAQd,CAAC,IAAK;IACV,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;MAChCN,MAAM,CAACM,CAAC,CAAC,GAAGH,UAAU,CAACG,CAAC,CAAC,CAACd,CAAC,CAAC;IAChC;IACA,OAAOQ,MAAM;EACjB,CAAC;AACL;AACA,SAASD,SAASA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACrB,MAAMS,MAAM,GAAAO,aAAA,CAAAA,aAAA,KAAQjB,CAAC,GAAKC,CAAC,CAAE;EAC7B,MAAMY,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMK,GAAG,IAAIR,MAAM,EAAE;IACtB,IAAIV,CAAC,CAACkB,GAAG,CAAC,KAAKC,SAAS,IAAIlB,CAAC,CAACiB,GAAG,CAAC,KAAKC,SAAS,EAAE;MAC9CN,UAAU,CAACK,GAAG,CAAC,GAAGf,QAAQ,CAACH,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAClB,CAAC,CAACkB,GAAG,CAAC,EAAEjB,CAAC,CAACiB,GAAG,CAAC,CAAC;IACtD;EACJ;EACA,OAAQH,CAAC,IAAK;IACV,KAAK,MAAMG,GAAG,IAAIL,UAAU,EAAE;MAC1BH,MAAM,CAACQ,GAAG,CAAC,GAAGL,UAAU,CAACK,GAAG,CAAC,CAACH,CAAC,CAAC;IACpC;IACA,OAAOL,MAAM;EACjB,CAAC;AACL;AACA,SAASU,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,QAAQ,GAAG;IAAEjC,KAAK,EAAE,CAAC;IAAEkC,GAAG,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAChD,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,MAAM,CAACK,MAAM,CAACf,MAAM,EAAEI,CAAC,EAAE,EAAE;IAAA,IAAAY,qBAAA;IAC3C,MAAMC,IAAI,GAAGP,MAAM,CAACQ,KAAK,CAACd,CAAC,CAAC;IAC5B,MAAMe,WAAW,GAAGV,MAAM,CAACW,OAAO,CAACH,IAAI,CAAC,CAACL,QAAQ,CAACK,IAAI,CAAC,CAAC;IACxD,MAAMI,WAAW,IAAAL,qBAAA,GAAGP,MAAM,CAACM,MAAM,CAACI,WAAW,CAAC,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IACnDL,aAAa,CAACP,CAAC,CAAC,GAAGiB,WAAW;IAC9BT,QAAQ,CAACK,IAAI,CAAC,EAAE;EACpB;EACA,OAAON,aAAa;AACxB;AACA,MAAMlB,UAAU,GAAGA,CAACgB,MAAM,EAAEC,MAAM,KAAK;EACnC,MAAMY,QAAQ,GAAG1C,OAAO,CAAC2C,iBAAiB,CAACb,MAAM,CAAC;EAClD,MAAMc,WAAW,GAAG3C,mBAAmB,CAAC4B,MAAM,CAAC;EAC/C,MAAMgB,WAAW,GAAG5C,mBAAmB,CAAC6B,MAAM,CAAC;EAC/C,MAAMgB,cAAc,GAAGF,WAAW,CAACJ,OAAO,CAACP,GAAG,CAACb,MAAM,KAAKyB,WAAW,CAACL,OAAO,CAACP,GAAG,CAACb,MAAM,IACpFwB,WAAW,CAACJ,OAAO,CAACzC,KAAK,CAACqB,MAAM,KAAKyB,WAAW,CAACL,OAAO,CAACzC,KAAK,CAACqB,MAAM,IACrEwB,WAAW,CAACJ,OAAO,CAACN,MAAM,CAACd,MAAM,IAAIyB,WAAW,CAACL,OAAO,CAACN,MAAM,CAACd,MAAM;EAC1E,IAAI0B,cAAc,EAAE;IAChB,IAAKxC,eAAe,CAACyC,GAAG,CAAClB,MAAM,CAAC,IAC5B,CAACgB,WAAW,CAACV,MAAM,CAACf,MAAM,IACzBd,eAAe,CAACyC,GAAG,CAACjB,MAAM,CAAC,IACxB,CAACc,WAAW,CAACT,MAAM,CAACf,MAAO,EAAE;MACjC,OAAOb,aAAa,CAACsB,MAAM,EAAEC,MAAM,CAAC;IACxC;IACA,OAAOlC,IAAI,CAACoB,QAAQ,CAACY,UAAU,CAACgB,WAAW,EAAEC,WAAW,CAAC,EAAEA,WAAW,CAACV,MAAM,CAAC,EAAEO,QAAQ,CAAC;EAC7F,CAAC,MACI;IACD7C,OAAO,CAAC,IAAI,qBAAAmD,MAAA,CAAqBnB,MAAM,aAAAmB,MAAA,CAAUlB,MAAM,6KAA0K,CAAC;IAClO,OAAO3B,YAAY,CAAC0B,MAAM,EAAEC,MAAM,CAAC;EACvC;AACJ,CAAC;AAED,SAASnB,QAAQ,EAAEK,QAAQ,EAAEH,UAAU,EAAEI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}