{"ast": null, "code": "import React from'react';import{<PERSON>rowserRouter as Router,Routes,Route,Link}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import Navigation from'./components/Navigation';import HomePage from'./pages/HomePage';import ProductsPage from'./pages/ProductsPage';import DigitalProductsPage from'./pages/DigitalProductsPage';import AboutPage from'./pages/AboutPage';import ContactPage from'./pages/ContactPage';import MultiLanguageSupport from'./components/MultiLanguageSupport';import EmailNotifications from'./components/EmailNotifications';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",children:[/*#__PURE__*/_jsx(Navigation,{}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(HomePage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/products\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(ProductsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/digital-products\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(DigitalProductsPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/about\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(AboutPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/contact\",element:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:/*#__PURE__*/_jsx(ContactPage,{})})})]})}),/*#__PURE__*/_jsx(\"footer\",{className:\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"col-span-1 md:col-span-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6 text-white\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-2xl font-bold\",children:\"ShopHub\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 mb-4 max-w-md\",children:\"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-4\",children:[/*#__PURE__*/_jsx(MultiLanguageSupport,{}),/*#__PURE__*/_jsx(EmailNotifications,{})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Quick Links\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/products\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Products\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/digital-products\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Digital Products\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/about\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"About Us\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(Link,{to:\"/contact\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Contact\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-4\",children:\"Customer Service\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Help Center\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Returns\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Shipping Info\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-300 hover:text-light-orange-400 transition-colors\",children:\"Track Order\"})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm\",children:\"\\xA9 2024 ShopHub. All rights reserved.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-6 mt-4 md:mt-0\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Privacy Policy\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Terms of Service\"}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",children:\"Cookie Policy\"})]})]})]})})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "MultiLanguageSupport", "EmailNotifications", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "href"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n        <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</a></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</a>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,KAAQ,kBAAkB,CAC/E,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,oBAAoB,KAAM,mCAAmC,CACpE,MAAO,CAAAC,kBAAkB,KAAM,iCAAiC,CAChE,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACf,MAAM,EAAAmB,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3EJ,IAAA,CAACT,UAAU,GAAE,CAAC,cAEdS,IAAA,CAACV,eAAe,EAACgB,IAAI,CAAC,MAAM,CAAAF,QAAA,cAC1BF,KAAA,CAAChB,MAAM,EAAAkB,QAAA,eACLJ,IAAA,CAACb,KAAK,EAACoB,IAAI,CAAC,GAAG,CAACC,OAAO,cACrBR,IAAA,CAACX,MAAM,CAACoB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACR,QAAQ,GAAE,CAAC,CACF,CACb,CAAE,CAAC,cACJQ,IAAA,CAACb,KAAK,EAACoB,IAAI,CAAC,WAAW,CAACC,OAAO,cAC7BR,IAAA,CAACX,MAAM,CAACoB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACP,YAAY,GAAE,CAAC,CACN,CACb,CAAE,CAAC,cACJO,IAAA,CAACb,KAAK,EAACoB,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrCR,IAAA,CAACX,MAAM,CAACoB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACN,mBAAmB,GAAE,CAAC,CACb,CACb,CAAE,CAAC,cACJM,IAAA,CAACb,KAAK,EAACoB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BR,IAAA,CAACX,MAAM,CAACoB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACL,SAAS,GAAE,CAAC,CACH,CACb,CAAE,CAAC,cACJK,IAAA,CAACb,KAAK,EAACoB,IAAI,CAAC,UAAU,CAACC,OAAO,cAC5BR,IAAA,CAACX,MAAM,CAACoB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAZ,QAAA,cAE9BJ,IAAA,CAACJ,WAAW,GAAE,CAAC,CACL,CACb,CAAE,CAAC,EACE,CAAC,CACM,CAAC,cAGlBI,IAAA,WAAQK,SAAS,CAAC,uDAAuD,CAAAD,QAAA,cACvEF,KAAA,QAAKG,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3DF,KAAA,QAAKG,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDF,KAAA,QAAKG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvCF,KAAA,QAAKG,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/CJ,IAAA,QAAKK,SAAS,CAAC,oHAAoH,CAAAD,QAAA,cACjIJ,IAAA,QAAKK,SAAS,CAAC,oBAAoB,CAACY,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAf,QAAA,cACvFJ,IAAA,SAAMoB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,4CAA4C,CAAE,CAAC,CACjH,CAAC,CACH,CAAC,cACNvB,IAAA,SAAMK,SAAS,CAAC,oBAAoB,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAChD,CAAC,cACNJ,IAAA,MAAGK,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,0JAG3C,CAAG,CAAC,cACJF,KAAA,QAAKG,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BJ,IAAA,CAACH,oBAAoB,GAAE,CAAC,cACxBG,IAAA,CAACF,kBAAkB,GAAE,CAAC,EACnB,CAAC,EACH,CAAC,cAGNI,KAAA,QAAAE,QAAA,eACEJ,IAAA,OAAIK,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3DF,KAAA,OAAIG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACvBJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACZ,IAAI,EAACoC,EAAE,CAAC,GAAG,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAI,CAAC,cACzGJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACZ,IAAI,EAACoC,EAAE,CAAC,WAAW,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,cACrHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACZ,IAAI,EAACoC,EAAE,CAAC,mBAAmB,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,CAAI,CAAC,cACrIJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACZ,IAAI,EAACoC,EAAE,CAAC,QAAQ,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,CAAI,CAAC,cAClHJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,CAACZ,IAAI,EAACoC,EAAE,CAAC,UAAU,CAACnB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAI,CAAC,EACjH,CAAC,EACF,CAAC,cAGNF,KAAA,QAAAE,QAAA,eACEJ,IAAA,OAAIK,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAChEF,KAAA,OAAIG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACvBJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,aAAW,CAAG,CAAC,CAAI,CAAC,cAC5GJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAAO,CAAG,CAAC,CAAI,CAAC,cACxGJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,eAAa,CAAG,CAAC,CAAI,CAAC,cAC9GJ,IAAA,OAAAI,QAAA,cAAIJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,aAAW,CAAG,CAAC,CAAI,CAAC,EAC1G,CAAC,EACF,CAAC,EACH,CAAC,cAENF,KAAA,QAAKG,SAAS,CAAC,2FAA2F,CAAAD,QAAA,eACxGJ,IAAA,MAAGK,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,yCAErC,CAAG,CAAC,cACJF,KAAA,QAAKG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,gBAAc,CAAG,CAAC,cAC9GJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,kBAAgB,CAAG,CAAC,cAChHJ,IAAA,MAAGyB,IAAI,CAAC,GAAG,CAACpB,SAAS,CAAC,qEAAqE,CAAAD,QAAA,CAAC,eAAa,CAAG,CAAC,EAC1G,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,EACN,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}