import React, { createContext, useContext, useState, useEffect } from 'react';

const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// Mock user data for demonstration
const mockUsers = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    password: 'password123', // In real app, this would be hashed
    firstName: '<PERSON>',
    lastName: 'Doe',
    phone: '+****************',
    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    addresses: [
      {
        id: 'addr-001',
        type: 'shipping',
        isDefault: true,
        firstName: 'John',
        lastName: 'Doe',
        address: '123 Main Street',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'United States'
      }
    ],
    paymentMethods: [
      {
        id: 'payment-001',
        type: 'credit_card',
        isDefault: true,
        last4: '4242',
        brand: 'Visa',
        expiryMonth: 12,
        expiryYear: 2025
      }
    ],
    preferences: {
      emailNotifications: true,
      smsNotifications: false,
      marketingEmails: true,
      currency: 'USD',
      language: 'en'
    },
    orderHistory: [],
    wishlist: [],
    createdAt: '2024-01-15T10:30:00Z',
    lastLogin: '2024-01-20T14:22:00Z',
    isVerified: true
  }
];

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check for existing session on mount
  useEffect(() => {
    const checkAuthStatus = () => {
      const token = localStorage.getItem('authToken');
      const userData = localStorage.getItem('userData');
      
      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsAuthenticated(true);
        } catch (error) {
          console.error('Error parsing user data:', error);
          localStorage.removeItem('authToken');
          localStorage.removeItem('userData');
        }
      }
      setIsLoading(false);
    };

    checkAuthStatus();
  }, []);

  const login = async (email, password, rememberMe = false) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find user in mock data
      const foundUser = mockUsers.find(u => u.email === email && u.password === password);
      
      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      // Generate mock token
      const token = `mock_token_${Date.now()}`;
      
      // Store auth data
      if (rememberMe) {
        localStorage.setItem('authToken', token);
        localStorage.setItem('userData', JSON.stringify(foundUser));
      } else {
        sessionStorage.setItem('authToken', token);
        sessionStorage.setItem('userData', JSON.stringify(foundUser));
      }

      setUser(foundUser);
      setIsAuthenticated(true);
      setIsLoading(false);
      
      return { success: true, user: foundUser };
    } catch (error) {
      setIsLoading(false);
      return { success: false, error: error.message };
    }
  };

  const register = async (userData) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser = {
        id: `user-${Date.now()}`,
        email: userData.email,
        password: userData.password,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone || '',
        profilePicture: '',
        addresses: [],
        paymentMethods: [],
        preferences: {
          emailNotifications: true,
          smsNotifications: false,
          marketingEmails: userData.marketingEmails || false,
          currency: 'USD',
          language: 'en'
        },
        orderHistory: [],
        wishlist: [],
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
        isVerified: false
      };

      // Add to mock users (in real app, this would be sent to server)
      mockUsers.push(newUser);

      setIsLoading(false);
      return { success: true, message: 'Account created successfully. Please check your email for verification.' };
    } catch (error) {
      setIsLoading(false);
      return { success: false, error: error.message };
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('userData');
    
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateProfile = async (updatedData) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const updatedUser = { ...user, ...updatedData };
      
      // Update stored data
      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      if (token) {
        if (localStorage.getItem('authToken')) {
          localStorage.setItem('userData', JSON.stringify(updatedUser));
        } else {
          sessionStorage.setItem('userData', JSON.stringify(updatedUser));
        }
      }

      setUser(updatedUser);
      setIsLoading(false);
      
      return { success: true, user: updatedUser };
    } catch (error) {
      setIsLoading(false);
      return { success: false, error: error.message };
    }
  };

  const resetPassword = async (email) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user exists
      const foundUser = mockUsers.find(u => u.email === email);
      if (!foundUser) {
        throw new Error('No account found with this email address');
      }

      setIsLoading(false);
      return { success: true, message: 'Password reset link sent to your email' };
    } catch (error) {
      setIsLoading(false);
      return { success: false, error: error.message };
    }
  };

  const addToWishlist = (productId) => {
    if (!user) return;
    
    const updatedWishlist = [...user.wishlist];
    if (!updatedWishlist.includes(productId)) {
      updatedWishlist.push(productId);
      updateProfile({ wishlist: updatedWishlist });
    }
  };

  const removeFromWishlist = (productId) => {
    if (!user) return;
    
    const updatedWishlist = user.wishlist.filter(id => id !== productId);
    updateProfile({ wishlist: updatedWishlist });
  };

  const isInWishlist = (productId) => {
    return user?.wishlist?.includes(productId) || false;
  };

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    addToWishlist,
    removeFromWishlist,
    isInWishlist
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export default UserContext;
