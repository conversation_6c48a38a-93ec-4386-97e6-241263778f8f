{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{useInView}from'react-intersection-observer';import{Link}from'react-router-dom';import{ShoppingBagIcon,StarIcon,TruckIcon,ShieldCheckIcon,HeartIcon,SparklesIcon,ArrowDownTrayIcon as CloudDownloadIcon,ComputerDesktopIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid}from'@heroicons/react/24/solid';import{getFeaturedProducts,getDigitalProducts}from'../data/products';import{useCart}from'../components/ShoppingCart';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomePage=()=>{const[heroRef,heroInView]=useInView({threshold:0.1,triggerOnce:true});const[featuresRef,featuresInView]=useInView({threshold:0.1,triggerOnce:true});const[productsRef,productsInView]=useInView({threshold:0.1,triggerOnce:true});const{addToCart}=useCart();const featuredProducts=getFeaturedProducts().slice(0,3);const digitalProducts=getDigitalProducts().slice(0,3);const handleAddToCart=product=>{addToCart(product);toast.success(\"\".concat(product.name,\" added to cart!\"),{duration:3000,position:'top-right'});};const testimonials=[{id:1,name:'Sarah Johnson',role:'Verified Customer',content:'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',rating:5,avatar:'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'},{id:2,name:'Mike Chen',role:'Tech Enthusiast',content:'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',rating:5,avatar:'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'},{id:3,name:'Emily Davis',role:'Regular Shopper',content:'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',rating:5,avatar:'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'}];const features=[{icon:TruckIcon,title:'Free Shipping',description:'Free delivery on orders over $50'},{icon:ShieldCheckIcon,title:'Secure Payment',description:'100% secure payment processing'},{icon:HeartIcon,title:'24/7 Support',description:'Round-the-clock customer service'},{icon:SparklesIcon,title:'Premium Quality',description:'Only the finest products'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsxs(motion.section,{ref:heroRef,initial:{opacity:0},animate:heroInView?{opacity:1}:{},transition:{duration:1},className:\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{x:-100,opacity:0},animate:heroInView?{x:0,opacity:1}:{},transition:{duration:0.8,delay:0.2},children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\",children:[\"Discover Amazing\",/*#__PURE__*/_jsx(\"span\",{className:\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\",children:\"Products\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-light-orange-100 mb-8 leading-relaxed\",children:\"Shop the latest trends with unbeatable prices and premium quality. Your perfect shopping experience starts here.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/products\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",children:\"Shop Now\"})}),/*#__PURE__*/_jsx(Link,{to:\"/digital-products\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\",children:\"Digital Products\"})})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{x:100,opacity:0},animate:heroInView?{x:0,opacity:1}:{},transition:{duration:0.8,delay:0.4},className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"relative z-10\",children:/*#__PURE__*/_jsx(\"img\",{src:\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\",alt:\"Shopping Experience\",className:\"rounded-2xl shadow-2xl\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"})]})]})})]}),/*#__PURE__*/_jsx(motion.section,{ref:featuresRef,initial:{opacity:0,y:50},animate:featuresInView?{opacity:1,y:0}:{},transition:{duration:0.8},className:\"py-20 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"Why Choose Us?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"We're committed to providing you with the best shopping experience possible\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:features.map((feature,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},animate:featuresInView?{opacity:1,y:0}:{},transition:{duration:0.6,delay:index*0.1},className:\"text-center group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",children:/*#__PURE__*/_jsx(feature.icon,{className:\"w-10 h-10 text-light-orange-600\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-3\",children:feature.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:feature.description})]},index))})]})}),/*#__PURE__*/_jsx(motion.section,{ref:productsRef,initial:{opacity:0},animate:productsInView?{opacity:1}:{},transition:{duration:0.8},className:\"py-20 bg-gradient-to-br from-light-orange-50 to-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"Featured Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"Discover our handpicked selection of premium products\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",children:featuredProducts.map((product,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},animate:productsInView?{opacity:1,y:0}:{},transition:{duration:0.6,delay:index*0.1},whileHover:{y:-10},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images?product.images[0]:product.image,alt:product.name,className:\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",children:product.badge})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},className:\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",children:/*#__PURE__*/_jsx(HeartIcon,{className:\"w-5 h-5 text-gray-600\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\" reviews)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-green-600 font-semibold\",children:[\"Save $\",(product.originalPrice-product.price).toFixed(2)]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>handleAddToCart(product),className:\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(ShoppingBagIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Add to Cart\"})]})]})]},product.id))})]})}),/*#__PURE__*/_jsx(motion.section,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:0.8},viewport:{once:true},className:\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"Digital Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"Instant access to software, games, and digital content\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-8\",children:digitalProducts.map((product,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:0.6,delay:index*0.1},viewport:{once:true},whileHover:{y:-10},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images[0],alt:product.name,className:\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",children:\"Digital\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-3 h-3 mr-1\"}),\"Instant\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\" reviews)\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-blue-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]})}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>handleAddToCart(product),className:\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Get Instantly\"})]})]})]},product.id))}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mt-12\",children:/*#__PURE__*/_jsx(Link,{to:\"/digital-products\",children:/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",children:[/*#__PURE__*/_jsx(ComputerDesktopIcon,{className:\"w-6 h-6\"}),/*#__PURE__*/_jsx(\"span\",{children:\"View All Digital Products\"})]})})})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"py-20 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",children:\"What Our Customers Say\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-2xl mx-auto\",children:\"Don't just take our word for it - hear from our satisfied customers\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-8\",children:testimonials.map((testimonial,index)=>/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:0.6,delay:index*0.1},className:\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"img\",{src:testimonial.avatar,alt:testimonial.name,className:\"w-12 h-12 rounded-full mr-4\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-gray-900\",children:testimonial.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:testimonial.role})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex mb-4\",children:[...Array(testimonial.rating)].map((_,i)=>/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-5 h-5 text-yellow-400\"},i))}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-700 italic\",children:[\"\\\"\",testimonial.content,\"\\\"\"]})]},testimonial.id))})]})})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "motion", "useInView", "Link", "ShoppingBagIcon", "StarIcon", "TruckIcon", "ShieldCheckIcon", "HeartIcon", "SparklesIcon", "ArrowDownTrayIcon", "CloudDownloadIcon", "ComputerDesktopIcon", "StarIconSolid", "getFeaturedProducts", "getDigitalProducts", "useCart", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "HomePage", "hero<PERSON><PERSON>", "hero<PERSON>n<PERSON>iew", "threshold", "triggerOnce", "featuresRef", "featuresInView", "productsRef", "productsInView", "addToCart", "featuredProducts", "slice", "digitalProducts", "handleAddToCart", "product", "success", "concat", "name", "duration", "position", "testimonials", "id", "role", "content", "rating", "avatar", "features", "icon", "title", "description", "className", "children", "section", "ref", "initial", "opacity", "animate", "transition", "div", "x", "delay", "to", "button", "whileHover", "scale", "whileTap", "src", "alt", "y", "map", "feature", "index", "images", "image", "badge", "Array", "_", "i", "Math", "floor", "reviews", "price", "originalPrice", "toFixed", "onClick", "whileInView", "viewport", "once", "testimonial"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Link } from 'react-router-dom';\nimport {\n  ShoppingBagIcon,\n  StarIcon,\n  TruckIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  SparklesIcon,\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getFeaturedProducts, getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst HomePage = () => {\n  const [heroRef, heroInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [featuresRef, featuresInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const [productsRef, productsInView] = useInView({ threshold: 0.1, triggerOnce: true });\n  const { addToCart } = useCart();\n\n  const featuredProducts = getFeaturedProducts().slice(0, 3);\n  const digitalProducts = getDigitalProducts().slice(0, 3);\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n\n  const testimonials = [\n    {\n      id: 1,\n      name: 'Sarah Johnson',\n      role: 'Verified Customer',\n      content: 'Amazing quality products and lightning-fast delivery! The customer service is exceptional.',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100'\n    },\n    {\n      id: 2,\n      name: 'Mike Chen',\n      role: 'Tech Enthusiast',\n      content: 'Best online shopping experience I\\'ve ever had. The product recommendations are spot on!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'\n    },\n    {\n      id: 3,\n      name: 'Emily Davis',\n      role: 'Regular Shopper',\n      content: 'Love the variety and quality. The website is so easy to navigate and the deals are incredible!',\n      rating: 5,\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100'\n    }\n  ];\n\n  const features = [\n    {\n      icon: TruckIcon,\n      title: 'Free Shipping',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: 'Secure Payment',\n      description: '100% secure payment processing'\n    },\n    {\n      icon: HeartIcon,\n      title: '24/7 Support',\n      description: 'Round-the-clock customer service'\n    },\n    {\n      icon: SparklesIcon,\n      title: 'Premium Quality',\n      description: 'Only the finest products'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      <Toaster position=\"top-right\" />\n      {/* Hero Section */}\n      <motion.section\n        ref={heroRef}\n        initial={{ opacity: 0 }}\n        animate={heroInView ? { opacity: 1 } : {}}\n        transition={{ duration: 1 }}\n        className=\"relative bg-gradient-to-br from-light-orange-500 via-light-orange-600 to-light-orange-700 overflow-hidden\"\n      >\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce\"></div>\n          <div className=\"absolute bottom-20 left-1/4 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-ping\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ x: -100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <h1 className=\"text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n                Discover Amazing\n                <span className=\"block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n                  Products\n                </span>\n              </h1>\n              <p className=\"text-xl text-light-orange-100 mb-8 leading-relaxed\">\n                Shop the latest trends with unbeatable prices and premium quality. \n                Your perfect shopping experience starts here.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link to=\"/products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-white text-light-orange-600 px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                  >\n                    Shop Now\n                  </motion.button>\n                </Link>\n                <Link to=\"/digital-products\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-light-orange-600 transition-all duration-300\"\n                  >\n                    Digital Products\n                  </motion.button>\n                </Link>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ x: 100, opacity: 0 }}\n              animate={heroInView ? { x: 0, opacity: 1 } : {}}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600\"\n                  alt=\"Shopping Experience\"\n                  className=\"rounded-2xl shadow-2xl\"\n                />\n              </div>\n              <div className=\"absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-yellow-400 to-orange-400 rounded-2xl opacity-20\"></div>\n            </motion.div>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section\n        ref={featuresRef}\n        initial={{ opacity: 0, y: 50 }}\n        animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose Us?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              We're committed to providing you with the best shopping experience possible\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                animate={featuresInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-center group\"\n              >\n                <div className=\"bg-gradient-to-br from-light-orange-100 to-light-orange-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <feature.icon className=\"w-10 h-10 text-light-orange-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Featured Products Section */}\n      <motion.section\n        ref={productsRef}\n        initial={{ opacity: 0 }}\n        animate={productsInView ? { opacity: 1 } : {}}\n        transition={{ duration: 0.8 }}\n        className=\"py-20 bg-gradient-to-br from-light-orange-50 to-white\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of premium products\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                animate={productsInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images ? product.images[0] : product.image}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-light-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      {product.badge}\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n                    >\n                      <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-light-orange-600\">\n                        ${product.price}\n                      </span>\n                      <span className=\"text-lg text-gray-500 line-through\">\n                        ${product.originalPrice}\n                      </span>\n                    </div>\n                    <span className=\"text-sm text-green-600 font-semibold\">\n                      Save ${(product.originalPrice - product.price).toFixed(2)}\n                    </span>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={() => handleAddToCart(product)}\n                    className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <ShoppingBagIcon className=\"w-5 h-5\" />\n                    <span>Add to Cart</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Digital Products Section */}\n      <motion.section\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              Digital Products\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Instant access to software, games, and digital content\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {digitalProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10 }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n              >\n                <div className=\"relative\">\n                  <img\n                    src={product.images[0]}\n                    alt={product.name}\n                    className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                      Digital\n                    </span>\n                  </div>\n                  <div className=\"absolute top-4 right-4\">\n                    <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n                      <CloudDownloadIcon className=\"w-3 h-3 mr-1\" />\n                      Instant\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{product.name}</h3>\n\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex\">\n                      {[...Array(5)].map((_, i) => (\n                        i < Math.floor(product.rating) ? (\n                          <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                        ) : (\n                          <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                        )\n                      ))}\n                    </div>\n                    <span className=\"text-sm text-gray-600 ml-2\">\n                      {product.rating} ({product.reviews} reviews)\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl font-bold text-blue-600\">\n                        ${product.price}\n                      </span>\n                      {product.originalPrice && product.originalPrice > product.price && (\n                        <span className=\"text-lg text-gray-500 line-through\">\n                          ${product.originalPrice}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <motion.button\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    onClick={() => handleAddToCart(product)}\n                    className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n                  >\n                    <CloudDownloadIcon className=\"w-5 h-5\" />\n                    <span>Get Instantly</span>\n                  </motion.button>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link to=\"/digital-products\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\"\n              >\n                <ComputerDesktopIcon className=\"w-6 h-6\" />\n                <span>View All Digital Products</span>\n              </motion.button>\n            </Link>\n          </div>\n        </div>\n      </motion.section>\n\n      {/* Testimonials Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n              What Our Customers Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Don't just take our word for it - hear from our satisfied customers\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"bg-gradient-to-br from-light-orange-50 to-white p-8 rounded-2xl shadow-lg\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <img\n                    src={testimonial.avatar}\n                    alt={testimonial.name}\n                    className=\"w-12 h-12 rounded-full mr-4\"\n                  />\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{testimonial.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex mb-4\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <StarIconSolid key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-700 italic\">\"{testimonial.content}\"</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,SAAS,KAAQ,6BAA6B,CACvD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,eAAe,CACfC,QAAQ,CACRC,SAAS,CACTC,eAAe,CACfC,SAAS,CACTC,YAAY,CACZC,iBAAiB,GAAI,CAAAC,iBAAiB,CACtCC,mBAAmB,KACd,6BAA6B,CACpC,OAASP,QAAQ,GAAI,CAAAQ,aAAa,KAAQ,2BAA2B,CACrE,OAASC,mBAAmB,CAAEC,kBAAkB,KAAQ,kBAAkB,CAC1E,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGvB,SAAS,CAAC,CAAEwB,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CAC9E,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3B,SAAS,CAAC,CAAEwB,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CACtF,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG7B,SAAS,CAAC,CAAEwB,SAAS,CAAE,GAAG,CAAEC,WAAW,CAAE,IAAK,CAAC,CAAC,CACtF,KAAM,CAAEK,SAAU,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAE/B,KAAM,CAAAiB,gBAAgB,CAAGnB,mBAAmB,CAAC,CAAC,CAACoB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAC1D,KAAM,CAAAC,eAAe,CAAGpB,kBAAkB,CAAC,CAAC,CAACmB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAExD,KAAM,CAAAE,eAAe,CAAIC,OAAO,EAAK,CACnCL,SAAS,CAACK,OAAO,CAAC,CAClBpB,KAAK,CAACqB,OAAO,IAAAC,MAAA,CAAIF,OAAO,CAACG,IAAI,oBAAmB,CAC9CC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,WACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnB,CACEC,EAAE,CAAE,CAAC,CACLJ,IAAI,CAAE,eAAe,CACrBK,IAAI,CAAE,mBAAmB,CACzBC,OAAO,CAAE,4FAA4F,CACrGC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,oEACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLJ,IAAI,CAAE,WAAW,CACjBK,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,0FAA0F,CACnGC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,oEACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLJ,IAAI,CAAE,aAAa,CACnBK,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,gGAAgG,CACzGC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,oEACV,CAAC,CACF,CAED,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEC,IAAI,CAAE5C,SAAS,CACf6C,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,kCACf,CAAC,CACD,CACEF,IAAI,CAAE3C,eAAe,CACrB4C,KAAK,CAAE,gBAAgB,CACvBC,WAAW,CAAE,gCACf,CAAC,CACD,CACEF,IAAI,CAAE1C,SAAS,CACf2C,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,kCACf,CAAC,CACD,CACEF,IAAI,CAAEzC,YAAY,CAClB0C,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,0BACf,CAAC,CACF,CAED,mBACE9B,KAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlC,IAAA,CAACF,OAAO,EAACwB,QAAQ,CAAC,WAAW,CAAE,CAAC,cAEhCpB,KAAA,CAACrB,MAAM,CAACsD,OAAO,EACbC,GAAG,CAAEhC,OAAQ,CACbiC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAElC,UAAU,CAAG,CAAEiC,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC1CE,UAAU,CAAE,CAAEnB,QAAQ,CAAE,CAAE,CAAE,CAC5BY,SAAS,CAAC,2GAA2G,CAAAC,QAAA,eAGrHhC,KAAA,QAAK+B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BlC,IAAA,QAAKiC,SAAS,CAAC,qFAAqF,CAAM,CAAC,cAC3GjC,IAAA,QAAKiC,SAAS,CAAC,uFAAuF,CAAM,CAAC,cAC7GjC,IAAA,QAAKiC,SAAS,CAAC,wFAAwF,CAAM,CAAC,EAC3G,CAAC,cAENjC,IAAA,QAAKiC,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAC7EhC,KAAA,QAAK+B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEhC,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EACTJ,OAAO,CAAE,CAAEK,CAAC,CAAE,CAAC,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CACjCC,OAAO,CAAElC,UAAU,CAAG,CAAEqC,CAAC,CAAE,CAAC,CAAEJ,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAChDE,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAE,GAAI,CAAE,CAAAT,QAAA,eAE1ChC,KAAA,OAAI+B,SAAS,CAAC,8DAA8D,CAAAC,QAAA,EAAC,kBAE3E,cAAAlC,IAAA,SAAMiC,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAAC,UAErG,CAAM,CAAC,EACL,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,kHAGlE,CAAG,CAAC,cACJhC,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9ClC,IAAA,CAACjB,IAAI,EAAC6D,EAAE,CAAC,WAAW,CAAAV,QAAA,cAClBlC,IAAA,CAACnB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1Bd,SAAS,CAAC,mIAAmI,CAAAC,QAAA,CAC9I,UAED,CAAe,CAAC,CACZ,CAAC,cACPlC,IAAA,CAACjB,IAAI,EAAC6D,EAAE,CAAC,mBAAmB,CAAAV,QAAA,cAC1BlC,IAAA,CAACnB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1Bd,SAAS,CAAC,sJAAsJ,CAAAC,QAAA,CACjK,kBAED,CAAe,CAAC,CACZ,CAAC,EACJ,CAAC,EACI,CAAC,cAEbhC,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EACTJ,OAAO,CAAE,CAAEK,CAAC,CAAE,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CAChCC,OAAO,CAAElC,UAAU,CAAG,CAAEqC,CAAC,CAAE,CAAC,CAAEJ,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAChDE,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAE,GAAI,CAAE,CAC1CV,SAAS,CAAC,UAAU,CAAAC,QAAA,eAEpBlC,IAAA,QAAKiC,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BlC,IAAA,QACEiD,GAAG,CAAC,oEAAoE,CACxEC,GAAG,CAAC,qBAAqB,CACzBjB,SAAS,CAAC,wBAAwB,CACnC,CAAC,CACC,CAAC,cACNjC,IAAA,QAAKiC,SAAS,CAAC,+GAA+G,CAAM,CAAC,EAC3H,CAAC,EACV,CAAC,CACH,CAAC,EACQ,CAAC,cAGjBjC,IAAA,CAACnB,MAAM,CAACsD,OAAO,EACbC,GAAG,CAAE5B,WAAY,CACjB6B,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BZ,OAAO,CAAE9B,cAAc,CAAG,CAAE6B,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CACpDX,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAI,CAAE,CAC9BY,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAE1BhC,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhC,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,OAAIiC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,gBAElE,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,6EAEvD,CAAG,CAAC,EACD,CAAC,cAENlC,IAAA,QAAKiC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEL,QAAQ,CAACuB,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3BpD,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EAETJ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BZ,OAAO,CAAE9B,cAAc,CAAG,CAAE6B,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CACpDX,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDrB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7BlC,IAAA,QAAKiC,SAAS,CAAC,0LAA0L,CAAAC,QAAA,cACvMlC,IAAA,CAACqD,OAAO,CAACvB,IAAI,EAACG,SAAS,CAAC,iCAAiC,CAAE,CAAC,CACzD,CAAC,cACNjC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEmB,OAAO,CAACtB,KAAK,CAAK,CAAC,cAC7E/B,IAAA,MAAGiC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEmB,OAAO,CAACrB,WAAW,CAAI,CAAC,GAVjDsB,KAWK,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBtD,IAAA,CAACnB,MAAM,CAACsD,OAAO,EACbC,GAAG,CAAE1B,WAAY,CACjB2B,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE5B,cAAc,CAAG,CAAE2B,OAAO,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CAC9CE,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAI,CAAE,CAC9BY,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cAEjEhC,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhC,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,OAAIiC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,mBAElE,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,uDAEvD,CAAG,CAAC,EACD,CAAC,cAENlC,IAAA,QAAKiC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClErB,gBAAgB,CAACuC,GAAG,CAAC,CAACnC,OAAO,CAAEqC,KAAK,gBACnCpD,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EAETJ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BZ,OAAO,CAAE5B,cAAc,CAAG,CAAE2B,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAC,CAAG,CAAC,CAAE,CACpDX,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDR,UAAU,CAAE,CAAEK,CAAC,CAAE,CAAC,EAAG,CAAE,CACvBlB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,eAE/EhC,KAAA,QAAK+B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBlC,IAAA,QACEiD,GAAG,CAAEhC,OAAO,CAACsC,MAAM,CAAGtC,OAAO,CAACsC,MAAM,CAAC,CAAC,CAAC,CAAGtC,OAAO,CAACuC,KAAM,CACxDN,GAAG,CAAEjC,OAAO,CAACG,IAAK,CAClBa,SAAS,CAAC,kFAAkF,CAC7F,CAAC,cACFjC,IAAA,QAAKiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpClC,IAAA,SAAMiC,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CAC1FjB,OAAO,CAACwC,KAAK,CACV,CAAC,CACJ,CAAC,cACNzD,IAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrClC,IAAA,CAACnB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BC,QAAQ,CAAE,CAAED,KAAK,CAAE,GAAI,CAAE,CACzBd,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAE7DlC,IAAA,CAACZ,SAAS,EAAC6C,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAClC,CAAC,CACb,CAAC,EACH,CAAC,cAEN/B,KAAA,QAAK+B,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBlC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEjB,OAAO,CAACG,IAAI,CAAK,CAAC,cAE5ElB,KAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClC,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGwB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAAC7C,OAAO,CAACU,MAAM,CAAC,cAC5B3B,IAAA,CAACP,aAAa,EAASwC,SAAS,CAAC,yBAAyB,EAAtC2B,CAAwC,CAAC,cAE7D5D,IAAA,CAACf,QAAQ,EAASgD,SAAS,CAAC,uBAAuB,EAApC2B,CAAsC,CAExD,CAAC,CACC,CAAC,cACN1D,KAAA,SAAM+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCjB,OAAO,CAACU,MAAM,CAAC,IAAE,CAACV,OAAO,CAAC8C,OAAO,CAAC,WACrC,EAAM,CAAC,EACJ,CAAC,cAEN7D,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhC,KAAA,QAAK+B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ChC,KAAA,SAAM+B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,GACxD,CAACjB,OAAO,CAAC+C,KAAK,EACX,CAAC,cACP9D,KAAA,SAAM+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACjB,OAAO,CAACgD,aAAa,EACnB,CAAC,EACJ,CAAC,cACN/D,KAAA,SAAM+B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,QAC/C,CAAC,CAACjB,OAAO,CAACgD,aAAa,CAAGhD,OAAO,CAAC+C,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC,EACrD,CAAC,EACJ,CAAC,cAENhE,KAAA,CAACrB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BoB,OAAO,CAAEA,CAAA,GAAMnD,eAAe,CAACC,OAAO,CAAE,CACxCgB,SAAS,CAAC,yOAAyO,CAAAC,QAAA,eAEnPlC,IAAA,CAAChB,eAAe,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,cACvCjC,IAAA,SAAAkC,QAAA,CAAM,aAAW,CAAM,CAAC,EACX,CAAC,EACb,CAAC,GAtEDjB,OAAO,CAACO,EAuEH,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBxB,IAAA,CAACnB,MAAM,CAACsD,OAAO,EACbE,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxB8B,WAAW,CAAE,CAAE9B,OAAO,CAAE,CAAE,CAAE,CAC5BE,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAI,CAAE,CAC9BgD,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAE,CACzBrC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cAE7DhC,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhC,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,OAAIiC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,kBAElE,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,wDAEvD,CAAG,CAAC,EACD,CAAC,cAENlC,IAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDnB,eAAe,CAACqC,GAAG,CAAC,CAACnC,OAAO,CAAEqC,KAAK,gBAClCpD,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EAETJ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BiB,WAAW,CAAE,CAAE9B,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAE,CAClCX,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDe,QAAQ,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAE,CACzBxB,UAAU,CAAE,CAAEK,CAAC,CAAE,CAAC,EAAG,CAAE,CACvBlB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,eAE/EhC,KAAA,QAAK+B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBlC,IAAA,QACEiD,GAAG,CAAEhC,OAAO,CAACsC,MAAM,CAAC,CAAC,CAAE,CACvBL,GAAG,CAAEjC,OAAO,CAACG,IAAK,CAClBa,SAAS,CAAC,kFAAkF,CAC7F,CAAC,cACFjC,IAAA,QAAKiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpClC,IAAA,SAAMiC,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAAC,SAEtF,CAAM,CAAC,CACJ,CAAC,cACNlC,IAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrChC,KAAA,SAAM+B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eACjGlC,IAAA,CAACT,iBAAiB,EAAC0C,SAAS,CAAC,cAAc,CAAE,CAAC,UAEhD,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,cAEN/B,KAAA,QAAK+B,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBlC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEjB,OAAO,CAACG,IAAI,CAAK,CAAC,cAE5ElB,KAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClC,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGwB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,GAAG,CAAC,CAACO,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAAC7C,OAAO,CAACU,MAAM,CAAC,cAC5B3B,IAAA,CAACP,aAAa,EAASwC,SAAS,CAAC,yBAAyB,EAAtC2B,CAAwC,CAAC,cAE7D5D,IAAA,CAACf,QAAQ,EAASgD,SAAS,CAAC,uBAAuB,EAApC2B,CAAsC,CAExD,CAAC,CACC,CAAC,cACN1D,KAAA,SAAM+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCjB,OAAO,CAACU,MAAM,CAAC,IAAE,CAACV,OAAO,CAAC8C,OAAO,CAAC,WACrC,EAAM,CAAC,EACJ,CAAC,cAEN/D,IAAA,QAAKiC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDhC,KAAA,QAAK+B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ChC,KAAA,SAAM+B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,GAChD,CAACjB,OAAO,CAAC+C,KAAK,EACX,CAAC,CACN/C,OAAO,CAACgD,aAAa,EAAIhD,OAAO,CAACgD,aAAa,CAAGhD,OAAO,CAAC+C,KAAK,eAC7D9D,KAAA,SAAM+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACjB,OAAO,CAACgD,aAAa,EACnB,CACP,EACE,CAAC,CACH,CAAC,cAEN/D,KAAA,CAACrB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BoB,OAAO,CAAEA,CAAA,GAAMnD,eAAe,CAACC,OAAO,CAAE,CACxCgB,SAAS,CAAC,yMAAyM,CAAAC,QAAA,eAEnNlC,IAAA,CAACT,iBAAiB,EAAC0C,SAAS,CAAC,SAAS,CAAE,CAAC,cACzCjC,IAAA,SAAAkC,QAAA,CAAM,eAAa,CAAM,CAAC,EACb,CAAC,EACb,CAAC,GAnEDjB,OAAO,CAACO,EAoEH,CACb,CAAC,CACC,CAAC,cAENxB,IAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChClC,IAAA,CAACjB,IAAI,EAAC6D,EAAE,CAAC,mBAAmB,CAAAV,QAAA,cAC1BhC,KAAA,CAACrB,MAAM,CAACgE,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1Bd,SAAS,CAAC,gMAAgM,CAAAC,QAAA,eAE1MlC,IAAA,CAACR,mBAAmB,EAACyC,SAAS,CAAC,SAAS,CAAE,CAAC,cAC3CjC,IAAA,SAAAkC,QAAA,CAAM,2BAAyB,CAAM,CAAC,EACzB,CAAC,CACZ,CAAC,CACJ,CAAC,EACH,CAAC,CACQ,CAAC,cAGjBlC,IAAA,YAASiC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cACjChC,KAAA,QAAK+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhC,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,OAAIiC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,wBAElE,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,qEAEvD,CAAG,CAAC,EACD,CAAC,cAENlC,IAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDX,YAAY,CAAC6B,GAAG,CAAC,CAACmB,WAAW,CAAEjB,KAAK,gBACnCpD,KAAA,CAACrB,MAAM,CAAC4D,GAAG,EAETJ,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,EAAG,CAAE,CAC/BiB,WAAW,CAAE,CAAE9B,OAAO,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAE,CAClCX,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEsB,KAAK,CAAEW,KAAK,CAAG,GAAI,CAAE,CAClDrB,SAAS,CAAC,2EAA2E,CAAAC,QAAA,eAErFhC,KAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClC,IAAA,QACEiD,GAAG,CAAEsB,WAAW,CAAC3C,MAAO,CACxBsB,GAAG,CAAEqB,WAAW,CAACnD,IAAK,CACtBa,SAAS,CAAC,6BAA6B,CACxC,CAAC,cACF/B,KAAA,QAAAgC,QAAA,eACElC,IAAA,OAAIiC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEqC,WAAW,CAACnD,IAAI,CAAK,CAAC,cACnEpB,IAAA,MAAGiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEqC,WAAW,CAAC9C,IAAI,CAAI,CAAC,EACxD,CAAC,EACH,CAAC,cAENzB,IAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAC,GAAGwB,KAAK,CAACa,WAAW,CAAC5C,MAAM,CAAC,CAAC,CAACyB,GAAG,CAAC,CAACO,CAAC,CAAEC,CAAC,gBACvC5D,IAAA,CAACP,aAAa,EAASwC,SAAS,CAAC,yBAAyB,EAAtC2B,CAAwC,CAC7D,CAAC,CACC,CAAC,cAEN1D,KAAA,MAAG+B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAAC,IAAC,CAACqC,WAAW,CAAC7C,OAAO,CAAC,IAAC,EAAG,CAAC,GAxB1D6C,WAAW,CAAC/C,EAyBP,CACb,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAArB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}