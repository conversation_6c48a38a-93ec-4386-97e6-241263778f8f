[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "22"}, {"size": 577, "mtime": 1750156789424, "results": "23", "hashOfConfig": "24"}, {"size": 6509, "mtime": 1750162380263, "results": "25", "hashOfConfig": "24"}, {"size": 5003, "mtime": 1750108593800, "results": "26", "hashOfConfig": "24"}, {"size": 5867, "mtime": 1750158880553, "results": "27", "hashOfConfig": "24"}, {"size": 7488, "mtime": 1750158198494, "results": "28", "hashOfConfig": "24"}, {"size": 6535, "mtime": 1750158944275, "results": "29", "hashOfConfig": "24"}, {"size": 225, "mtime": 1750155963978, "results": "30", "hashOfConfig": "24"}, {"size": 12437, "mtime": 1750159769866, "results": "31", "hashOfConfig": "24"}, {"size": 10621, "mtime": 1750159108346, "results": "32", "hashOfConfig": "24"}, {"size": 13106, "mtime": 1750159208689, "results": "33", "hashOfConfig": "24"}, {"size": 9203, "mtime": 1750159280862, "results": "34", "hashOfConfig": "24"}, {"size": 237, "mtime": 1750156242633, "results": "35", "hashOfConfig": "24"}, {"size": 8712, "mtime": 1750159406548, "results": "36", "hashOfConfig": "24"}, {"size": 8223, "mtime": 1750159345006, "results": "37", "hashOfConfig": "24"}, {"size": 7010, "mtime": 1750159469458, "results": "38", "hashOfConfig": "24"}, {"size": 10788, "mtime": 1750162085403, "results": "39", "hashOfConfig": "24"}, {"size": 13025, "mtime": 1750160847574, "results": "40", "hashOfConfig": "24"}, {"size": 12661, "mtime": 1750160775265, "results": "41", "hashOfConfig": "24"}, {"size": 22498, "mtime": 1750161878707, "results": "42", "hashOfConfig": "24"}, {"size": 18858, "mtime": 1750162514322, "results": "43", "hashOfConfig": "24"}, {"size": 22928, "mtime": 1750162064634, "results": "44", "hashOfConfig": "24"}, {"size": 12105, "mtime": 1750162475832, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", ["112", "113", "114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js", ["119"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js", ["120"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", ["121", "122"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", ["123"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", ["124", "125", "126"], [], {"ruleId": "127", "severity": 1, "message": "128", "line": 115, "column": 23, "nodeType": "129", "endLine": 115, "endColumn": 107}, {"ruleId": "127", "severity": 1, "message": "128", "line": 116, "column": 23, "nodeType": "129", "endLine": 116, "endColumn": 107}, {"ruleId": "127", "severity": 1, "message": "128", "line": 117, "column": 23, "nodeType": "129", "endLine": 117, "endColumn": 107}, {"ruleId": "127", "severity": 1, "message": "128", "line": 118, "column": 23, "nodeType": "129", "endLine": 118, "endColumn": 107}, {"ruleId": "127", "severity": 1, "message": "128", "line": 128, "column": 17, "nodeType": "129", "endLine": 128, "endColumn": 109}, {"ruleId": "127", "severity": 1, "message": "128", "line": 129, "column": 17, "nodeType": "129", "endLine": 129, "endColumn": 109}, {"ruleId": "127", "severity": 1, "message": "128", "line": 130, "column": 17, "nodeType": "129", "endLine": 130, "endColumn": 109}, {"ruleId": "130", "severity": 1, "message": "131", "line": 5, "column": 7, "nodeType": "132", "messageId": "133", "endLine": 5, "endColumn": 15}, {"ruleId": "130", "severity": 1, "message": "134", "line": 41, "column": 22, "nodeType": "132", "messageId": "133", "endLine": 41, "endColumn": 35}, {"ruleId": "130", "severity": 1, "message": "135", "line": 9, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 9, "endColumn": 12}, {"ruleId": "130", "severity": 1, "message": "136", "line": 10, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 10, "endColumn": 13}, {"ruleId": "130", "severity": 1, "message": "137", "line": 14, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 14, "endColumn": 10}, {"ruleId": "130", "severity": 1, "message": "138", "line": 9, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 9, "endColumn": 18}, {"ruleId": "130", "severity": 1, "message": "139", "line": 11, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 11, "endColumn": 24}, {"ruleId": "130", "severity": 1, "message": "140", "line": 12, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 12, "endColumn": 15}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'setPromotions' is assigned a value but never used.", "'UsersIcon' is defined but never used.", "'TrophyIcon' is defined but never used.", "'TagIcon' is defined but never used.", "'ShoppingBagIcon' is defined but never used.", "'DevicePhoneMobileIcon' is defined but never used.", "'GlobeAltIcon' is defined but never used."]