[{"C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js": "4", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js": "5", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js": "6", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js": "7", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js": "8", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js": "9", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js": "10", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js": "11", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js": "13", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js": "14", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js": "15", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js": "16", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js": "17", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js": "18", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js": "19", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js": "20", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js": "21", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js": "22", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js": "23", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js": "24", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "25", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js": "26", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js": "27", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js": "28", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js": "29", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js": "30", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js": "31", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js": "32", "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js": "33"}, {"size": 577, "mtime": *************, "results": "34", "hashOfConfig": "35"}, {"size": 9018, "mtime": *************, "results": "36", "hashOfConfig": "35"}, {"size": 5003, "mtime": *************, "results": "37", "hashOfConfig": "35"}, {"size": 5867, "mtime": *************, "results": "38", "hashOfConfig": "35"}, {"size": 7488, "mtime": *************, "results": "39", "hashOfConfig": "35"}, {"size": 9872, "mtime": *************, "results": "40", "hashOfConfig": "35"}, {"size": 225, "mtime": *************, "results": "41", "hashOfConfig": "35"}, {"size": 12437, "mtime": *************, "results": "42", "hashOfConfig": "35"}, {"size": 10621, "mtime": *************, "results": "43", "hashOfConfig": "35"}, {"size": 13106, "mtime": 1750159208689, "results": "44", "hashOfConfig": "35"}, {"size": 9203, "mtime": 1750159280862, "results": "45", "hashOfConfig": "35"}, {"size": 237, "mtime": 1750156242633, "results": "46", "hashOfConfig": "35"}, {"size": 8712, "mtime": 1750159406548, "results": "47", "hashOfConfig": "35"}, {"size": 8223, "mtime": 1750159345006, "results": "48", "hashOfConfig": "35"}, {"size": 7010, "mtime": 1750159469458, "results": "49", "hashOfConfig": "35"}, {"size": 13918, "mtime": 1750166667532, "results": "50", "hashOfConfig": "35"}, {"size": 11440, "mtime": 1750164163184, "results": "51", "hashOfConfig": "35"}, {"size": 12667, "mtime": 1750164007467, "results": "52", "hashOfConfig": "35"}, {"size": 23925, "mtime": 1750167258944, "results": "53", "hashOfConfig": "35"}, {"size": 19342, "mtime": 1750163550286, "results": "54", "hashOfConfig": "35"}, {"size": 52765, "mtime": 1750166001060, "results": "55", "hashOfConfig": "35"}, {"size": 12456, "mtime": 1750164967846, "results": "56", "hashOfConfig": "35"}, {"size": 1653, "mtime": 1750164096843, "results": "57", "hashOfConfig": "35"}, {"size": 2903, "mtime": 1750164073874, "results": "58", "hashOfConfig": "35"}, {"size": 15294, "mtime": 1750164732603, "results": "59", "hashOfConfig": "35"}, {"size": 3218, "mtime": 1750164772685, "results": "60", "hashOfConfig": "35"}, {"size": 7547, "mtime": 1750166070393, "results": "61", "hashOfConfig": "35"}, {"size": 9102, "mtime": 1750166125481, "results": "62", "hashOfConfig": "35"}, {"size": 12346, "mtime": 1750166411028, "results": "63", "hashOfConfig": "35"}, {"size": 7212, "mtime": 1750166452419, "results": "64", "hashOfConfig": "35"}, {"size": 13681, "mtime": 1750166786153, "results": "65", "hashOfConfig": "35"}, {"size": 9048, "mtime": 1750166894082, "results": "66", "hashOfConfig": "35"}, {"size": 994, "mtime": 1750166931473, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "79hmpe", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\SearchFilters.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductList.js", ["167"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ShoppingCart.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\UserAccounts.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\OrderHistory.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProductReviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Wishlist.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\EmailNotifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Promotions.js", ["168"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\MultiLanguageSupport.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Navigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\data\\products.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\DigitalProductsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\PlaceholderPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\contexts\\UserContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\LoginPage.js", ["169"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\AccountPage.js", ["170"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\pages\\WishlistPage.js", ["171"], [], "C:\\Users\\<USER>\\Desktop\\My projects\\ecomerce\\digital-ecommerce\\frontend\\src\\components\\ProtectedRoute.js", [], [], {"ruleId": "172", "severity": 1, "message": "173", "line": 5, "column": 7, "nodeType": "174", "messageId": "175", "endLine": 5, "endColumn": 15}, {"ruleId": "172", "severity": 1, "message": "176", "line": 41, "column": 22, "nodeType": "174", "messageId": "175", "endLine": 41, "endColumn": 35}, {"ruleId": "172", "severity": 1, "message": "177", "line": 8, "column": 3, "nodeType": "174", "messageId": "175", "endLine": 8, "endColumn": 17}, {"ruleId": "172", "severity": 1, "message": "178", "line": 5, "column": 3, "nodeType": "174", "messageId": "175", "endLine": 5, "endColumn": 10}, {"ruleId": "172", "severity": 1, "message": "179", "line": 18, "column": 37, "nodeType": "174", "messageId": "175", "endLine": 18, "endColumn": 49}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'setPromotions' is assigned a value but never used.", "'LockClosedIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'isInWishlist' is assigned a value but never used."]