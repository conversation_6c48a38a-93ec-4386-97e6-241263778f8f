import React from 'react';
import { motion } from 'framer-motion';

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  className = '',
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variants = {
    primary: 'bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white hover:from-light-orange-600 hover:to-light-orange-700 focus:ring-light-orange-300 shadow-md hover:shadow-lg',
    secondary: 'bg-white text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-50 focus:ring-light-orange-300 shadow-md hover:shadow-lg',
    outline: 'bg-transparent text-light-orange-600 border-2 border-light-orange-500 hover:bg-light-orange-500 hover:text-white focus:ring-light-orange-300',
    ghost: 'bg-transparent text-light-orange-600 hover:bg-light-orange-100 focus:ring-light-orange-300',
    danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-300 shadow-md hover:shadow-lg',
    success: 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-300 shadow-md hover:shadow-lg',
    digital: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-300 shadow-md hover:shadow-lg'
  };

  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-6 py-4 text-lg'
  };

  const disabledClasses = 'opacity-50 cursor-not-allowed pointer-events-none';
  const fullWidthClass = fullWidth ? 'w-full' : '';

  const buttonClasses = `
    ${baseClasses}
    ${variants[variant]}
    ${sizes[size]}
    ${disabled ? disabledClasses : ''}
    ${fullWidthClass}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const handleClick = (e) => {
    if (!disabled && !loading && onClick) {
      onClick(e);
    }
  };

  const renderIcon = () => {
    if (loading) {
      return (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
      );
    }
    
    if (Icon) {
      return (
        <Icon className={`w-5 h-5 ${iconPosition === 'right' ? 'ml-2' : 'mr-2'}`} />
      );
    }
    
    return null;
  };

  return (
    <motion.button
      whileHover={!disabled ? { scale: 1.02 } : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {iconPosition === 'left' && renderIcon()}
      {loading ? 'Loading...' : children}
      {iconPosition === 'right' && renderIcon()}
    </motion.button>
  );
};

export default Button;
