{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{UserIcon,ArrowRightEndOnRectangleIcon as LoginIcon,ArrowLeftStartOnRectangleIcon as LogoutIcon,CogIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UserAccounts=()=>{const[isLoggedIn,setIsLoggedIn]=useState(false);const[isDropdownOpen,setIsDropdownOpen]=useState(false);const[showLoginModal,setShowLoginModal]=useState(false);const[showRegisterModal,setShowRegisterModal]=useState(false);const[loginForm,setLoginForm]=useState({email:'',password:''});const[registerForm,setRegisterForm]=useState({name:'',email:'',password:'',confirmPassword:''});// Mock user data\nconst user={name:'John Doe',email:'<EMAIL>',avatar:'https://via.placeholder.com/40'};const handleLogin=e=>{e.preventDefault();// Mock login logic\nsetIsLoggedIn(true);setShowLoginModal(false);setLoginForm({email:'',password:''});};const handleRegister=e=>{e.preventDefault();// Mock register logic\nsetIsLoggedIn(true);setShowRegisterModal(false);setRegisterForm({name:'',email:'',password:'',confirmPassword:''});};const handleLogout=()=>{setIsLoggedIn(false);setIsDropdownOpen(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[isLoggedIn?/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsDropdownOpen(!isDropdownOpen),className:\"flex items-center space-x-2 p-2 bg-light-orange-100 text-light-orange-700 rounded-full hover:bg-light-orange-200 transition-colors shadow-md\",children:[/*#__PURE__*/_jsx(\"img\",{src:user.avatar,alt:user.name,className:\"w-8 h-8 rounded-full border-2 border-light-orange-300\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden md:block font-medium\",children:user.name}),/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 9l-7 7-7-7\"})})]}):/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowLoginModal(true),className:\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors shadow-md\",children:[/*#__PURE__*/_jsx(LoginIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Sign In\"})]}),isLoggedIn&&isDropdownOpen&&/*#__PURE__*/_jsxs(\"div\",{className:\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-b border-light-orange-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"img\",{src:user.avatar,alt:user.name,className:\"w-12 h-12 rounded-full border-2 border-light-orange-300\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-light-orange-800\",children:user.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-light-orange-600\",children:user.email})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"py-2\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\",children:[/*#__PURE__*/_jsx(UserIcon,{className:\"w-5 h-5 text-light-orange-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-800\",children:\"Profile\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"w-5 h-5 text-light-orange-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-light-orange-800\",children:\"Settings\"})]}),/*#__PURE__*/_jsx(\"hr\",{className:\"my-2 border-light-orange-100\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleLogout,className:\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors\",children:[/*#__PURE__*/_jsx(LogoutIcon,{className:\"w-5 h-5 text-red-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-red-800\",children:\"Sign Out\"})]})]})]}),showLoginModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-white\",children:\"Sign In\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowLoginModal(false),className:\"text-white hover:text-light-orange-200 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M6 18L18 6M6 6l12 12\"})})})]})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleLogin,className:\"p-6 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:loginForm.email,onChange:e=>setLoginForm(_objectSpread(_objectSpread({},loginForm),{},{email:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Enter your email\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:loginForm.password,onChange:e=>setLoginForm(_objectSpread(_objectSpread({},loginForm),{},{password:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Enter your password\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\",children:\"Sign In\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>{setShowLoginModal(false);setShowRegisterModal(true);},className:\"text-light-orange-600 hover:text-light-orange-700 transition-colors\",children:\"Don't have an account? Sign up\"})})]})]})}),showRegisterModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-white\",children:\"Create Account\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowRegisterModal(false),className:\"text-white hover:text-light-orange-200 transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M6 18L18 6M6 6l12 12\"})})})]})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleRegister,className:\"p-6 space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:registerForm.name,onChange:e=>setRegisterForm(_objectSpread(_objectSpread({},registerForm),{},{name:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Enter your full name\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Email Address\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",value:registerForm.email,onChange:e=>setRegisterForm(_objectSpread(_objectSpread({},registerForm),{},{email:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Enter your email\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:registerForm.password,onChange:e=>setRegisterForm(_objectSpread(_objectSpread({},registerForm),{},{password:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Create a password\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-light-orange-800 mb-2\",children:\"Confirm Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:registerForm.confirmPassword,onChange:e=>setRegisterForm(_objectSpread(_objectSpread({},registerForm),{},{confirmPassword:e.target.value})),className:\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\",placeholder:\"Confirm your password\",required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\",children:\"Create Account\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>{setShowRegisterModal(false);setShowLoginModal(true);},className:\"text-light-orange-600 hover:text-light-orange-700 transition-colors\",children:\"Already have an account? Sign in\"})})]})]})})]});};export default UserAccounts;", "map": {"version": 3, "names": ["React", "useState", "UserIcon", "ArrowRightEndOnRectangleIcon", "LoginIcon", "ArrowLeftStartOnRectangleIcon", "LogoutIcon", "CogIcon", "jsx", "_jsx", "jsxs", "_jsxs", "UserAccounts", "isLoggedIn", "setIsLoggedIn", "isDropdownOpen", "setIsDropdownOpen", "showLoginModal", "setShowLoginModal", "showRegisterModal", "setShowRegisterModal", "loginForm", "setLoginForm", "email", "password", "registerForm", "setRegisterForm", "name", "confirmPassword", "user", "avatar", "handleLogin", "e", "preventDefault", "handleRegister", "handleLogout", "className", "children", "onClick", "src", "alt", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "value", "onChange", "_objectSpread", "target", "placeholder", "required"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/UserAccounts.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { UserIcon, ArrowRightEndOnRectangleIcon as LoginIcon, ArrowLeftStartOnRectangleIcon as LogoutIcon, CogIcon } from '@heroicons/react/24/outline';\r\n\r\nconst UserAccounts = () => {\r\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n  const [showLoginModal, setShowLoginModal] = useState(false);\r\n  const [showRegisterModal, setShowRegisterModal] = useState(false);\r\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\r\n  const [registerForm, setRegisterForm] = useState({\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  });\r\n\r\n  // Mock user data\r\n  const user = {\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    avatar: 'https://via.placeholder.com/40'\r\n  };\r\n\r\n  const handleLogin = (e) => {\r\n    e.preventDefault();\r\n    // Mock login logic\r\n    setIsLoggedIn(true);\r\n    setShowLoginModal(false);\r\n    setLoginForm({ email: '', password: '' });\r\n  };\r\n\r\n  const handleRegister = (e) => {\r\n    e.preventDefault();\r\n    // Mock register logic\r\n    setIsLoggedIn(true);\r\n    setShowRegisterModal(false);\r\n    setRegisterForm({ name: '', email: '', password: '', confirmPassword: '' });\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setIsLoggedIn(false);\r\n    setIsDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* User Button */}\r\n      {isLoggedIn ? (\r\n        <button\r\n          onClick={() => setIsDropdownOpen(!isDropdownOpen)}\r\n          className=\"flex items-center space-x-2 p-2 bg-light-orange-100 text-light-orange-700 rounded-full hover:bg-light-orange-200 transition-colors shadow-md\"\r\n        >\r\n          <img\r\n            src={user.avatar}\r\n            alt={user.name}\r\n            className=\"w-8 h-8 rounded-full border-2 border-light-orange-300\"\r\n          />\r\n          <span className=\"hidden md:block font-medium\">{user.name}</span>\r\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n          </svg>\r\n        </button>\r\n      ) : (\r\n        <button\r\n          onClick={() => setShowLoginModal(true)}\r\n          className=\"flex items-center space-x-2 px-4 py-2 bg-light-orange-500 text-white rounded-lg hover:bg-light-orange-600 transition-colors shadow-md\"\r\n        >\r\n          <LoginIcon className=\"w-5 h-5\" />\r\n          <span>Sign In</span>\r\n        </button>\r\n      )}\r\n\r\n      {/* User Dropdown Menu */}\r\n      {isLoggedIn && isDropdownOpen && (\r\n        <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\">\r\n          <div className=\"p-4 border-b border-light-orange-100\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <img\r\n                src={user.avatar}\r\n                alt={user.name}\r\n                className=\"w-12 h-12 rounded-full border-2 border-light-orange-300\"\r\n              />\r\n              <div>\r\n                <h3 className=\"font-semibold text-light-orange-800\">{user.name}</h3>\r\n                <p className=\"text-sm text-light-orange-600\">{user.email}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"py-2\">\r\n            <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\">\r\n              <UserIcon className=\"w-5 h-5 text-light-orange-600\" />\r\n              <span className=\"text-light-orange-800\">Profile</span>\r\n            </button>\r\n            <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-light-orange-50 transition-colors\">\r\n              <CogIcon className=\"w-5 h-5 text-light-orange-600\" />\r\n              <span className=\"text-light-orange-800\">Settings</span>\r\n            </button>\r\n            <hr className=\"my-2 border-light-orange-100\" />\r\n            <button\r\n              onClick={handleLogout}\r\n              className=\"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-50 transition-colors\"\r\n            >\r\n              <LogoutIcon className=\"w-5 h-5 text-red-600\" />\r\n              <span className=\"text-red-800\">Sign Out</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Login Modal */}\r\n      {showLoginModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\">\r\n            <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-bold text-white\">Sign In</h2>\r\n                <button\r\n                  onClick={() => setShowLoginModal(false)}\r\n                  className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <form onSubmit={handleLogin} className=\"p-6 space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Email Address\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  value={loginForm.email}\r\n                  onChange={(e) => setLoginForm({...loginForm, email: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={loginForm.password}\r\n                  onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\"\r\n              >\r\n                Sign In\r\n              </button>\r\n\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    setShowLoginModal(false);\r\n                    setShowRegisterModal(true);\r\n                  }}\r\n                  className=\"text-light-orange-600 hover:text-light-orange-700 transition-colors\"\r\n                >\r\n                  Don't have an account? Sign up\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Register Modal */}\r\n      {showRegisterModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-xl shadow-2xl w-full max-w-md mx-4\">\r\n            <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-bold text-white\">Create Account</h2>\r\n                <button\r\n                  onClick={() => setShowRegisterModal(false)}\r\n                  className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <form onSubmit={handleRegister} className=\"p-6 space-y-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Full Name\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={registerForm.name}\r\n                  onChange={(e) => setRegisterForm({...registerForm, name: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your full name\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Email Address\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  value={registerForm.email}\r\n                  onChange={(e) => setRegisterForm({...registerForm, email: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={registerForm.password}\r\n                  onChange={(e) => setRegisterForm({...registerForm, password: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Create a password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-light-orange-800 mb-2\">\r\n                  Confirm Password\r\n                </label>\r\n                <input\r\n                  type=\"password\"\r\n                  value={registerForm.confirmPassword}\r\n                  onChange={(e) => setRegisterForm({...registerForm, confirmPassword: e.target.value})}\r\n                  className=\"w-full px-4 py-3 border border-light-orange-200 rounded-lg focus:ring-2 focus:ring-light-orange-300 focus:border-light-orange-400 transition-colors\"\r\n                  placeholder=\"Confirm your password\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md\"\r\n              >\r\n                Create Account\r\n              </button>\r\n\r\n              <div className=\"text-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    setShowRegisterModal(false);\r\n                    setShowLoginModal(true);\r\n                  }}\r\n                  className=\"text-light-orange-600 hover:text-light-orange-700 transition-colors\"\r\n                >\r\n                  Already have an account? Sign in\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserAccounts;\r\n"], "mappings": "4JAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,QAAQ,CAAEC,4BAA4B,GAAI,CAAAC,SAAS,CAAEC,6BAA6B,GAAI,CAAAC,UAAU,CAAEC,OAAO,KAAQ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExJ,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACc,cAAc,CAAEC,iBAAiB,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACkB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACoB,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAC,CAAEsB,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAG,CAAC,CAAC,CACvE,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,CAC/C0B,IAAI,CAAE,EAAE,CACRJ,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZI,eAAe,CAAE,EACnB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,IAAI,CAAG,CACXF,IAAI,CAAE,UAAU,CAChBJ,KAAK,CAAE,sBAAsB,CAC7BO,MAAM,CAAE,gCACV,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIC,CAAC,EAAK,CACzBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB;AACAnB,aAAa,CAAC,IAAI,CAAC,CACnBI,iBAAiB,CAAC,KAAK,CAAC,CACxBI,YAAY,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAG,CAAC,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAU,cAAc,CAAIF,CAAC,EAAK,CAC5BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB;AACAnB,aAAa,CAAC,IAAI,CAAC,CACnBM,oBAAoB,CAAC,KAAK,CAAC,CAC3BM,eAAe,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEJ,KAAK,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEI,eAAe,CAAE,EAAG,CAAC,CAAC,CAC7E,CAAC,CAED,KAAM,CAAAO,YAAY,CAAGA,CAAA,GAAM,CACzBrB,aAAa,CAAC,KAAK,CAAC,CACpBE,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAED,mBACEL,KAAA,QAAKyB,SAAS,CAAC,UAAU,CAAAC,QAAA,EAEtBxB,UAAU,cACTF,KAAA,WACE2B,OAAO,CAAEA,CAAA,GAAMtB,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAClDqB,SAAS,CAAC,8IAA8I,CAAAC,QAAA,eAExJ5B,IAAA,QACE8B,GAAG,CAAEV,IAAI,CAACC,MAAO,CACjBU,GAAG,CAAEX,IAAI,CAACF,IAAK,CACfS,SAAS,CAAC,uDAAuD,CAClE,CAAC,cACF3B,IAAA,SAAM2B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAER,IAAI,CAACF,IAAI,CAAO,CAAC,cAChElB,IAAA,QAAK2B,SAAS,CAAC,SAAS,CAACK,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAN,QAAA,cAC5E5B,IAAA,SAAMmC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,gBAAgB,CAAE,CAAC,CACrF,CAAC,EACA,CAAC,cAETpC,KAAA,WACE2B,OAAO,CAAEA,CAAA,GAAMpB,iBAAiB,CAAC,IAAI,CAAE,CACvCkB,SAAS,CAAC,uIAAuI,CAAAC,QAAA,eAEjJ5B,IAAA,CAACL,SAAS,EAACgC,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC3B,IAAA,SAAA4B,QAAA,CAAM,SAAO,CAAM,CAAC,EACd,CACT,CAGAxB,UAAU,EAAIE,cAAc,eAC3BJ,KAAA,QAAKyB,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC5G5B,IAAA,QAAK2B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,cACnD1B,KAAA,QAAKyB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5B,IAAA,QACE8B,GAAG,CAAEV,IAAI,CAACC,MAAO,CACjBU,GAAG,CAAEX,IAAI,CAACF,IAAK,CACfS,SAAS,CAAC,yDAAyD,CACpE,CAAC,cACFzB,KAAA,QAAA0B,QAAA,eACE5B,IAAA,OAAI2B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAER,IAAI,CAACF,IAAI,CAAK,CAAC,cACpElB,IAAA,MAAG2B,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAER,IAAI,CAACN,KAAK,CAAI,CAAC,EAC1D,CAAC,EACH,CAAC,CACH,CAAC,cAENZ,KAAA,QAAKyB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,KAAA,WAAQyB,SAAS,CAAC,mGAAmG,CAAAC,QAAA,eACnH5B,IAAA,CAACP,QAAQ,EAACkC,SAAS,CAAC,+BAA+B,CAAE,CAAC,cACtD3B,IAAA,SAAM2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,EAChD,CAAC,cACT1B,KAAA,WAAQyB,SAAS,CAAC,mGAAmG,CAAAC,QAAA,eACnH5B,IAAA,CAACF,OAAO,EAAC6B,SAAS,CAAC,+BAA+B,CAAE,CAAC,cACrD3B,IAAA,SAAM2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACjD,CAAC,cACT5B,IAAA,OAAI2B,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC/CzB,KAAA,WACE2B,OAAO,CAAEH,YAAa,CACtBC,SAAS,CAAC,0FAA0F,CAAAC,QAAA,eAEpG5B,IAAA,CAACH,UAAU,EAAC8B,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAC/C3B,IAAA,SAAM2B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACxC,CAAC,EACN,CAAC,EACH,CACN,CAGApB,cAAc,eACbR,IAAA,QAAK2B,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF1B,KAAA,QAAKyB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClE5B,IAAA,QAAK2B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChG1B,KAAA,QAAKyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5B,IAAA,OAAI2B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cACzD5B,IAAA,WACE6B,OAAO,CAAEA,CAAA,GAAMpB,iBAAiB,CAAC,KAAK,CAAE,CACxCkB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpE5B,IAAA,QAAK2B,SAAS,CAAC,SAAS,CAACK,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAN,QAAA,cAC5E5B,IAAA,SAAMmC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,sBAAsB,CAAE,CAAC,CAC3F,CAAC,CACA,CAAC,EACN,CAAC,CACH,CAAC,cAENpC,KAAA,SAAMqC,QAAQ,CAAEjB,WAAY,CAACK,SAAS,CAAC,eAAe,CAAAC,QAAA,eACpD1B,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,eAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAE7B,SAAS,CAACE,KAAM,CACvB4B,QAAQ,CAAGnB,CAAC,EAAKV,YAAY,CAAA8B,aAAA,CAAAA,aAAA,IAAK/B,SAAS,MAAEE,KAAK,CAAES,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CACrEd,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5C,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,UAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE7B,SAAS,CAACG,QAAS,CAC1B2B,QAAQ,CAAGnB,CAAC,EAAKV,YAAY,CAAA8B,aAAA,CAAAA,aAAA,IAAK/B,SAAS,MAAEG,QAAQ,CAAEQ,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CACxEd,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,qBAAqB,CACjCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN9C,IAAA,WACEwC,IAAI,CAAC,QAAQ,CACbb,SAAS,CAAC,6MAA6M,CAAAC,QAAA,CACxN,SAED,CAAQ,CAAC,cAET5B,IAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B5B,IAAA,WACEwC,IAAI,CAAC,QAAQ,CACbX,OAAO,CAAEA,CAAA,GAAM,CACbpB,iBAAiB,CAAC,KAAK,CAAC,CACxBE,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACFgB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAChF,gCAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CACN,CAGAlB,iBAAiB,eAChBV,IAAA,QAAK2B,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF1B,KAAA,QAAKyB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClE5B,IAAA,QAAK2B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,cAChG1B,KAAA,QAAKyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5B,IAAA,OAAI2B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAChE5B,IAAA,WACE6B,OAAO,CAAEA,CAAA,GAAMlB,oBAAoB,CAAC,KAAK,CAAE,CAC3CgB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cAEpE5B,IAAA,QAAK2B,SAAS,CAAC,SAAS,CAACK,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAN,QAAA,cAC5E5B,IAAA,SAAMmC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,sBAAsB,CAAE,CAAC,CAC3F,CAAC,CACA,CAAC,EACN,CAAC,CACH,CAAC,cAENpC,KAAA,SAAMqC,QAAQ,CAAEd,cAAe,CAACE,SAAS,CAAC,eAAe,CAAAC,QAAA,eACvD1B,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,WAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEzB,YAAY,CAACE,IAAK,CACzBwB,QAAQ,CAAGnB,CAAC,EAAKN,eAAe,CAAA0B,aAAA,CAAAA,aAAA,IAAK3B,YAAY,MAAEE,IAAI,CAAEK,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CAC1Ed,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,sBAAsB,CAClCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5C,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,eAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEzB,YAAY,CAACF,KAAM,CAC1B4B,QAAQ,CAAGnB,CAAC,EAAKN,eAAe,CAAA0B,aAAA,CAAAA,aAAA,IAAK3B,YAAY,MAAEF,KAAK,CAAES,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CAC3Ed,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,kBAAkB,CAC9BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5C,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,UAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEzB,YAAY,CAACD,QAAS,CAC7B2B,QAAQ,CAAGnB,CAAC,EAAKN,eAAe,CAAA0B,aAAA,CAAAA,aAAA,IAAK3B,YAAY,MAAED,QAAQ,CAAEQ,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CAC9Ed,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5C,KAAA,QAAA0B,QAAA,eACE5B,IAAA,UAAO2B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAAC,kBAE1E,CAAO,CAAC,cACR5B,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEzB,YAAY,CAACG,eAAgB,CACpCuB,QAAQ,CAAGnB,CAAC,EAAKN,eAAe,CAAA0B,aAAA,CAAAA,aAAA,IAAK3B,YAAY,MAAEG,eAAe,CAAEI,CAAC,CAACqB,MAAM,CAACH,KAAK,EAAC,CAAE,CACrFd,SAAS,CAAC,qJAAqJ,CAC/JkB,WAAW,CAAC,uBAAuB,CACnCC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN9C,IAAA,WACEwC,IAAI,CAAC,QAAQ,CACbb,SAAS,CAAC,6MAA6M,CAAAC,QAAA,CACxN,gBAED,CAAQ,CAAC,cAET5B,IAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B5B,IAAA,WACEwC,IAAI,CAAC,QAAQ,CACbX,OAAO,CAAEA,CAAA,GAAM,CACblB,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACFkB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAChF,kCAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}