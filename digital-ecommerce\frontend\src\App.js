import React from 'react';
import ProductList from './components/ProductList';
import SearchFilters from './components/SearchFilters';
import ShoppingCart from './components/ShoppingCart';
import UserAccounts from './components/UserAccounts';
import OrderHistory from './components/OrderHistory';
import ProductReviews from './components/ProductReviews';
import Wishlist from './components/Wishlist';
import EmailNotifications from './components/EmailNotifications';
import Promotions from './components/Promotions';
import MultiLanguageSupport from './components/MultiLanguageSupport';
// Removed unused imports: Checkout, AdminDashboard

function App() {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">E-Commerce Store</h1>
            <div className="flex space-x-4">
              <UserAccounts />
              <ShoppingCart />
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <aside className="lg:col-span-1">
            <SearchFilters />
            <Promotions />
          </aside>
          <div className="lg:col-span-3">
            <ProductList />
            <OrderHistory />
            <ProductReviews />
            <Wishlist />
          </div>
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <MultiLanguageSupport />
            <EmailNotifications />
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
