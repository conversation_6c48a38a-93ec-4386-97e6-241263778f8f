{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport { HelpPage, ReturnsPage, ShippingPage, TrackOrderPage, PrivacyPage, TermsPage, CookiesPage, OrdersPage } from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(UserProvider, {\n    children: /*#__PURE__*/_jsxDEV(CartProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n          children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            mode: \"wait\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/products\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/digital-products\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(DigitalProductsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/about\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/contact\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/checkout\",\n                element: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -20\n                  },\n                  transition: {\n                    duration: 0.3\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/help\",\n                element: /*#__PURE__*/_jsxDEV(HelpPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/returns\",\n                element: /*#__PURE__*/_jsxDEV(ReturnsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/shipping\",\n                element: /*#__PURE__*/_jsxDEV(ShippingPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/track\",\n                element: /*#__PURE__*/_jsxDEV(TrackOrderPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/orders\",\n                element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/privacy\",\n                element: /*#__PURE__*/_jsxDEV(PrivacyPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/terms\",\n                element: /*#__PURE__*/_jsxDEV(TermsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/cookies\",\n                element: /*#__PURE__*/_jsxDEV(CookiesPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/register\",\n                element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/reset-password\",\n                element: /*#__PURE__*/_jsxDEV(ResetPasswordPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n            className: \"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-1 md:col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-6 h-6 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 123,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 122,\n                        columnNumber: 21\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-bold\",\n                      children: \"ShopHub\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-300 mb-4 max-w-md\",\n                    children: \"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-4\",\n                    children: [/*#__PURE__*/_jsxDEV(MultiLanguageSupport, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(EmailNotifications, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Quick Links\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Home\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 142,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/products\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Products\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/digital-products\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Digital Products\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 144,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/about\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"About Us\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/contact\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Contact\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 146,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Customer Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/help\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Help Center\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 154,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/returns\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Returns\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 155,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/shipping\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Shipping Info\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/track\",\n                        className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                        children: \"Track Order\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 157,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 text-sm\",\n                  children: \"\\xA9 2024 ShopHub. All rights reserved.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-6 mt-4 md:mt-0\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/privacy\",\n                    className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                    children: \"Privacy Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                    children: \"Terms of Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/cookies\",\n                    className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                    children: \"Cookie Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "CartProvider", "UserProvider", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "CheckoutPage", "LoginPage", "RegisterPage", "ResetPasswordPage", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage", "MultiLanguageSupport", "EmailNotifications", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport { CartProvider } from './components/ShoppingCart';\nimport { UserProvider } from './contexts/UserContext';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport CheckoutPage from './pages/CheckoutPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ResetPasswordPage from './pages/ResetPasswordPage';\nimport {\n  HelpPage,\n  ReturnsPage,\n  ShippingPage,\n  TrackOrderPage,\n  PrivacyPage,\n  TermsPage,\n  CookiesPage,\n  OrdersPage\n} from './pages/PlaceholderPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <UserProvider>\n      <CartProvider>\n        <Router>\n        <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n          <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n            <Route path=\"/checkout\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <CheckoutPage />\n              </motion.div>\n            } />\n            <Route path=\"/help\" element={<HelpPage />} />\n            <Route path=\"/returns\" element={<ReturnsPage />} />\n            <Route path=\"/shipping\" element={<ShippingPage />} />\n            <Route path=\"/track\" element={<TrackOrderPage />} />\n            <Route path=\"/orders\" element={<OrdersPage />} />\n            <Route path=\"/privacy\" element={<PrivacyPage />} />\n            <Route path=\"/terms\" element={<TermsPage />} />\n            <Route path=\"/cookies\" element={<CookiesPage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/register\" element={<RegisterPage />} />\n            <Route path=\"/reset-password\" element={<ResetPasswordPage />} />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/help\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</Link></li>\n                  <li><Link to=\"/returns\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</Link></li>\n                  <li><Link to=\"/shipping\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</Link></li>\n                  <li><Link to=\"/track\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <Link to=\"/privacy\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</Link>\n                <Link to=\"/terms\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</Link>\n                <Link to=\"/cookies\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n        </div>\n        </Router>\n      </CartProvider>\n    </UserProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,SACEC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,QACL,yBAAyB;AAChC,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACrB,YAAY;IAAAuB,QAAA,eACXF,OAAA,CAACtB,YAAY;MAAAwB,QAAA,eACXF,OAAA,CAAC7B,MAAM;QAAA+B,QAAA,eACPF,OAAA;UAAKG,SAAS,EAAC,8DAA8D;UAAAD,QAAA,gBAC3EF,OAAA,CAACvB,UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEhBP,OAAA,CAACxB,eAAe;YAACgC,IAAI,EAAC,MAAM;YAAAN,QAAA,eAC1BF,OAAA,CAAC5B,MAAM;cAAA8B,QAAA,gBACLF,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,GAAG;gBAACC,OAAO,eACrBV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAACpB,QAAQ;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAC7BV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAACnB,YAAY;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,mBAAmB;gBAACC,OAAO,eACrCV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAAClB,mBAAmB;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAC1BV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAACjB,SAAS;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAC5BV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAAChB,WAAW;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAC7BV,OAAA,CAACzB,MAAM,CAACoC,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BE,IAAI,EAAE;oBAAEH,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BG,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI,CAAE;kBAAAhB,QAAA,eAE9BF,OAAA,CAACf,YAAY;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAEV,OAAA,CAACX,QAAQ;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEV,OAAA,CAACV,WAAW;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAEV,OAAA,CAACT,YAAY;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEV,OAAA,CAACR,cAAc;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAEV,OAAA,CAACJ,UAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEV,OAAA,CAACP,WAAW;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEV,OAAA,CAACN,SAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEV,OAAA,CAACL,WAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEV,OAAA,CAACd,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAEV,OAAA,CAACb,YAAY;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDP,OAAA,CAAC3B,KAAK;gBAACoC,IAAI,EAAC,iBAAiB;gBAACC,OAAO,eAAEV,OAAA,CAACZ,iBAAiB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGlBP,OAAA;YAAQG,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACvEF,OAAA;cAAKG,SAAS,EAAC,8CAA8C;cAAAD,QAAA,gBAC3DF,OAAA;gBAAKG,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBAEpDF,OAAA;kBAAKG,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvCF,OAAA;oBAAKG,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBAC/CF,OAAA;sBAAKG,SAAS,EAAC,oHAAoH;sBAAAD,QAAA,eACjIF,OAAA;wBAAKG,SAAS,EAAC,oBAAoB;wBAACgB,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAnB,QAAA,eACvFF,OAAA;0BAAMsB,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAA4C;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNP,OAAA;sBAAMG,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACNP,OAAA;oBAAGG,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,EAAC;kBAG3C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJP,OAAA;oBAAKG,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7BF,OAAA,CAACH,oBAAoB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxBP,OAAA,CAACF,kBAAkB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNP,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAIG,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DP,OAAA;oBAAIG,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACvBF,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,GAAG;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzGP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,WAAW;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrHP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,mBAAmB;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrIP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,QAAQ;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClHP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,UAAU;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAGNP,OAAA;kBAAAE,QAAA,gBACEF,OAAA;oBAAIG,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEP,OAAA;oBAAIG,SAAS,EAAC,WAAW;oBAAAD,QAAA,gBACvBF,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,OAAO;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAW;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpHP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,UAAU;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnHP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,WAAW;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1HP,OAAA;sBAAAE,QAAA,eAAIF,OAAA,CAAC1B,IAAI;wBAACoD,EAAE,EAAC,QAAQ;wBAACvB,SAAS,EAAC,6DAA6D;wBAAAD,QAAA,EAAC;sBAAW;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENP,OAAA;gBAAKG,SAAS,EAAC,2FAA2F;gBAAAD,QAAA,gBACxGF,OAAA;kBAAGG,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAKG,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1CF,OAAA,CAAC1B,IAAI;oBAACoD,EAAE,EAAC,UAAU;oBAACvB,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzHP,OAAA,CAAC1B,IAAI;oBAACoD,EAAE,EAAC,QAAQ;oBAACvB,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzHP,OAAA,CAAC1B,IAAI;oBAACoD,EAAE,EAAC,UAAU;oBAACvB,SAAS,EAAC,qEAAqE;oBAAAD,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAACoB,EAAA,GArJQ1B,GAAG;AAuJZ,eAAeA,GAAG;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}