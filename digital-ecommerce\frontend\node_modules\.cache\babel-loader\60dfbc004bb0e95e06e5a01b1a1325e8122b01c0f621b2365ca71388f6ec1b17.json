{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\ProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport { FunnelIcon, Squares2X2Icon, ListBulletIcon, StarIcon, HeartIcon, ShoppingBagIcon, AdjustmentsHorizontalIcon, ChevronRightIcon, HomeIcon, TagIcon, ClockIcon, TruckIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { categories, products, getProductsByCategory } from '../data/products';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductsPage = () => {\n  _s();\n  var _subcategories$find;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortBy, setSortBy] = useState('featured');\n  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');\n  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');\n  const [productType, setProductType] = useState('all'); // all, physical, digital\n  const [priceRange, setPriceRange] = useState([0, 1000]);\n  const [selectedRating, setSelectedRating] = useState(0);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Get current category data\n  const currentCategory = categories.find(cat => cat.id === selectedCategory);\n  const subcategories = currentCategory ? [{\n    id: 'all',\n    name: 'All ' + currentCategory.name,\n    count: getProductsByCategory(selectedCategory).length\n  }, ...currentCategory.subcategories.map(sub => ({\n    id: sub,\n    name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),\n    count: products.filter(p => p.subcategory === sub).length\n  }))] : [];\n  const productTypeOptions = [{\n    id: 'all',\n    name: 'All Products',\n    count: products.length\n  }, {\n    id: 'physical',\n    name: 'Physical Products',\n    count: products.filter(p => p.type === 'physical').length\n  }, {\n    id: 'digital',\n    name: 'Digital Products',\n    count: products.filter(p => p.type === 'digital').length\n  }];\n  const sortOptions = [{\n    value: 'featured',\n    label: 'Featured'\n  }, {\n    value: 'price-low',\n    label: 'Price: Low to High'\n  }, {\n    value: 'price-high',\n    label: 'Price: High to Low'\n  }, {\n    value: 'rating',\n    label: 'Highest Rated'\n  }, {\n    value: 'newest',\n    label: 'Newest First'\n  }];\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;\n      const typeMatch = productType === 'all' || product.type === productType;\n      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];\n      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;\n      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'newest':\n        filtered.sort((a, b) => b.id.localeCompare(a.id));\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n    return filtered;\n  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);\n  const ProductCard = ({\n    product,\n    index\n  }) => {\n    var _product$shipping;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      layout: true,\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      exit: {\n        opacity: 0,\n        y: -20\n      },\n      transition: {\n        duration: 0.3,\n        delay: index * 0.05\n      },\n      className: `bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 ${viewMode === 'list' ? 'flex' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.images ? product.images[0] : product.image,\n          alt: product.name,\n          className: `w-full object-cover group-hover:scale-105 transition-transform duration-300 ${viewMode === 'list' ? 'h-48' : 'h-64'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 9\n        }, this), product.badge && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 left-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `px-3 py-1 rounded-full text-sm font-semibold text-white ${product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'}`,\n            children: product.badge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), product.type === 'digital' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 left-4 mt-8\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\",\n            children: \"Digital\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 right-4\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.1\n            },\n            whileTap: {\n              scale: 0.9\n            },\n            className: \"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(HeartIcon, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 9\n        }, this), !product.inStock && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-semibold\",\n            children: \"Out of Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: `font-semibold text-gray-900 mb-2 ${viewMode === 'list' ? 'text-xl' : 'text-lg'}`,\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [...Array(5)].map((_, i) => i < Math.floor(product.rating) ? /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                className: \"w-4 h-4 text-yellow-400\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(StarIcon, {\n                className: \"w-4 h-4 text-gray-300\"\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600 ml-2\",\n              children: [product.rating, \" (\", product.reviews, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-light-orange-600\",\n              children: [\"$\", product.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 13\n            }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-gray-500 line-through\",\n              children: [\"$\", product.originalPrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 11\n          }, this), product.type === 'digital' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n                className: \"w-4 h-4 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-green-600 font-medium\",\n                children: \"Instant Delivery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), product.platforms && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Platforms:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-800\",\n                children: product.platforms.join(', ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [product.colors && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Colors:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), product.colors.slice(0, 3).map((color, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${color === 'black' || color === 'Black' ? 'bg-black' : color === 'white' || color === 'White' ? 'bg-white' : color === 'blue' || color === 'Blue' ? 'bg-blue-500' : color === 'red' || color === 'Red' ? 'bg-red-500' : color === 'silver' || color === 'Silver' ? 'bg-gray-400' : color === 'gold' || color === 'Gold' ? 'bg-yellow-400' : 'bg-gray-300'}`\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)), product.colors.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"+\", product.colors.length - 3]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(TruckIcon, {\n                className: \"w-4 h-4 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-blue-600\",\n                children: (_product$shipping = product.shipping) !== null && _product$shipping !== void 0 && _product$shipping.free ? 'Free Shipping' : 'Shipping Available'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              className: `w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`,\n              children: [product.inStock ? 'In Stock' : 'Out of Stock', product.stockCount && product.inStock && ` (${product.stockCount} available)`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex items-center space-x-2 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n              className: \"w-4 h-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), \"Home\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n            className: \"w-4 h-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), selectedCategory !== 'all' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n              className: \"w-4 h-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-light-orange-600 font-medium\",\n              children: currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), selectedSubcategory !== 'all' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n              className: \"w-4 h-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-light-orange-600 font-medium\",\n              children: (_subcategories$find = subcategories.find(sub => sub.id === selectedSubcategory)) === null || _subcategories$find === void 0 ? void 0 : _subcategories$find.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl lg:text-5xl font-bold text-white mb-4\",\n            children: selectedCategory === 'all' ? 'All Products' : (currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.name) || 'Products'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-light-orange-100 max-w-2xl mx-auto\",\n            children: selectedCategory === 'all' ? 'Discover our amazing collection of premium products' : (currentCategory === null || currentCategory === void 0 ? void 0 : currentCategory.description) || 'Explore our curated selection'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4 justify-center\",\n          children: [{\n            id: 'all',\n            name: 'All Products',\n            icon: '🛍️'\n          }, ...categories].map(category => /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => {\n              setSelectedCategory(category.id);\n              setSelectedSubcategory('all');\n              setSearchParams({\n                category: category.id\n              });\n            },\n            className: `flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${selectedCategory === category.id ? 'bg-light-orange-500 text-white shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: category.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-64 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowFilters(!showFilters),\n                className: \"lg:hidden p-2 text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(AdjustmentsHorizontalIcon, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-3\",\n                  children: \"Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setSelectedCategory(category.id),\n                    className: `w-full text-left px-3 py-2 rounded-lg transition-colors ${selectedCategory === category.id ? 'bg-light-orange-100 text-light-orange-700' : 'text-gray-600 hover:bg-gray-100'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: category.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: [\"(\", category.count, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)\n                  }, category.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-3\",\n                  children: [\"Price Range: $\", priceRange[0], \" - $\", priceRange[1]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0\",\n                  max: \"1000\",\n                  value: priceRange[1],\n                  onChange: e => setPriceRange([priceRange[0], parseInt(e.target.value)]),\n                  className: \"w-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-3\",\n                  children: \"Minimum Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [4, 3, 2, 1, 0].map(rating => /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setSelectedRating(rating),\n                    className: `flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${selectedRating === rating ? 'bg-light-orange-100 text-light-orange-700' : 'text-gray-600 hover:bg-gray-100'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex\",\n                      children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIconSolid, {\n                        className: `w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: rating > 0 ? `${rating}+ Stars` : 'All Ratings'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 25\n                    }, this)]\n                  }, rating, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-6 mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: [\"Showing \", filteredAndSortedProducts.length, \" of \", products.length, \" products\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: sortBy,\n                  onChange: e => setSortBy(e.target.value),\n                  className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\",\n                  children: sortOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex bg-gray-100 rounded-lg p-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('grid'),\n                    className: `p-2 rounded-md transition-colors ${viewMode === 'grid' ? 'bg-white text-light-orange-600 shadow-sm' : 'text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(Squares2X2Icon, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setViewMode('list'),\n                    className: `p-2 rounded-md transition-colors ${viewMode === 'list' ? 'bg-white text-light-orange-600 shadow-sm' : 'text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(ListBulletIcon, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            mode: \"wait\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              exit: {\n                opacity: 0\n              },\n              className: `${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8' : 'space-y-6'}`,\n              children: filteredAndSortedProducts.map((product, index) => /*#__PURE__*/_jsxDEV(ProductCard, {\n                product: product,\n                index: index\n              }, product.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this))\n            }, `${viewMode}-${selectedCategory}-${sortBy}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), filteredAndSortedProducts.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(FunnelIcon, {\n                className: \"w-16 h-16 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No products found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Try adjusting your filters to see more results.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsPage, \"WdP8mHYag7mZoXjMKgXiM5gAxRI=\", false, function () {\n  return [useSearchParams];\n});\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "motion", "AnimatePresence", "useSearchParams", "Link", "FunnelIcon", "Squares2X2Icon", "ListBulletIcon", "StarIcon", "HeartIcon", "ShoppingBagIcon", "AdjustmentsHorizontalIcon", "ChevronRightIcon", "HomeIcon", "TagIcon", "ClockIcon", "TruckIcon", "CheckCircleIcon", "StarIconSolid", "categories", "products", "getProductsByCategory", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsPage", "_s", "_subcategories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "sortBy", "setSortBy", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "get", "selectedSubcategory", "setSelectedSubcategory", "productType", "setProductType", "priceRange", "setPriceRange", "selectedRating", "setSelectedRating", "showFilters", "setShowFilters", "currentCategory", "find", "cat", "id", "subcategories", "name", "count", "length", "map", "sub", "split", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "filter", "p", "subcategory", "productTypeOptions", "type", "sortOptions", "value", "label", "filteredAndSortedProducts", "filtered", "product", "categoryMatch", "category", "subcategoryMatch", "typeMatch", "priceMatch", "price", "ratingMatch", "rating", "sort", "a", "b", "localeCompare", "ProductCard", "index", "_product$shipping", "div", "layout", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "delay", "className", "children", "src", "images", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "badge", "button", "whileHover", "scale", "whileTap", "inStock", "Array", "_", "i", "Math", "floor", "reviews", "originalPrice", "platforms", "colors", "color", "shipping", "free", "stockCount", "to", "description", "icon", "onClick", "min", "max", "onChange", "e", "parseInt", "target", "option", "mode", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSearchParams, Link } from 'react-router-dom';\nimport {\n  FunnelIcon,\n  Squares2X2Icon,\n  ListBulletIcon,\n  StarIcon,\n  HeartIcon,\n  ShoppingBagIcon,\n  AdjustmentsHorizontalIcon,\n  ChevronRightIcon,\n  HomeIcon,\n  TagIcon,\n  ClockIcon,\n  TruckIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { categories, products, getProductsByCategory } from '../data/products';\n\nconst ProductsPage = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortBy, setSortBy] = useState('featured');\n  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');\n  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || 'all');\n  const [productType, setProductType] = useState('all'); // all, physical, digital\n  const [priceRange, setPriceRange] = useState([0, 1000]);\n  const [selectedRating, setSelectedRating] = useState(0);\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Get current category data\n  const currentCategory = categories.find(cat => cat.id === selectedCategory);\n  const subcategories = currentCategory ? [\n    { id: 'all', name: 'All ' + currentCategory.name, count: getProductsByCategory(selectedCategory).length },\n    ...currentCategory.subcategories.map(sub => ({\n      id: sub,\n      name: sub.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),\n      count: products.filter(p => p.subcategory === sub).length\n    }))\n  ] : [];\n\n  const productTypeOptions = [\n    { id: 'all', name: 'All Products', count: products.length },\n    { id: 'physical', name: 'Physical Products', count: products.filter(p => p.type === 'physical').length },\n    { id: 'digital', name: 'Digital Products', count: products.filter(p => p.type === 'digital').length }\n  ];\n\n  const sortOptions = [\n    { value: 'featured', label: 'Featured' },\n    { value: 'price-low', label: 'Price: Low to High' },\n    { value: 'price-high', label: 'Price: High to Low' },\n    { value: 'rating', label: 'Highest Rated' },\n    { value: 'newest', label: 'Newest First' }\n  ];\n\n  const filteredAndSortedProducts = useMemo(() => {\n    let filtered = products.filter(product => {\n      const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n      const subcategoryMatch = selectedSubcategory === 'all' || product.subcategory === selectedSubcategory;\n      const typeMatch = productType === 'all' || product.type === productType;\n      const priceMatch = product.price >= priceRange[0] && product.price <= priceRange[1];\n      const ratingMatch = selectedRating === 0 || product.rating >= selectedRating;\n\n      return categoryMatch && subcategoryMatch && typeMatch && priceMatch && ratingMatch;\n    });\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'newest':\n        filtered.sort((a, b) => b.id.localeCompare(a.id));\n        break;\n      case 'name':\n        filtered.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      default:\n        // Featured - keep original order\n        break;\n    }\n\n    return filtered;\n  }, [selectedCategory, selectedSubcategory, productType, priceRange, selectedRating, sortBy]);\n\n  const ProductCard = ({ product, index }) => (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3, delay: index * 0.05 }}\n      className={`bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer hover:shadow-xl transition-all duration-300 ${\n        viewMode === 'list' ? 'flex' : ''\n      }`}\n    >\n      <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n        <img\n          src={product.images ? product.images[0] : product.image}\n          alt={product.name}\n          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${\n            viewMode === 'list' ? 'h-48' : 'h-64'\n          }`}\n        />\n        {product.badge && (\n          <div className=\"absolute top-4 left-4\">\n            <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${\n              product.type === 'digital' ? 'bg-blue-500' : 'bg-light-orange-500'\n            }`}>\n              {product.badge}\n            </span>\n          </div>\n        )}\n        {product.type === 'digital' && (\n          <div className=\"absolute top-4 left-4 mt-8\">\n            <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n              Digital\n            </span>\n          </div>\n        )}\n        <div className=\"absolute top-4 right-4\">\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            className=\"bg-white bg-opacity-90 p-2 rounded-full shadow-lg\"\n          >\n            <HeartIcon className=\"w-5 h-5 text-gray-600\" />\n          </motion.button>\n        </div>\n        {!product.inStock && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-semibold\">Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      <div className={`p-6 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>\n        <div>\n          <h3 className={`font-semibold text-gray-900 mb-2 ${\n            viewMode === 'list' ? 'text-xl' : 'text-lg'\n          }`}>\n            {product.name}\n          </h3>\n\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex\">\n              {[...Array(5)].map((_, i) => (\n                i < Math.floor(product.rating) ? (\n                  <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n                ) : (\n                  <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n                )\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-600 ml-2\">\n              {product.rating} ({product.reviews})\n            </span>\n          </div>\n\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <span className=\"text-2xl font-bold text-light-orange-600\">\n              ${product.price}\n            </span>\n            {product.originalPrice && product.originalPrice > product.price && (\n              <span className=\"text-lg text-gray-500 line-through\">\n                ${product.originalPrice}\n              </span>\n            )}\n          </div>\n\n          {/* Product Type Specific Info */}\n          {product.type === 'digital' ? (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <ClockIcon className=\"w-4 h-4 text-green-600\" />\n                <span className=\"text-sm text-green-600 font-medium\">Instant Delivery</span>\n              </div>\n              {product.platforms && (\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-600\">Platforms:</span>\n                  <span className=\"text-sm text-gray-800\">{product.platforms.join(', ')}</span>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Color Options for Physical Products */}\n              {product.colors && (\n                <div className=\"flex items-center space-x-2 mb-4\">\n                  <span className=\"text-sm text-gray-600\">Colors:</span>\n                  {product.colors.slice(0, 3).map((color, index) => (\n                    <div\n                      key={index}\n                      className={`w-6 h-6 rounded-full border-2 border-gray-300 cursor-pointer ${\n                        color === 'black' || color === 'Black' ? 'bg-black' :\n                        color === 'white' || color === 'White' ? 'bg-white' :\n                        color === 'blue' || color === 'Blue' ? 'bg-blue-500' :\n                        color === 'red' || color === 'Red' ? 'bg-red-500' :\n                        color === 'silver' || color === 'Silver' ? 'bg-gray-400' :\n                        color === 'gold' || color === 'Gold' ? 'bg-yellow-400' :\n                        'bg-gray-300'\n                      }`}\n                    />\n                  ))}\n                  {product.colors.length > 3 && (\n                    <span className=\"text-sm text-gray-500\">+{product.colors.length - 3}</span>\n                  )}\n                </div>\n              )}\n              {/* Shipping Info */}\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TruckIcon className=\"w-4 h-4 text-blue-600\" />\n                <span className=\"text-sm text-blue-600\">\n                  {product.shipping?.free ? 'Free Shipping' : 'Shipping Available'}\n                </span>\n              </div>\n            </>\n          )}\n\n          {/* Stock Status */}\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <CheckCircleIcon className={`w-4 h-4 ${product.inStock ? 'text-green-600' : 'text-red-600'}`} />\n            <span className={`text-sm ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>\n              {product.inStock ? 'In Stock' : 'Out of Stock'}\n              {product.stockCount && product.inStock && ` (${product.stockCount} available)`}\n            </span>\n          </div>\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 rounded-lg font-semibold hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n        >\n          <ShoppingBagIcon className=\"w-5 h-5\" />\n          <span>Add to Cart</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Breadcrumb */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <nav className=\"flex items-center space-x-2 text-sm\">\n            <Link to=\"/\" className=\"flex items-center text-gray-600 hover:text-light-orange-600 transition-colors\">\n              <HomeIcon className=\"w-4 h-4 mr-1\" />\n              Home\n            </Link>\n            <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n            <span className=\"text-gray-600\">Products</span>\n            {selectedCategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {currentCategory?.name}\n                </span>\n              </>\n            )}\n            {selectedSubcategory !== 'all' && (\n              <>\n                <ChevronRightIcon className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-light-orange-600 font-medium\">\n                  {subcategories.find(sub => sub.id === selectedSubcategory)?.name}\n                </span>\n              </>\n            )}\n          </nav>\n        </div>\n      </div>\n\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\n              {selectedCategory === 'all' ? 'All Products' : currentCategory?.name || 'Products'}\n            </h1>\n            <p className=\"text-xl text-light-orange-100 max-w-2xl mx-auto\">\n              {selectedCategory === 'all'\n                ? 'Discover our amazing collection of premium products'\n                : currentCategory?.description || 'Explore our curated selection'\n              }\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {[{ id: 'all', name: 'All Products', icon: '🛍️' }, ...categories].map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => {\n                  setSelectedCategory(category.id);\n                  setSelectedSubcategory('all');\n                  setSearchParams({ category: category.id });\n                }}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-light-orange-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-light-orange-100 hover:text-light-orange-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar Filters */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n                <button\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"lg:hidden p-2 text-gray-600\"\n                >\n                  <AdjustmentsHorizontalIcon className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                {/* Categories */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Categories</h4>\n                  <div className=\"space-y-2\">\n                    {categories.map(category => (\n                      <button\n                        key={category.id}\n                        onClick={() => setSelectedCategory(category.id)}\n                        className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                          selectedCategory === category.id\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex justify-between\">\n                          <span>{category.name}</span>\n                          <span className=\"text-sm\">({category.count})</span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Price Range */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">\n                    Price Range: ${priceRange[0]} - ${priceRange[1]}\n                  </h4>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"1000\"\n                    value={priceRange[1]}\n                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}\n                    className=\"w-full\"\n                  />\n                </div>\n\n                {/* Rating Filter */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Minimum Rating</h4>\n                  <div className=\"space-y-2\">\n                    {[4, 3, 2, 1, 0].map(rating => (\n                      <button\n                        key={rating}\n                        onClick={() => setSelectedRating(rating)}\n                        className={`flex items-center space-x-2 w-full px-3 py-2 rounded-lg transition-colors ${\n                          selectedRating === rating\n                            ? 'bg-light-orange-100 text-light-orange-700'\n                            : 'text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <div className=\"flex\">\n                          {[...Array(5)].map((_, i) => (\n                            <StarIconSolid\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < rating ? 'text-yellow-400' : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                        <span>{rating > 0 ? `${rating}+ Stars` : 'All Ratings'}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {/* Toolbar */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8\">\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n                <div>\n                  <p className=\"text-gray-600\">\n                    Showing {filteredAndSortedProducts.length} of {products.length} products\n                  </p>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  {/* Sort Dropdown */}\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-light-orange-300\"\n                  >\n                    {sortOptions.map(option => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n\n                  {/* View Mode Toggle */}\n                  <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                    <button\n                      onClick={() => setViewMode('grid')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'grid'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <Squares2X2Icon className=\"w-5 h-5\" />\n                    </button>\n                    <button\n                      onClick={() => setViewMode('list')}\n                      className={`p-2 rounded-md transition-colors ${\n                        viewMode === 'list'\n                          ? 'bg-white text-light-orange-600 shadow-sm'\n                          : 'text-gray-600'\n                      }`}\n                    >\n                      <ListBulletIcon className=\"w-5 h-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Products Grid */}\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={`${viewMode}-${selectedCategory}-${sortBy}`}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className={`${\n                  viewMode === 'grid'\n                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8'\n                    : 'space-y-6'\n                }`}\n              >\n                {filteredAndSortedProducts.map((product, index) => (\n                  <ProductCard key={product.id} product={product} index={index} />\n                ))}\n              </motion.div>\n            </AnimatePresence>\n\n            {filteredAndSortedProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <FunnelIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,eAAe,EAAEC,IAAI,QAAQ,kBAAkB;AACxD,SACEC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,yBAAyB,EACzBC,gBAAgB,EAChBC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,SAAS,EACTC,eAAe,QACV,6BAA6B;AACpC,SAAST,QAAQ,IAAIU,aAAa,QAAQ,2BAA2B;AACrE,SAASC,UAAU,EAAEC,QAAQ,EAAEC,qBAAqB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EACzB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,eAAe,CAAC,CAAC;EACzD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,UAAU,CAAC;EAChD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC8B,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;EAC/F,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC8B,YAAY,CAACQ,GAAG,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC;EACxG,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EACvD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMiD,eAAe,GAAG7B,UAAU,CAAC8B,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKhB,gBAAgB,CAAC;EAC3E,MAAMiB,aAAa,GAAGJ,eAAe,GAAG,CACtC;IAAEG,EAAE,EAAE,KAAK;IAAEE,IAAI,EAAE,MAAM,GAAGL,eAAe,CAACK,IAAI;IAAEC,KAAK,EAAEjC,qBAAqB,CAACc,gBAAgB,CAAC,CAACoB;EAAO,CAAC,EACzG,GAAGP,eAAe,CAACI,aAAa,CAACI,GAAG,CAACC,GAAG,KAAK;IAC3CN,EAAE,EAAEM,GAAG;IACPJ,IAAI,EAAEI,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACF,GAAG,CAACG,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACxFT,KAAK,EAAElC,QAAQ,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKT,GAAG,CAAC,CAACF;EACrD,CAAC,CAAC,CAAC,CACJ,GAAG,EAAE;EAEN,MAAMY,kBAAkB,GAAG,CACzB;IAAEhB,EAAE,EAAE,KAAK;IAAEE,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAElC,QAAQ,CAACmC;EAAO,CAAC,EAC3D;IAAEJ,EAAE,EAAE,UAAU;IAAEE,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAElC,QAAQ,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACG,IAAI,KAAK,UAAU,CAAC,CAACb;EAAO,CAAC,EACxG;IAAEJ,EAAE,EAAE,SAAS;IAAEE,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAElC,QAAQ,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACG,IAAI,KAAK,SAAS,CAAC,CAACb;EAAO,CAAC,CACtG;EAED,MAAMc,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAqB,CAAC,EACnD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAqB,CAAC,EACpD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC3C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAe,CAAC,CAC3C;EAED,MAAMC,yBAAyB,GAAGxE,OAAO,CAAC,MAAM;IAC9C,IAAIyE,QAAQ,GAAGrD,QAAQ,CAAC4C,MAAM,CAACU,OAAO,IAAI;MACxC,MAAMC,aAAa,GAAGxC,gBAAgB,KAAK,KAAK,IAAIuC,OAAO,CAACE,QAAQ,KAAKzC,gBAAgB;MACzF,MAAM0C,gBAAgB,GAAGvC,mBAAmB,KAAK,KAAK,IAAIoC,OAAO,CAACR,WAAW,KAAK5B,mBAAmB;MACrG,MAAMwC,SAAS,GAAGtC,WAAW,KAAK,KAAK,IAAIkC,OAAO,CAACN,IAAI,KAAK5B,WAAW;MACvE,MAAMuC,UAAU,GAAGL,OAAO,CAACM,KAAK,IAAItC,UAAU,CAAC,CAAC,CAAC,IAAIgC,OAAO,CAACM,KAAK,IAAItC,UAAU,CAAC,CAAC,CAAC;MACnF,MAAMuC,WAAW,GAAGrC,cAAc,KAAK,CAAC,IAAI8B,OAAO,CAACQ,MAAM,IAAItC,cAAc;MAE5E,OAAO+B,aAAa,IAAIE,gBAAgB,IAAIC,SAAS,IAAIC,UAAU,IAAIE,WAAW;IACpF,CAAC,CAAC;;IAEF;IACA,QAAQhD,MAAM;MACZ,KAAK,WAAW;QACdwC,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,KAAK,GAAGK,CAAC,CAACL,KAAK,CAAC;QAC1C;MACF,KAAK,YAAY;QACfP,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,KAAK,GAAGI,CAAC,CAACJ,KAAK,CAAC;QAC1C;MACF,KAAK,QAAQ;QACXP,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACH,MAAM,GAAGE,CAAC,CAACF,MAAM,CAAC;QAC5C;MACF,KAAK,QAAQ;QACXT,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAClC,EAAE,CAACmC,aAAa,CAACF,CAAC,CAACjC,EAAE,CAAC,CAAC;QACjD;MACF,KAAK,MAAM;QACTsB,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/B,IAAI,CAACiC,aAAa,CAACD,CAAC,CAAChC,IAAI,CAAC,CAAC;QACrD;MACF;QACE;QACA;IACJ;IAEA,OAAOoB,QAAQ;EACjB,CAAC,EAAE,CAACtC,gBAAgB,EAAEG,mBAAmB,EAAEE,WAAW,EAAEE,UAAU,EAAEE,cAAc,EAAEX,MAAM,CAAC,CAAC;EAE5F,MAAMsD,WAAW,GAAGA,CAAC;IAAEb,OAAO;IAAEc;EAAM,CAAC;IAAA,IAAAC,iBAAA;IAAA,oBACrClE,OAAA,CAACtB,MAAM,CAACyF,GAAG;MACTC,MAAM;MACNC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,IAAI,EAAE;QAAEH,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAC7BG,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAEX,KAAK,GAAG;MAAK,CAAE;MACnDY,SAAS,EAAE,mHACTrE,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,EAChC;MAAAsE,QAAA,gBAEH9E,OAAA;QAAK6E,SAAS,EAAE,YAAYrE,QAAQ,KAAK,MAAM,GAAG,oBAAoB,GAAG,EAAE,EAAG;QAAAsE,QAAA,gBAC5E9E,OAAA;UACE+E,GAAG,EAAE5B,OAAO,CAAC6B,MAAM,GAAG7B,OAAO,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAG7B,OAAO,CAAC8B,KAAM;UACxDC,GAAG,EAAE/B,OAAO,CAACrB,IAAK;UAClB+C,SAAS,EAAE,+EACTrE,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;QACpC;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACDnC,OAAO,CAACoC,KAAK,iBACZvF,OAAA;UAAK6E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC9E,OAAA;YAAM6E,SAAS,EAAE,2DACf1B,OAAO,CAACN,IAAI,KAAK,SAAS,GAAG,aAAa,GAAG,qBAAqB,EACjE;YAAAiC,QAAA,EACA3B,OAAO,CAACoC;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACAnC,OAAO,CAACN,IAAI,KAAK,SAAS,iBACzB7C,OAAA;UAAK6E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzC9E,OAAA;YAAM6E,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAElF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eACDtF,OAAA;UAAK6E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrC9E,OAAA,CAACtB,MAAM,CAAC8G,MAAM;YACZC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAI,CAAE;YACzBb,SAAS,EAAC,mDAAmD;YAAAC,QAAA,eAE7D9E,OAAA,CAACd,SAAS;cAAC2F,SAAS,EAAC;YAAuB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EACL,CAACnC,OAAO,CAACyC,OAAO,iBACf5F,OAAA;UAAK6E,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvF9E,OAAA;YAAM6E,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtF,OAAA;QAAK6E,SAAS,EAAE,OAAOrE,QAAQ,KAAK,MAAM,GAAG,sCAAsC,GAAG,EAAE,EAAG;QAAAsE,QAAA,gBACzF9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA;YAAI6E,SAAS,EAAE,oCACbrE,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAC1C;YAAAsE,QAAA,EACA3B,OAAO,CAACrB;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAELtF,OAAA;YAAK6E,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC9E,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClB,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAAC6D,CAAC,EAAEC,CAAC,KACtBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC9C,OAAO,CAACQ,MAAM,CAAC,gBAC5B3D,OAAA,CAACL,aAAa;gBAASkF,SAAS,EAAC;cAAyB,GAAtCkB,CAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuC,CAAC,gBAE7DtF,OAAA,CAACf,QAAQ;gBAAS4F,SAAS,EAAC;cAAuB,GAApCkB,CAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CAExD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtF,OAAA;cAAM6E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GACzC3B,OAAO,CAACQ,MAAM,EAAC,IAAE,EAACR,OAAO,CAAC+C,OAAO,EAAC,GACrC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENtF,OAAA;YAAK6E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C9E,OAAA;cAAM6E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GAAC,GACxD,EAAC3B,OAAO,CAACM,KAAK;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EACNnC,OAAO,CAACgD,aAAa,IAAIhD,OAAO,CAACgD,aAAa,GAAGhD,OAAO,CAACM,KAAK,iBAC7DzD,OAAA;cAAM6E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GAAC,GAClD,EAAC3B,OAAO,CAACgD,aAAa;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLnC,OAAO,CAACN,IAAI,KAAK,SAAS,gBACzB7C,OAAA;YAAK6E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9E,OAAA;cAAK6E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9E,OAAA,CAACR,SAAS;gBAACqF,SAAS,EAAC;cAAwB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDtF,OAAA;gBAAM6E,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,EACLnC,OAAO,CAACiD,SAAS,iBAChBpG,OAAA;cAAK6E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9E,OAAA;gBAAM6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtF,OAAA;gBAAM6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE3B,OAAO,CAACiD,SAAS,CAAC5D,IAAI,CAAC,IAAI;cAAC;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENtF,OAAA,CAAAE,SAAA;YAAA4E,QAAA,GAEG3B,OAAO,CAACkD,MAAM,iBACbrG,OAAA;cAAK6E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9E,OAAA;gBAAM6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACrDnC,OAAO,CAACkD,MAAM,CAAC9D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACN,GAAG,CAAC,CAACqE,KAAK,EAAErC,KAAK,kBAC3CjE,OAAA;gBAEE6E,SAAS,EAAE,gEACTyB,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,GAAG,UAAU,GACnDA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,GAAG,UAAU,GACnDA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,MAAM,GAAG,aAAa,GACpDA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,KAAK,GAAG,YAAY,GACjDA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,GAAG,aAAa,GACxDA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,MAAM,GAAG,eAAe,GACtD,aAAa;cACZ,GATErC,KAAK;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUX,CACF,CAAC,EACDnC,OAAO,CAACkD,MAAM,CAACrE,MAAM,GAAG,CAAC,iBACxBhC,OAAA;gBAAM6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,GAAC,EAAC3B,OAAO,CAACkD,MAAM,CAACrE,MAAM,GAAG,CAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAC3E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAEDtF,OAAA;cAAK6E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C9E,OAAA,CAACP,SAAS;gBAACoF,SAAS,EAAC;cAAuB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CtF,OAAA;gBAAM6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpC,CAAAZ,iBAAA,GAAAf,OAAO,CAACoD,QAAQ,cAAArC,iBAAA,eAAhBA,iBAAA,CAAkBsC,IAAI,GAAG,eAAe,GAAG;cAAoB;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CACH,eAGDtF,OAAA;YAAK6E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C9E,OAAA,CAACN,eAAe;cAACmF,SAAS,EAAE,WAAW1B,OAAO,CAACyC,OAAO,GAAG,gBAAgB,GAAG,cAAc;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGtF,OAAA;cAAM6E,SAAS,EAAE,WAAW1B,OAAO,CAACyC,OAAO,GAAG,gBAAgB,GAAG,cAAc,EAAG;cAAAd,QAAA,GAC/E3B,OAAO,CAACyC,OAAO,GAAG,UAAU,GAAG,cAAc,EAC7CzC,OAAO,CAACsD,UAAU,IAAItD,OAAO,CAACyC,OAAO,IAAI,KAAKzC,OAAO,CAACsD,UAAU,aAAa;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA,CAACtB,MAAM,CAAC8G,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1Bb,SAAS,EAAC,yOAAyO;UAAAC,QAAA,gBAEnP9E,OAAA,CAACb,eAAe;YAAC0F,SAAS,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCtF,OAAA;YAAA8E,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA,CACd;EAED,oBACEtF,OAAA;IAAK6E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC9E,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC9E,OAAA;QAAK6E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D9E,OAAA;UAAK6E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD9E,OAAA,CAACnB,IAAI;YAAC6H,EAAE,EAAC,GAAG;YAAC7B,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBACpG9E,OAAA,CAACV,QAAQ;cAACuF,SAAS,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPtF,OAAA,CAACX,gBAAgB;YAACwF,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDtF,OAAA;YAAM6E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC9C1E,gBAAgB,KAAK,KAAK,iBACzBZ,OAAA,CAAAE,SAAA;YAAA4E,QAAA,gBACE9E,OAAA,CAACX,gBAAgB;cAACwF,SAAS,EAAC;YAAuB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDtF,OAAA;cAAM6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAChDrD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA,eACP,CACH,EACAvE,mBAAmB,KAAK,KAAK,iBAC5Bf,OAAA,CAAAE,SAAA;YAAA4E,QAAA,gBACE9E,OAAA,CAACX,gBAAgB;cAACwF,SAAS,EAAC;YAAuB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDtF,OAAA;cAAM6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAAzE,mBAAA,GAChDwB,aAAa,CAACH,IAAI,CAACQ,GAAG,IAAIA,GAAG,CAACN,EAAE,KAAKb,mBAAmB,CAAC,cAAAV,mBAAA,uBAAzDA,mBAAA,CAA2DyB;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAK6E,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/E9E,OAAA;QAAK6E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD9E,OAAA,CAACtB,MAAM,CAACyF,GAAG;UACTE,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BM,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvB9E,OAAA;YAAI6E,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAC3DlE,gBAAgB,KAAK,KAAK,GAAG,cAAc,GAAG,CAAAa,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,IAAI,KAAI;UAAU;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACLtF,OAAA;YAAG6E,SAAS,EAAC,iDAAiD;YAAAC,QAAA,EAC3DlE,gBAAgB,KAAK,KAAK,GACvB,qDAAqD,GACrD,CAAAa,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkF,WAAW,KAAI;UAA+B;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC9E,OAAA;QAAK6E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D9E,OAAA;UAAK6E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CAAC;YAAElD,EAAE,EAAE,KAAK;YAAEE,IAAI,EAAE,cAAc;YAAE8E,IAAI,EAAE;UAAM,CAAC,EAAE,GAAGhH,UAAU,CAAC,CAACqC,GAAG,CAAEoB,QAAQ,iBAC9ErD,OAAA,CAACtB,MAAM,CAAC8G,MAAM;YAEZC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BmB,OAAO,EAAEA,CAAA,KAAM;cACbhG,mBAAmB,CAACwC,QAAQ,CAACzB,EAAE,CAAC;cAChCZ,sBAAsB,CAAC,KAAK,CAAC;cAC7BT,eAAe,CAAC;gBAAE8C,QAAQ,EAAEA,QAAQ,CAACzB;cAAG,CAAC,CAAC;YAC5C,CAAE;YACFiD,SAAS,EAAE,iFACTjE,gBAAgB,KAAKyC,QAAQ,CAACzB,EAAE,GAC5B,0CAA0C,GAC1C,iFAAiF,EACpF;YAAAkD,QAAA,gBAEH9E,OAAA;cAAM6E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEzB,QAAQ,CAACuD;YAAI;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDtF,OAAA;cAAA8E,QAAA,EAAOzB,QAAQ,CAACvB;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAfvBjC,QAAQ,CAACzB,EAAE;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBH,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtF,OAAA;MAAK6E,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D9E,OAAA;QAAK6E,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9C9E,OAAA;UAAK6E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC9E,OAAA;YAAK6E,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAC/D9E,OAAA;cAAK6E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD9E,OAAA;gBAAI6E,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEtF,OAAA;gBACE6G,OAAO,EAAEA,CAAA,KAAMrF,cAAc,CAAC,CAACD,WAAW,CAAE;gBAC5CsD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eAEvC9E,OAAA,CAACZ,yBAAyB;kBAACyF,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENtF,OAAA;cAAK6E,SAAS,EAAE,aAAatD,WAAW,GAAG,OAAO,GAAG,iBAAiB,EAAG;cAAAuD,QAAA,gBAEvE9E,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAI6E,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DtF,OAAA;kBAAK6E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBlF,UAAU,CAACqC,GAAG,CAACoB,QAAQ,iBACtBrD,OAAA;oBAEE6G,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAACwC,QAAQ,CAACzB,EAAE,CAAE;oBAChDiD,SAAS,EAAE,2DACTjE,gBAAgB,KAAKyC,QAAQ,CAACzB,EAAE,GAC5B,2CAA2C,GAC3C,iCAAiC,EACpC;oBAAAkD,QAAA,eAEH9E,OAAA;sBAAK6E,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnC9E,OAAA;wBAAA8E,QAAA,EAAOzB,QAAQ,CAACvB;sBAAI;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC5BtF,OAAA;wBAAM6E,SAAS,EAAC,SAAS;wBAAAC,QAAA,GAAC,GAAC,EAACzB,QAAQ,CAACtB,KAAK,EAAC,GAAC;sBAAA;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAAC,GAXDjC,QAAQ,CAACzB,EAAE;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYV,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAI6E,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,GAAC,gBAC/B,EAAC3D,UAAU,CAAC,CAAC,CAAC,EAAC,MAAI,EAACA,UAAU,CAAC,CAAC,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACLtF,OAAA;kBACE6C,IAAI,EAAC,OAAO;kBACZiE,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAC,MAAM;kBACVhE,KAAK,EAAE5B,UAAU,CAAC,CAAC,CAAE;kBACrB6F,QAAQ,EAAGC,CAAC,IAAK7F,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC,EAAE+F,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACpE,KAAK,CAAC,CAAC,CAAE;kBAC1E8B,SAAS,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNtF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAI6E,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEtF,OAAA;kBAAK6E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC7C,GAAG,CAAC0B,MAAM,iBACzB3D,OAAA;oBAEE6G,OAAO,EAAEA,CAAA,KAAMvF,iBAAiB,CAACqC,MAAM,CAAE;oBACzCkB,SAAS,EAAE,6EACTxD,cAAc,KAAKsC,MAAM,GACrB,2CAA2C,GAC3C,iCAAiC,EACpC;oBAAAmB,QAAA,gBAEH9E,OAAA;sBAAK6E,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAAC6D,CAAC,EAAEC,CAAC,kBACtB/F,OAAA,CAACL,aAAa;wBAEZkF,SAAS,EAAE,WACTkB,CAAC,GAAGpC,MAAM,GAAG,iBAAiB,GAAG,eAAe;sBAC/C,GAHEoC,CAAC;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAIP,CACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNtF,OAAA;sBAAA8E,QAAA,EAAOnB,MAAM,GAAG,CAAC,GAAG,GAAGA,MAAM,SAAS,GAAG;oBAAa;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAlBzD3B,MAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmBL,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAK6E,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBAErB9E,OAAA;YAAK6E,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACtD9E,OAAA;cAAK6E,SAAS,EAAC,6EAA6E;cAAAC,QAAA,gBAC1F9E,OAAA;gBAAA8E,QAAA,eACE9E,OAAA;kBAAG6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,UACnB,EAAC7B,yBAAyB,CAACjB,MAAM,EAAC,MAAI,EAACnC,QAAQ,CAACmC,MAAM,EAAC,WACjE;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENtF,OAAA;gBAAK6E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAE1C9E,OAAA;kBACE+C,KAAK,EAAErC,MAAO;kBACdsG,QAAQ,EAAGC,CAAC,IAAKtG,SAAS,CAACsG,CAAC,CAACE,MAAM,CAACpE,KAAK,CAAE;kBAC3C8B,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,EAE/FhC,WAAW,CAACb,GAAG,CAACmF,MAAM,iBACrBpH,OAAA;oBAA2B+C,KAAK,EAAEqE,MAAM,CAACrE,KAAM;oBAAA+B,QAAA,EAC5CsC,MAAM,CAACpE;kBAAK,GADFoE,MAAM,CAACrE,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGTtF,OAAA;kBAAK6E,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC9C9E,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAMpG,WAAW,CAAC,MAAM,CAAE;oBACnCoE,SAAS,EAAE,oCACTrE,QAAQ,KAAK,MAAM,GACf,0CAA0C,GAC1C,eAAe,EAClB;oBAAAsE,QAAA,eAEH9E,OAAA,CAACjB,cAAc;sBAAC8F,SAAS,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACTtF,OAAA;oBACE6G,OAAO,EAAEA,CAAA,KAAMpG,WAAW,CAAC,MAAM,CAAE;oBACnCoE,SAAS,EAAE,oCACTrE,QAAQ,KAAK,MAAM,GACf,0CAA0C,GAC1C,eAAe,EAClB;oBAAAsE,QAAA,eAEH9E,OAAA,CAAChB,cAAc;sBAAC6F,SAAS,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA,CAACrB,eAAe;YAAC0I,IAAI,EAAC,MAAM;YAAAvC,QAAA,eAC1B9E,OAAA,CAACtB,MAAM,CAACyF,GAAG;cAETE,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBG,IAAI,EAAE;gBAAEH,OAAO,EAAE;cAAE,CAAE;cACrBO,SAAS,EAAE,GACTrE,QAAQ,KAAK,MAAM,GACf,sDAAsD,GACtD,WAAW,EACd;cAAAsE,QAAA,EAEF7B,yBAAyB,CAAChB,GAAG,CAAC,CAACkB,OAAO,EAAEc,KAAK,kBAC5CjE,OAAA,CAACgE,WAAW;gBAAkBb,OAAO,EAAEA,OAAQ;gBAACc,KAAK,EAAEA;cAAM,GAA3Cd,OAAO,CAACvB,EAAE;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmC,CAChE;YAAC,GAZG,GAAG9E,QAAQ,IAAII,gBAAgB,IAAIF,MAAM,EAAE;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAatC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEjBrC,yBAAyB,CAACjB,MAAM,KAAK,CAAC,iBACrChC,OAAA;YAAK6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9E,OAAA;cAAK6E,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACjC9E,OAAA,CAAClB,UAAU;gBAAC+F,SAAS,EAAC;cAAmB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNtF,OAAA;cAAI6E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EtF,OAAA;cAAG6E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAjeID,YAAY;EAAA,QACwBvB,eAAe;AAAA;AAAA0I,EAAA,GADnDnH,YAAY;AAmelB,eAAeA,YAAY;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}