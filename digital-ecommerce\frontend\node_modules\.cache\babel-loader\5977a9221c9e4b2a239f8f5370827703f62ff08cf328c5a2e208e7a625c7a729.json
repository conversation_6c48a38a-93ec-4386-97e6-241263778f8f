{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\PlaceholderPage.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport Button from '../components/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlaceholderPage = ({\n  title,\n  description,\n  icon: Icon = ExclamationTriangleIcon\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      className: \"text-center max-w-md mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-8\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          className: \"w-16 h-16 text-light-orange-500 mx-auto mb-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8 leading-relaxed\",\n          children: description || `The ${title.toLowerCase()} page is coming soon. We're working hard to bring you this feature.`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              icon: ArrowLeftIcon,\n              children: \"Back to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              fullWidth: true,\n              children: \"Contact Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n\n// Specific placeholder pages\n_c = PlaceholderPage;\nexport const HelpPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Help Center\",\n  description: \"Our comprehensive help center is being developed. For immediate assistance, please contact our support team.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 42,\n  columnNumber: 3\n}, this);\n_c2 = HelpPage;\nexport const ReturnsPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Returns & Exchanges\",\n  description: \"Learn about our hassle-free return policy and how to exchange your items. This page is currently under construction.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 49,\n  columnNumber: 3\n}, this);\n_c3 = ReturnsPage;\nexport const ShippingPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Shipping Information\",\n  description: \"Find detailed information about our shipping options, delivery times, and tracking. Coming soon!\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 56,\n  columnNumber: 3\n}, this);\n_c4 = ShippingPage;\nexport const TrackOrderPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Track Your Order\",\n  description: \"Track your order status and delivery progress. This feature is being developed and will be available soon.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 63,\n  columnNumber: 3\n}, this);\n_c5 = TrackOrderPage;\nexport const PrivacyPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Privacy Policy\",\n  description: \"Your privacy is important to us. Our detailed privacy policy is being finalized and will be available soon.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 70,\n  columnNumber: 3\n}, this);\n_c6 = PrivacyPage;\nexport const TermsPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Terms of Service\",\n  description: \"Review our terms and conditions for using our platform. This page is currently being updated.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 77,\n  columnNumber: 3\n}, this);\n_c7 = TermsPage;\nexport const CookiesPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Cookie Policy\",\n  description: \"Learn about how we use cookies to improve your browsing experience. Policy details coming soon.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 84,\n  columnNumber: 3\n}, this);\n_c8 = CookiesPage;\nexport const OrdersPage = () => /*#__PURE__*/_jsxDEV(PlaceholderPage, {\n  title: \"Order History\",\n  description: \"View your past orders and track current purchases. This feature is being developed.\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 91,\n  columnNumber: 3\n}, this);\n_c9 = OrdersPage;\nexport default PlaceholderPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"PlaceholderPage\");\n$RefreshReg$(_c2, \"HelpPage\");\n$RefreshReg$(_c3, \"ReturnsPage\");\n$RefreshReg$(_c4, \"ShippingPage\");\n$RefreshReg$(_c5, \"TrackOrderPage\");\n$RefreshReg$(_c6, \"PrivacyPage\");\n$RefreshReg$(_c7, \"TermsPage\");\n$RefreshReg$(_c8, \"CookiesPage\");\n$RefreshReg$(_c9, \"OrdersPage\");", "map": {"version": 3, "names": ["React", "motion", "Link", "ArrowLeftIcon", "ExclamationTriangleIcon", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PlaceholderPage", "title", "description", "icon", "Icon", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLowerCase", "to", "fullWidth", "variant", "_c", "HelpPage", "_c2", "ReturnsPage", "_c3", "ShippingPage", "_c4", "TrackOrderPage", "_c5", "PrivacyPage", "_c6", "TermsPage", "_c7", "CookiesPage", "_c8", "OrdersPage", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/PlaceholderPage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport Button from '../components/Button';\n\nconst PlaceholderPage = ({ title, description, icon: Icon = ExclamationTriangleIcon }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"text-center max-w-md mx-auto px-4\"\n      >\n        <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n          <Icon className=\"w-16 h-16 text-light-orange-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8 leading-relaxed\">\n            {description || `The ${title.toLowerCase()} page is coming soon. We're working hard to bring you this feature.`}\n          </p>\n          <div className=\"space-y-4\">\n            <Link to=\"/\">\n              <Button fullWidth icon={ArrowLeftIcon}>\n                Back to Home\n              </Button>\n            </Link>\n            <Link to=\"/contact\">\n              <Button variant=\"outline\" fullWidth>\n                Contact Support\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\n// Specific placeholder pages\nexport const HelpPage = () => (\n  <PlaceholderPage \n    title=\"Help Center\" \n    description=\"Our comprehensive help center is being developed. For immediate assistance, please contact our support team.\"\n  />\n);\n\nexport const ReturnsPage = () => (\n  <PlaceholderPage \n    title=\"Returns & Exchanges\" \n    description=\"Learn about our hassle-free return policy and how to exchange your items. This page is currently under construction.\"\n  />\n);\n\nexport const ShippingPage = () => (\n  <PlaceholderPage \n    title=\"Shipping Information\" \n    description=\"Find detailed information about our shipping options, delivery times, and tracking. Coming soon!\"\n  />\n);\n\nexport const TrackOrderPage = () => (\n  <PlaceholderPage \n    title=\"Track Your Order\" \n    description=\"Track your order status and delivery progress. This feature is being developed and will be available soon.\"\n  />\n);\n\nexport const PrivacyPage = () => (\n  <PlaceholderPage \n    title=\"Privacy Policy\" \n    description=\"Your privacy is important to us. Our detailed privacy policy is being finalized and will be available soon.\"\n  />\n);\n\nexport const TermsPage = () => (\n  <PlaceholderPage \n    title=\"Terms of Service\" \n    description=\"Review our terms and conditions for using our platform. This page is currently being updated.\"\n  />\n);\n\nexport const CookiesPage = () => (\n  <PlaceholderPage \n    title=\"Cookie Policy\" \n    description=\"Learn about how we use cookies to improve your browsing experience. Policy details coming soon.\"\n  />\n);\n\nexport const OrdersPage = () => (\n  <PlaceholderPage \n    title=\"Order History\" \n    description=\"View your past orders and track current purchases. This feature is being developed.\"\n  />\n);\n\nexport default PlaceholderPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,EAAEC,uBAAuB,QAAQ,6BAA6B;AACpF,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC,IAAI,EAAEC,IAAI,GAAGR;AAAwB,CAAC,KAAK;EACxF,oBACEG,OAAA;IAAKM,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eACvEP,OAAA,CAACN,MAAM,CAACc,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BR,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAE7CP,OAAA;QAAKM,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDP,OAAA,CAACK,IAAI;UAACC,SAAS,EAAC;QAA8C;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjElB,OAAA;UAAIM,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAEL;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClElB,OAAA;UAAGM,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAC9CJ,WAAW,IAAI,OAAOD,KAAK,CAACiB,WAAW,CAAC,CAAC;QAAqE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eACJlB,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBP,OAAA,CAACL,IAAI;YAACyB,EAAE,EAAC,GAAG;YAAAb,QAAA,eACVP,OAAA,CAACF,MAAM;cAACuB,SAAS;cAACjB,IAAI,EAAER,aAAc;cAAAW,QAAA,EAAC;YAEvC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPlB,OAAA,CAACL,IAAI;YAACyB,EAAE,EAAC,UAAU;YAAAb,QAAA,eACjBP,OAAA,CAACF,MAAM;cAACwB,OAAO,EAAC,SAAS;cAACD,SAAS;cAAAd,QAAA,EAAC;YAEpC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;;AAED;AAAAK,EAAA,GAjCMtB,eAAe;AAkCrB,OAAO,MAAMuB,QAAQ,GAAGA,CAAA,kBACtBxB,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,aAAa;EACnBC,WAAW,EAAC;AAA8G;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3H,CACF;AAACO,GAAA,GALWD,QAAQ;AAOrB,OAAO,MAAME,WAAW,GAAGA,CAAA,kBACzB1B,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,qBAAqB;EAC3BC,WAAW,EAAC;AAAsH;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnI,CACF;AAACS,GAAA,GALWD,WAAW;AAOxB,OAAO,MAAME,YAAY,GAAGA,CAAA,kBAC1B5B,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,sBAAsB;EAC5BC,WAAW,EAAC;AAAkG;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC/G,CACF;AAACW,GAAA,GALWD,YAAY;AAOzB,OAAO,MAAME,cAAc,GAAGA,CAAA,kBAC5B9B,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,kBAAkB;EACxBC,WAAW,EAAC;AAA4G;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACzH,CACF;AAACa,GAAA,GALWD,cAAc;AAO3B,OAAO,MAAME,WAAW,GAAGA,CAAA,kBACzBhC,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,gBAAgB;EACtBC,WAAW,EAAC;AAA6G;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1H,CACF;AAACe,GAAA,GALWD,WAAW;AAOxB,OAAO,MAAME,SAAS,GAAGA,CAAA,kBACvBlC,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,kBAAkB;EACxBC,WAAW,EAAC;AAA+F;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5G,CACF;AAACiB,GAAA,GALWD,SAAS;AAOtB,OAAO,MAAME,WAAW,GAAGA,CAAA,kBACzBpC,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,eAAe;EACrBC,WAAW,EAAC;AAAiG;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC9G,CACF;AAACmB,GAAA,GALWD,WAAW;AAOxB,OAAO,MAAME,UAAU,GAAGA,CAAA,kBACxBtC,OAAA,CAACC,eAAe;EACdC,KAAK,EAAC,eAAe;EACrBC,WAAW,EAAC;AAAqF;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClG,CACF;AAACqB,GAAA,GALWD,UAAU;AAOvB,eAAerC,eAAe;AAAC,IAAAsB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}