{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\",\n      children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/products\",\n            element: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/digital-products\",\n            element: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/_jsxDEV(DigitalProductsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/about\",\n            element: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/contact\",\n            element: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"bg-gradient-to-r from-gray-900 to-gray-800 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-1 md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-white\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 84,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold\",\n                  children: \"ShopHub\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-300 mb-4 max-w-md\",\n                children: \"Your premier destination for quality products and exceptional shopping experiences. We're committed to bringing you the best deals and customer service.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(MultiLanguageSupport, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(EmailNotifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Home\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/products\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Products\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/digital-products\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Digital Products\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/about\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"About Us\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/contact\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Contact\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Customer Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Help Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Returns\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Shipping Info\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: \"#\",\n                    className: \"text-gray-300 hover:text-light-orange-400 transition-colors\",\n                    children: \"Track Order\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"\\xA9 2024 ShopHub. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-6 mt-4 md:mt-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\",\n                children: \"Cookie Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "motion", "AnimatePresence", "Navigation", "HomePage", "ProductsPage", "DigitalProductsPage", "AboutPage", "ContactPage", "MultiLanguageSupport", "EmailNotifications", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "path", "element", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Navigation from './components/Navigation';\nimport HomePage from './pages/HomePage';\nimport ProductsPage from './pages/ProductsPage';\nimport DigitalProductsPage from './pages/DigitalProductsPage';\nimport AboutPage from './pages/AboutPage';\nimport ContactPage from './pages/ContactPage';\nimport MultiLanguageSupport from './components/MultiLanguageSupport';\nimport EmailNotifications from './components/EmailNotifications';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white\">\n        <Navigation />\n\n        <AnimatePresence mode=\"wait\">\n          <Routes>\n            <Route path=\"/\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <HomePage />\n              </motion.div>\n            } />\n            <Route path=\"/products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/digital-products\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <DigitalProductsPage />\n              </motion.div>\n            } />\n            <Route path=\"/about\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AboutPage />\n              </motion.div>\n            } />\n            <Route path=\"/contact\" element={\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ duration: 0.3 }}\n              >\n                <ContactPage />\n              </motion.div>\n            } />\n          </Routes>\n        </AnimatePresence>\n\n        {/* Enhanced Footer */}\n        <footer className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {/* Company Info */}\n              <div className=\"col-span-1 md:col-span-2\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                    </svg>\n                  </div>\n                  <span className=\"text-2xl font-bold\">ShopHub</span>\n                </div>\n                <p className=\"text-gray-300 mb-4 max-w-md\">\n                  Your premier destination for quality products and exceptional shopping experiences.\n                  We're committed to bringing you the best deals and customer service.\n                </p>\n                <div className=\"flex space-x-4\">\n                  <MultiLanguageSupport />\n                  <EmailNotifications />\n                </div>\n              </div>\n\n              {/* Quick Links */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n                <ul className=\"space-y-2\">\n                  <li><Link to=\"/\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Home</Link></li>\n                  <li><Link to=\"/products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Products</Link></li>\n                  <li><Link to=\"/digital-products\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Digital Products</Link></li>\n                  <li><Link to=\"/about\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">About Us</Link></li>\n                  <li><Link to=\"/contact\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Contact</Link></li>\n                </ul>\n              </div>\n\n              {/* Customer Service */}\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">Customer Service</h3>\n                <ul className=\"space-y-2\">\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Help Center</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Returns</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Shipping Info</a></li>\n                  <li><a href=\"#\" className=\"text-gray-300 hover:text-light-orange-400 transition-colors\">Track Order</a></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 ShopHub. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 mt-4 md:mt-0\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Privacy Policy</a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Terms of Service</a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-light-orange-400 transition-colors text-sm\">Cookie Policy</a>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACf,MAAM;IAAAiB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,8DAA8D;MAAAD,QAAA,gBAC3EF,OAAA,CAACT,UAAU;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEdP,OAAA,CAACV,eAAe;QAACkB,IAAI,EAAC,MAAM;QAAAN,QAAA,eAC1BF,OAAA,CAACd,MAAM;UAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;YAACsB,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBV,OAAA,CAACX,MAAM,CAACsB,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE9BF,OAAA,CAACR,QAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACb,KAAK;YAACsB,IAAI,EAAC,WAAW;YAACC,OAAO,eAC7BV,OAAA,CAACX,MAAM,CAACsB,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE9BF,OAAA,CAACP,YAAY;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACb,KAAK;YAACsB,IAAI,EAAC,mBAAmB;YAACC,OAAO,eACrCV,OAAA,CAACX,MAAM,CAACsB,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE9BF,OAAA,CAACN,mBAAmB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACb,KAAK;YAACsB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BV,OAAA,CAACX,MAAM,CAACsB,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE9BF,OAAA,CAACL,SAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJP,OAAA,CAACb,KAAK;YAACsB,IAAI,EAAC,UAAU;YAACC,OAAO,eAC5BV,OAAA,CAACX,MAAM,CAACsB,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,IAAI,EAAE;gBAAEH,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BG,UAAU,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAhB,QAAA,eAE9BF,OAAA,CAACJ,WAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGlBP,OAAA;QAAQG,SAAS,EAAC,uDAAuD;QAAAD,QAAA,eACvEF,OAAA;UAAKG,SAAS,EAAC,8CAA8C;UAAAD,QAAA,gBAC3DF,OAAA;YAAKG,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBAEpDF,OAAA;cAAKG,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvCF,OAAA;gBAAKG,SAAS,EAAC,kCAAkC;gBAAAD,QAAA,gBAC/CF,OAAA;kBAAKG,SAAS,EAAC,oHAAoH;kBAAAD,QAAA,eACjIF,OAAA;oBAAKG,SAAS,EAAC,oBAAoB;oBAACgB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAnB,QAAA,eACvFF,OAAA;sBAAMsB,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA4C;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNP,OAAA;kBAAMG,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNP,OAAA;gBAAGG,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EAAC;cAG3C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAKG,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7BF,OAAA,CAACH,oBAAoB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBP,OAAA,CAACF,kBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAIG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DP,OAAA;gBAAIG,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACvBF,OAAA;kBAAAE,QAAA,eAAIF,OAAA,CAACZ,IAAI;oBAACsC,EAAE,EAAC,GAAG;oBAACvB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzGP,OAAA;kBAAAE,QAAA,eAAIF,OAAA,CAACZ,IAAI;oBAACsC,EAAE,EAAC,WAAW;oBAACvB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrHP,OAAA;kBAAAE,QAAA,eAAIF,OAAA,CAACZ,IAAI;oBAACsC,EAAE,EAAC,mBAAmB;oBAACvB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrIP,OAAA;kBAAAE,QAAA,eAAIF,OAAA,CAACZ,IAAI;oBAACsC,EAAE,EAAC,QAAQ;oBAACvB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClHP,OAAA;kBAAAE,QAAA,eAAIF,OAAA,CAACZ,IAAI;oBAACsC,EAAE,EAAC,UAAU;oBAACvB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGNP,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAIG,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEP,OAAA;gBAAIG,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACvBF,OAAA;kBAAAE,QAAA,eAAIF,OAAA;oBAAG2B,IAAI,EAAC,GAAG;oBAACxB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5GP,OAAA;kBAAAE,QAAA,eAAIF,OAAA;oBAAG2B,IAAI,EAAC,GAAG;oBAACxB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxGP,OAAA;kBAAAE,QAAA,eAAIF,OAAA;oBAAG2B,IAAI,EAAC,GAAG;oBAACxB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9GP,OAAA;kBAAAE,QAAA,eAAIF,OAAA;oBAAG2B,IAAI,EAAC,GAAG;oBAACxB,SAAS,EAAC,6DAA6D;oBAAAD,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENP,OAAA;YAAKG,SAAS,EAAC,2FAA2F;YAAAD,QAAA,gBACxGF,OAAA;cAAGG,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAErC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAKG,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1CF,OAAA;gBAAG2B,IAAI,EAAC,GAAG;gBAACxB,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9GP,OAAA;gBAAG2B,IAAI,EAAC,GAAG;gBAACxB,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChHP,OAAA;gBAAG2B,IAAI,EAAC,GAAG;gBAACxB,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACqB,EAAA,GA5HQ3B,GAAG;AA8HZ,eAAeA,GAAG;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}