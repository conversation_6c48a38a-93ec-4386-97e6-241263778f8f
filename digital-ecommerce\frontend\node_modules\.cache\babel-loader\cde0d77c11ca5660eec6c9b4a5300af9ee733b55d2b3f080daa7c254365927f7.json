{"ast": null, "code": "/*#__NO_SIDE_EFFECTS__*/\nconst noop = any => any;\nexport { noop };", "map": {"version": 3, "names": ["noop", "any"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "mappings": "AAAA;AACA,MAAMA,IAAI,GAAIC,GAAG,IAAKA,GAAG;AAEzB,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}