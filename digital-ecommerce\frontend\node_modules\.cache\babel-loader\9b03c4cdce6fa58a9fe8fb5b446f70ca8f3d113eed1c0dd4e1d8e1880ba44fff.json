{"ast": null, "code": "const floatRegex = /-?(?:[0-9]+(?:\\.[0-9]+)?|\\.[0-9]+)/g;\nexport { floatRegex };", "map": {"version": 3, "names": ["floatRegex"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs"], "sourcesContent": ["const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,qCAA6B;AAEhD,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}