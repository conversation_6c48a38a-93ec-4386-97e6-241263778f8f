{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"title\", \"titleId\"];\nimport * as React from \"react\";\nfunction FolderIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M19.5 21a3 3 0 0 0 3-3v-4.5a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3V18a3 3 0 0 0 3 3h15ZM1.5 10.146V6a3 3 0 0 1 3-3h5.379a2.25 2.25 0 0 1 1.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 0 1 3 3v1.146A4.483 4.483 0 0 0 19.5 9h-15a4.483 4.483 0 0 0-3 1.146Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(FolderIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "FolderIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "d", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@heroicons/react/24/solid/esm/FolderIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction FolderIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M19.5 21a3 3 0 0 0 3-3v-4.5a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3V18a3 3 0 0 0 3 3h15ZM1.5 10.146V6a3 3 0 0 1 3-3h5.379a2.25 2.25 0 0 1 1.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 0 1 3 3v1.146A4.483 4.483 0 0 0 19.5 9h-15a4.483 4.483 0 0 0-3 1.146Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(FolderIcon);\nexport default ForwardRef;"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAUA,CAAAC,IAAA,EAIhBC,MAAM,EAAE;EAAA,IAJS;MAClBC,KAAK;MACLC;IAEF,CAAC,GAAAH,IAAA;IADII,KAAK,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAER,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEZ,MAAM;IACX,iBAAiB,EAAEE;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaJ,KAAK,CAACS,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEX;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaJ,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IACzDQ,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAclB,KAAK,CAACmB,UAAU,CAAClB,UAAU,CAAC;AAC7D,eAAeiB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}