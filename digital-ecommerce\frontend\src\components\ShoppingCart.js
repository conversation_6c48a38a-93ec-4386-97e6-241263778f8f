import React, { useState, createContext, useContext } from 'react';
import { ShoppingCartIcon, TrashIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

// Create Cart Context
const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

const initialCartItems = [
  {
    id: 'elec-001',
    name: 'MacBook Pro 16" M3 Max',
    price: 3499.99,
    quantity: 1,
    image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300',
    type: 'physical'
  },
  {
    id: 'soft-001',
    name: 'Microsoft Office 365 Personal',
    price: 69.99,
    quantity: 1,
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300',
    type: 'digital'
  }
];

// Cart Provider Component
export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState(initialCartItems);

  const totalPrice = cartItems.reduce((total, item) => total + item.price * item.quantity, 0);
  const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);

  const addToCart = (product, quantity = 1) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === product.id);
      if (existingItem) {
        return prevItems.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        return [...prevItems, {
          id: product.id,
          name: product.name,
          price: product.price,
          quantity,
          image: product.images ? product.images[0] : product.image,
          type: product.type || 'physical'
        }];
      }
    });
  };

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(id);
      return;
    }
    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const removeItem = (id) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== id));
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const value = {
    cartItems,
    totalPrice,
    totalItems,
    addToCart,
    updateQuantity,
    removeItem,
    clearCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

const ShoppingCart = () => {
  const { cartItems, totalPrice, totalItems, updateQuantity, removeItem } = useCart();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      {/* Cart Icon Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="relative p-2 text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 rounded-full transition-colors duration-300"
      >
        <ShoppingCartIcon className="w-6 h-6" />
        {totalItems > 0 && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center"
          >
            {totalItems}
          </motion.span>
        )}
      </motion.button>

      {/* Cart Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50"
          >
          {/* Header */}
          <div className="bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white flex items-center">
                <ShoppingCartIcon className="w-6 h-6 mr-2" />
                Shopping Cart
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-light-orange-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Cart Items */}
          <div className="max-h-96 overflow-y-auto">
            {cartItems.length === 0 ? (
              <div className="p-8 text-center">
                <ShoppingCartIcon className="w-16 h-16 text-light-orange-300 mx-auto mb-4" />
                <p className="text-light-orange-600 text-lg">Your cart is empty</p>
                <p className="text-light-orange-500 text-sm mt-2">Add some products to get started!</p>
              </div>
            ) : (
              <div className="p-4 space-y-4">
                {cartItems.map(item => (
                  <div key={item.id} className="flex items-center space-x-4 p-3 bg-light-orange-50 rounded-lg border border-light-orange-100">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded-lg shadow-sm"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-light-orange-800 truncate">
                        {item.name}
                      </h3>
                      <p className="text-light-orange-600 font-medium">
                        ${item.price.toFixed(2)}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        disabled={item.quantity <= 1}
                        className={`p-1 rounded-full transition-colors ${
                          item.quantity <= 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-light-orange-200 text-light-orange-700 hover:bg-light-orange-300'
                        }`}
                      >
                        <MinusIcon className="w-4 h-4" />
                      </motion.button>
                      <span className="w-8 text-center font-semibold text-light-orange-800">
                        {item.quantity}
                      </span>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        disabled={item.quantity >= 10}
                        className={`p-1 rounded-full transition-colors ${
                          item.quantity >= 10
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-light-orange-200 text-light-orange-700 hover:bg-light-orange-300'
                        }`}
                      >
                        <PlusIcon className="w-4 h-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => removeItem(item.id)}
                        className="p-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors ml-2"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </motion.button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {cartItems.length > 0 && (
            <div className="p-6 border-t border-light-orange-200 bg-light-orange-50 rounded-b-xl">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-bold text-light-orange-800">Total:</span>
                <span className="text-2xl font-bold text-light-orange-700">
                  ${totalPrice.toFixed(2)}
                </span>
              </div>
              <motion.button
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to checkout page
                  window.location.href = '/checkout';
                }}
                className="w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md hover:shadow-lg"
              >
                Proceed to Checkout (${totalPrice.toFixed(2)})
              </motion.button>
            </div>
          )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ShoppingCart;
