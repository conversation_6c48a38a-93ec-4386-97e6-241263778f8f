{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\contexts\\\\UserContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContext = /*#__PURE__*/createContext();\nexport const useUser = () => {\n  _s();\n  const context = useContext(UserContext);\n  if (!context) {\n    throw new Error('useUser must be used within a UserProvider');\n  }\n  return context;\n};\n\n// Mock user data for demonstration\n_s(useUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst mockUsers = [{\n  id: 'user-001',\n  email: '<EMAIL>',\n  password: 'password123',\n  // In real app, this would be hashed\n  firstName: 'John',\n  lastName: 'Doe',\n  phone: '+****************',\n  profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n  addresses: [{\n    id: 'addr-001',\n    type: 'shipping',\n    isDefault: true,\n    firstName: 'John',\n    lastName: 'Doe',\n    address: '123 Main Street',\n    city: 'New York',\n    state: 'NY',\n    zipCode: '10001',\n    country: 'United States'\n  }],\n  paymentMethods: [{\n    id: 'payment-001',\n    type: 'credit_card',\n    isDefault: true,\n    last4: '4242',\n    brand: 'Visa',\n    expiryMonth: 12,\n    expiryYear: 2025\n  }],\n  preferences: {\n    emailNotifications: true,\n    smsNotifications: false,\n    marketingEmails: true,\n    currency: 'USD',\n    language: 'en'\n  },\n  orderHistory: [],\n  wishlist: [],\n  createdAt: '2024-01-15T10:30:00Z',\n  lastLogin: '2024-01-20T14:22:00Z',\n  isVerified: true\n}];\nexport const UserProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('userData');\n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n        }\n      }\n      setIsLoading(false);\n    };\n    checkAuthStatus();\n  }, []);\n  const login = async (email, password, rememberMe = false) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Find user in mock data\n      const foundUser = mockUsers.find(u => u.email === email && u.password === password);\n      if (!foundUser) {\n        throw new Error('Invalid email or password');\n      }\n\n      // Generate mock token\n      const token = `mock_token_${Date.now()}`;\n\n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('userData', JSON.stringify(foundUser));\n      } else {\n        sessionStorage.setItem('authToken', token);\n        sessionStorage.setItem('userData', JSON.stringify(foundUser));\n      }\n      setUser(foundUser);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      return {\n        success: true,\n        user: foundUser\n      };\n    } catch (error) {\n      setIsLoading(false);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const register = async userData => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Check if user already exists\n      const existingUser = mockUsers.find(u => u.email === userData.email);\n      if (existingUser) {\n        throw new Error('User with this email already exists');\n      }\n\n      // Create new user\n      const newUser = {\n        id: `user-${Date.now()}`,\n        email: userData.email,\n        password: userData.password,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone || '',\n        profilePicture: '',\n        addresses: [],\n        paymentMethods: [],\n        preferences: {\n          emailNotifications: true,\n          smsNotifications: false,\n          marketingEmails: userData.marketingEmails || false,\n          currency: 'USD',\n          language: 'en'\n        },\n        orderHistory: [],\n        wishlist: [],\n        createdAt: new Date().toISOString(),\n        lastLogin: new Date().toISOString(),\n        isVerified: false\n      };\n\n      // Add to mock users (in real app, this would be sent to server)\n      mockUsers.push(newUser);\n      setIsLoading(false);\n      return {\n        success: true,\n        message: 'Account created successfully. Please check your email for verification.'\n      };\n    } catch (error) {\n      setIsLoading(false);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n  const updateProfile = async updatedData => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 800));\n      const updatedUser = {\n        ...user,\n        ...updatedData\n      };\n\n      // Update stored data\n      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');\n      if (token) {\n        if (localStorage.getItem('authToken')) {\n          localStorage.setItem('userData', JSON.stringify(updatedUser));\n        } else {\n          sessionStorage.setItem('userData', JSON.stringify(updatedUser));\n        }\n      }\n      setUser(updatedUser);\n      setIsLoading(false);\n      return {\n        success: true,\n        user: updatedUser\n      };\n    } catch (error) {\n      setIsLoading(false);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const resetPassword = async email => {\n    setIsLoading(true);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Check if user exists\n      const foundUser = mockUsers.find(u => u.email === email);\n      if (!foundUser) {\n        throw new Error('No account found with this email address');\n      }\n      setIsLoading(false);\n      return {\n        success: true,\n        message: 'Password reset link sent to your email'\n      };\n    } catch (error) {\n      setIsLoading(false);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const addToWishlist = productId => {\n    if (!user) return;\n    const updatedWishlist = [...user.wishlist];\n    if (!updatedWishlist.includes(productId)) {\n      updatedWishlist.push(productId);\n      updateProfile({\n        wishlist: updatedWishlist\n      });\n    }\n  };\n  const removeFromWishlist = productId => {\n    if (!user) return;\n    const updatedWishlist = user.wishlist.filter(id => id !== productId);\n    updateProfile({\n      wishlist: updatedWishlist\n    });\n  };\n  const isInWishlist = productId => {\n    var _user$wishlist;\n    return (user === null || user === void 0 ? void 0 : (_user$wishlist = user.wishlist) === null || _user$wishlist === void 0 ? void 0 : _user$wishlist.includes(productId)) || false;\n  };\n  const value = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    updateProfile,\n    resetPassword,\n    addToWishlist,\n    removeFromWishlist,\n    isInWishlist\n  };\n  return /*#__PURE__*/_jsxDEV(UserContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n};\n_s2(UserProvider, \"BKa16Kz0rM4B0y8AT6EXjU1HOY4=\");\n_c = UserProvider;\nexport default UserContext;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UserContext", "useUser", "_s", "context", "Error", "mockUsers", "id", "email", "password", "firstName", "lastName", "phone", "profilePicture", "addresses", "type", "isDefault", "address", "city", "state", "zipCode", "country", "paymentMethods", "last4", "brand", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "preferences", "emailNotifications", "smsNotifications", "marketingEmails", "currency", "language", "orderHistory", "wishlist", "createdAt", "lastLogin", "isVerified", "UserProvider", "children", "_s2", "user", "setUser", "isLoading", "setIsLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "rememberMe", "Promise", "resolve", "setTimeout", "foundUser", "find", "u", "Date", "now", "setItem", "stringify", "sessionStorage", "success", "message", "register", "existingUser", "newUser", "toISOString", "push", "logout", "updateProfile", "updatedData", "updatedUser", "resetPassword", "addToWishlist", "productId", "updatedWishlist", "includes", "removeFromWishlist", "filter", "isInWishlist", "_user$wishlist", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/contexts/UserContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst UserContext = createContext();\n\nexport const useUser = () => {\n  const context = useContext(UserContext);\n  if (!context) {\n    throw new Error('useUser must be used within a UserProvider');\n  }\n  return context;\n};\n\n// Mock user data for demonstration\nconst mockUsers = [\n  {\n    id: 'user-001',\n    email: '<EMAIL>',\n    password: 'password123', // In real app, this would be hashed\n    firstName: '<PERSON>',\n    lastName: 'Doe',\n    phone: '+****************',\n    profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',\n    addresses: [\n      {\n        id: 'addr-001',\n        type: 'shipping',\n        isDefault: true,\n        firstName: 'John',\n        lastName: 'Doe',\n        address: '123 Main Street',\n        city: 'New York',\n        state: 'NY',\n        zipCode: '10001',\n        country: 'United States'\n      }\n    ],\n    paymentMethods: [\n      {\n        id: 'payment-001',\n        type: 'credit_card',\n        isDefault: true,\n        last4: '4242',\n        brand: 'Visa',\n        expiryMonth: 12,\n        expiryYear: 2025\n      }\n    ],\n    preferences: {\n      emailNotifications: true,\n      smsNotifications: false,\n      marketingEmails: true,\n      currency: 'USD',\n      language: 'en'\n    },\n    orderHistory: [],\n    wishlist: [],\n    createdAt: '2024-01-15T10:30:00Z',\n    lastLogin: '2024-01-20T14:22:00Z',\n    isVerified: true\n  }\n];\n\nexport const UserProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      const token = localStorage.getItem('authToken');\n      const userData = localStorage.getItem('userData');\n      \n      if (token && userData) {\n        try {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n        } catch (error) {\n          console.error('Error parsing user data:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  const login = async (email, password, rememberMe = false) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Find user in mock data\n      const foundUser = mockUsers.find(u => u.email === email && u.password === password);\n      \n      if (!foundUser) {\n        throw new Error('Invalid email or password');\n      }\n\n      // Generate mock token\n      const token = `mock_token_${Date.now()}`;\n      \n      // Store auth data\n      if (rememberMe) {\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('userData', JSON.stringify(foundUser));\n      } else {\n        sessionStorage.setItem('authToken', token);\n        sessionStorage.setItem('userData', JSON.stringify(foundUser));\n      }\n\n      setUser(foundUser);\n      setIsAuthenticated(true);\n      setIsLoading(false);\n      \n      return { success: true, user: foundUser };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const register = async (userData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Check if user already exists\n      const existingUser = mockUsers.find(u => u.email === userData.email);\n      if (existingUser) {\n        throw new Error('User with this email already exists');\n      }\n\n      // Create new user\n      const newUser = {\n        id: `user-${Date.now()}`,\n        email: userData.email,\n        password: userData.password,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone || '',\n        profilePicture: '',\n        addresses: [],\n        paymentMethods: [],\n        preferences: {\n          emailNotifications: true,\n          smsNotifications: false,\n          marketingEmails: userData.marketingEmails || false,\n          currency: 'USD',\n          language: 'en'\n        },\n        orderHistory: [],\n        wishlist: [],\n        createdAt: new Date().toISOString(),\n        lastLogin: new Date().toISOString(),\n        isVerified: false\n      };\n\n      // Add to mock users (in real app, this would be sent to server)\n      mockUsers.push(newUser);\n\n      setIsLoading(false);\n      return { success: true, message: 'Account created successfully. Please check your email for verification.' };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    \n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  const updateProfile = async (updatedData) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 800));\n      \n      const updatedUser = { ...user, ...updatedData };\n      \n      // Update stored data\n      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');\n      if (token) {\n        if (localStorage.getItem('authToken')) {\n          localStorage.setItem('userData', JSON.stringify(updatedUser));\n        } else {\n          sessionStorage.setItem('userData', JSON.stringify(updatedUser));\n        }\n      }\n\n      setUser(updatedUser);\n      setIsLoading(false);\n      \n      return { success: true, user: updatedUser };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const resetPassword = async (email) => {\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Check if user exists\n      const foundUser = mockUsers.find(u => u.email === email);\n      if (!foundUser) {\n        throw new Error('No account found with this email address');\n      }\n\n      setIsLoading(false);\n      return { success: true, message: 'Password reset link sent to your email' };\n    } catch (error) {\n      setIsLoading(false);\n      return { success: false, error: error.message };\n    }\n  };\n\n  const addToWishlist = (productId) => {\n    if (!user) return;\n    \n    const updatedWishlist = [...user.wishlist];\n    if (!updatedWishlist.includes(productId)) {\n      updatedWishlist.push(productId);\n      updateProfile({ wishlist: updatedWishlist });\n    }\n  };\n\n  const removeFromWishlist = (productId) => {\n    if (!user) return;\n    \n    const updatedWishlist = user.wishlist.filter(id => id !== productId);\n    updateProfile({ wishlist: updatedWishlist });\n  };\n\n  const isInWishlist = (productId) => {\n    return user?.wishlist?.includes(productId) || false;\n  };\n\n  const value = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    updateProfile,\n    resetPassword,\n    addToWishlist,\n    removeFromWishlist,\n    isInWishlist\n  };\n\n  return (\n    <UserContext.Provider value={value}>\n      {children}\n    </UserContext.Provider>\n  );\n};\n\nexport default UserContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,MAAMI,SAAS,GAAG,CAChB;EACEC,EAAE,EAAE,UAAU;EACdC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,aAAa;EAAE;EACzBC,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,mBAAmB;EAC1BC,cAAc,EAAE,oEAAoE;EACpFC,SAAS,EAAE,CACT;IACEP,EAAE,EAAE,UAAU;IACdQ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,IAAI;IACfN,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,KAAK;IACfM,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAE;EACX,CAAC,CACF;EACDC,cAAc,EAAE,CACd;IACEf,EAAE,EAAE,aAAa;IACjBQ,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE,IAAI;IACfO,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC,CACF;EACDC,WAAW,EAAE;IACXC,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,KAAK;IACvBC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC;EACDC,YAAY,EAAE,EAAE;EAChBC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,sBAAsB;EACjCC,SAAS,EAAE,sBAAsB;EACjCC,UAAU,EAAE;AACd,CAAC,CACF;AAED,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiD,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAEjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;QACrB,IAAI;UACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCT,OAAO,CAACU,UAAU,CAAC;UACnBN,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,CAAC,OAAOS,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;UACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;QACrC;MACF;MACAb,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,KAAK,GAAG,MAAAA,CAAOlD,KAAK,EAAEC,QAAQ,EAAEkD,UAAU,GAAG,KAAK,KAAK;IAC3Df,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,SAAS,GAAGzD,SAAS,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,KAAK,KAAKA,KAAK,IAAIyD,CAAC,CAACxD,QAAQ,KAAKA,QAAQ,CAAC;MAEnF,IAAI,CAACsD,SAAS,EAAE;QACd,MAAM,IAAI1D,KAAK,CAAC,2BAA2B,CAAC;MAC9C;;MAEA;MACA,MAAM2C,KAAK,GAAG,cAAckB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;;MAExC;MACA,IAAIR,UAAU,EAAE;QACdV,YAAY,CAACmB,OAAO,CAAC,WAAW,EAAEpB,KAAK,CAAC;QACxCC,YAAY,CAACmB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACN,SAAS,CAAC,CAAC;MAC7D,CAAC,MAAM;QACLO,cAAc,CAACF,OAAO,CAAC,WAAW,EAAEpB,KAAK,CAAC;QAC1CsB,cAAc,CAACF,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACN,SAAS,CAAC,CAAC;MAC/D;MAEArB,OAAO,CAACqB,SAAS,CAAC;MAClBjB,kBAAkB,CAAC,IAAI,CAAC;MACxBF,YAAY,CAAC,KAAK,CAAC;MAEnB,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAE9B,IAAI,EAAEsB;MAAU,CAAC;IAC3C,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdX,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOtB,QAAQ,IAAK;IACnCP,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAMa,YAAY,GAAGpE,SAAS,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,KAAK,KAAK2C,QAAQ,CAAC3C,KAAK,CAAC;MACpE,IAAIkE,YAAY,EAAE;QAChB,MAAM,IAAIrE,KAAK,CAAC,qCAAqC,CAAC;MACxD;;MAEA;MACA,MAAMsE,OAAO,GAAG;QACdpE,EAAE,EAAE,QAAQ2D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACxB3D,KAAK,EAAE2C,QAAQ,CAAC3C,KAAK;QACrBC,QAAQ,EAAE0C,QAAQ,CAAC1C,QAAQ;QAC3BC,SAAS,EAAEyC,QAAQ,CAACzC,SAAS;QAC7BC,QAAQ,EAAEwC,QAAQ,CAACxC,QAAQ;QAC3BC,KAAK,EAAEuC,QAAQ,CAACvC,KAAK,IAAI,EAAE;QAC3BC,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE,EAAE;QACbQ,cAAc,EAAE,EAAE;QAClBK,WAAW,EAAE;UACXC,kBAAkB,EAAE,IAAI;UACxBC,gBAAgB,EAAE,KAAK;UACvBC,eAAe,EAAEqB,QAAQ,CAACrB,eAAe,IAAI,KAAK;UAClDC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE;QACZ,CAAC;QACDC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,IAAI+B,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC;QACnCxC,SAAS,EAAE,IAAI8B,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC;QACnCvC,UAAU,EAAE;MACd,CAAC;;MAED;MACA/B,SAAS,CAACuE,IAAI,CAACF,OAAO,CAAC;MAEvB/B,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA0E,CAAC;IAC9G,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdX,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACnB7B,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;IACpCR,YAAY,CAACQ,UAAU,CAAC,UAAU,CAAC;IACnCa,cAAc,CAACb,UAAU,CAAC,WAAW,CAAC;IACtCa,cAAc,CAACb,UAAU,CAAC,UAAU,CAAC;IAErCf,OAAO,CAAC,IAAI,CAAC;IACbI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMiC,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3CpC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,MAAMoB,WAAW,GAAG;QAAE,GAAGxC,IAAI;QAAE,GAAGuC;MAAY,CAAC;;MAE/C;MACA,MAAMhC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAIoB,cAAc,CAACpB,OAAO,CAAC,WAAW,CAAC;MACtF,IAAIF,KAAK,EAAE;QACT,IAAIC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;UACrCD,YAAY,CAACmB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACY,WAAW,CAAC,CAAC;QAC/D,CAAC,MAAM;UACLX,cAAc,CAACF,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACY,WAAW,CAAC,CAAC;QACjE;MACF;MAEAvC,OAAO,CAACuC,WAAW,CAAC;MACpBrC,YAAY,CAAC,KAAK,CAAC;MAEnB,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAE9B,IAAI,EAAEwC;MAAY,CAAC;IAC7C,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdX,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMU,aAAa,GAAG,MAAO1E,KAAK,IAAK;IACrCoC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,SAAS,GAAGzD,SAAS,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,KAAK,KAAKA,KAAK,CAAC;MACxD,IAAI,CAACuD,SAAS,EAAE;QACd,MAAM,IAAI1D,KAAK,CAAC,0CAA0C,CAAC;MAC7D;MAEAuC,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAyC,CAAC;IAC7E,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdX,YAAY,CAAC,KAAK,CAAC;MACnB,OAAO;QAAE2B,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMW,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAAC3C,IAAI,EAAE;IAEX,MAAM4C,eAAe,GAAG,CAAC,GAAG5C,IAAI,CAACP,QAAQ,CAAC;IAC1C,IAAI,CAACmD,eAAe,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MACxCC,eAAe,CAACR,IAAI,CAACO,SAAS,CAAC;MAC/BL,aAAa,CAAC;QAAE7C,QAAQ,EAAEmD;MAAgB,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIH,SAAS,IAAK;IACxC,IAAI,CAAC3C,IAAI,EAAE;IAEX,MAAM4C,eAAe,GAAG5C,IAAI,CAACP,QAAQ,CAACsD,MAAM,CAACjF,EAAE,IAAIA,EAAE,KAAK6E,SAAS,CAAC;IACpEL,aAAa,CAAC;MAAE7C,QAAQ,EAAEmD;IAAgB,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMI,YAAY,GAAIL,SAAS,IAAK;IAAA,IAAAM,cAAA;IAClC,OAAO,CAAAjD,IAAI,aAAJA,IAAI,wBAAAiD,cAAA,GAAJjD,IAAI,CAAEP,QAAQ,cAAAwD,cAAA,uBAAdA,cAAA,CAAgBJ,QAAQ,CAACF,SAAS,CAAC,KAAI,KAAK;EACrD,CAAC;EAED,MAAMO,KAAK,GAAG;IACZlD,IAAI;IACJE,SAAS;IACTE,eAAe;IACfa,KAAK;IACLe,QAAQ;IACRK,MAAM;IACNC,aAAa;IACbG,aAAa;IACbC,aAAa;IACbI,kBAAkB;IAClBE;EACF,CAAC;EAED,oBACEzF,OAAA,CAACC,WAAW,CAAC2F,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApD,QAAA,EAChCA;EAAQ;IAAAsD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACxD,GAAA,CAtNWF,YAAY;AAAA2D,EAAA,GAAZ3D,YAAY;AAwNzB,eAAerC,WAAW;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}