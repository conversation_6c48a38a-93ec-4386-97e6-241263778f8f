{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"type\"];\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\nfunction applyGeneratorOptions(_ref) {\n  let {\n      type\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  if (isGenerator(type) && supportsLinearEasing()) {\n    return type.applyToOptions(options);\n  } else {\n    var _options$duration, _options$ease;\n    (_options$duration = options.duration) !== null && _options$duration !== void 0 ? _options$duration : options.duration = 300;\n    (_options$ease = options.ease) !== null && _options$ease !== void 0 ? _options$ease : options.ease = \"easeOut\";\n  }\n  return options;\n}\nexport { applyGeneratorOptions };", "map": {"version": 3, "names": ["supportsLinearEasing", "isGenerator", "applyGeneratorOptions", "_ref", "type", "options", "_objectWithoutProperties", "_excluded", "applyToOptions", "_options$duration", "_options$ease", "duration", "ease"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs"], "sourcesContent": ["import { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\n\nfunction applyGeneratorOptions({ type, ...options }) {\n    if (isGenerator(type) && supportsLinearEasing()) {\n        return type.applyToOptions(options);\n    }\n    else {\n        options.duration ?? (options.duration = 300);\n        options.ease ?? (options.ease = \"easeOut\");\n    }\n    return options;\n}\n\nexport { applyGeneratorOptions };\n"], "mappings": ";;AAAA,SAASA,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,WAAW,QAAQ,yCAAyC;AAErE,SAASC,qBAAqBA,CAAAC,IAAA,EAAuB;EAAA,IAAtB;MAAEC;IAAiB,CAAC,GAAAD,IAAA;IAATE,OAAO,GAAAC,wBAAA,CAAAH,IAAA,EAAAI,SAAA;EAC7C,IAAIN,WAAW,CAACG,IAAI,CAAC,IAAIJ,oBAAoB,CAAC,CAAC,EAAE;IAC7C,OAAOI,IAAI,CAACI,cAAc,CAACH,OAAO,CAAC;EACvC,CAAC,MACI;IAAA,IAAAI,iBAAA,EAAAC,aAAA;IACD,CAAAD,iBAAA,GAAAJ,OAAO,CAACM,QAAQ,cAAAF,iBAAA,cAAAA,iBAAA,GAAKJ,OAAO,CAACM,QAAQ,GAAG,GAAG;IAC3C,CAAAD,aAAA,GAAAL,OAAO,CAACO,IAAI,cAAAF,aAAA,cAAAA,aAAA,GAAKL,OAAO,CAACO,IAAI,GAAG,SAAS;EAC7C;EACA,OAAOP,OAAO;AAClB;AAEA,SAASH,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}