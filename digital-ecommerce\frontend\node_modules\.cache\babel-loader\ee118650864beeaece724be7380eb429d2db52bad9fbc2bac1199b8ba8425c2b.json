{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\AccountPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { UserIcon, CogIcon, ShoppingBagIcon, HeartIcon, MapPinIcon, CreditCardIcon, BellIcon, ShieldCheckIcon, PencilIcon, PlusIcon } from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountPage = () => {\n  _s();\n  const {\n    user,\n    updateProfile,\n    isLoading\n  } = useUser();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: (user === null || user === void 0 ? void 0 : user.firstName) || '',\n    lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    phone: (user === null || user === void 0 ? void 0 : user.phone) || ''\n  });\n  const tabs = [{\n    id: 'profile',\n    name: 'Profile',\n    icon: UserIcon\n  }, {\n    id: 'orders',\n    name: 'Orders',\n    icon: ShoppingBagIcon\n  }, {\n    id: 'wishlist',\n    name: 'Wishlist',\n    icon: HeartIcon\n  }, {\n    id: 'addresses',\n    name: 'Addresses',\n    icon: MapPinIcon\n  }, {\n    id: 'payments',\n    name: 'Payment Methods',\n    icon: CreditCardIcon\n  }, {\n    id: 'notifications',\n    name: 'Notifications',\n    icon: BellIcon\n  }, {\n    id: 'security',\n    name: 'Security',\n    icon: ShieldCheckIcon\n  }];\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSaveProfile = async () => {\n    const result = await updateProfile(formData);\n    if (result.success) {\n      toast.success('Profile updated successfully!');\n      setIsEditing(false);\n    } else {\n      toast.error(result.error);\n    }\n  };\n  const handleCancelEdit = () => {\n    setFormData({\n      firstName: (user === null || user === void 0 ? void 0 : user.firstName) || '',\n      lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n      email: (user === null || user === void 0 ? void 0 : user.email) || '',\n      phone: (user === null || user === void 0 ? void 0 : user.phone) || ''\n    });\n    setIsEditing(false);\n  };\n  const renderProfileTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Profile Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), !isEditing && /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setIsEditing(true),\n        variant: \"outline\",\n        icon: PencilIcon,\n        children: \"Edit Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture,\n            alt: \"Profile\",\n            className: \"w-24 h-24 rounded-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-24 h-24 bg-light-orange-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(UserIcon, {\n              className: \"w-12 h-12 text-light-orange-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), isEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"absolute bottom-0 right-0 bg-light-orange-500 text-white rounded-full p-2 hover:bg-light-orange-600 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: user === null || user === void 0 ? void 0 : user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"Member since \", new Date(user === null || user === void 0 ? void 0 : user.createdAt).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"First Name\",\n          name: \"firstName\",\n          value: formData.firstName,\n          onChange: handleInputChange,\n          disabled: !isEditing,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Last Name\",\n          name: \"lastName\",\n          value: formData.lastName,\n          onChange: handleInputChange,\n          disabled: !isEditing,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Email Address\",\n          type: \"email\",\n          name: \"email\",\n          value: formData.email,\n          onChange: handleInputChange,\n          disabled: !isEditing,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Phone Number\",\n          type: \"tel\",\n          name: \"phone\",\n          value: formData.phone,\n          onChange: handleInputChange,\n          disabled: !isEditing,\n          placeholder: \"+****************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4 mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveProfile,\n          loading: isLoading,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelEdit,\n          variant: \"outline\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n  const renderOrdersTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900\",\n      children: \"Order History\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(ShoppingBagIcon, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"No orders yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Start shopping to see your orders here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          children: \"Start Shopping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n  const renderWishlistTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900\",\n      children: \"Wishlist\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(HeartIcon, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Your wishlist is empty\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Save items you love for later.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          children: \"Browse Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n  const renderAddressesTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Addresses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: PlusIcon,\n        children: \"Add Address\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"No addresses saved\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Add your shipping and billing addresses.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: PlusIcon,\n          children: \"Add Your First Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n  const renderPaymentMethodsTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Payment Methods\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: PlusIcon,\n        children: \"Add Payment Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(CreditCardIcon, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"No payment methods\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Add your credit cards and payment methods.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: PlusIcon,\n          children: \"Add Payment Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n  const renderNotificationsTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900\",\n      children: \"Notification Preferences\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [{\n          id: 'email',\n          label: 'Email Notifications',\n          description: 'Receive order updates and promotions via email'\n        }, {\n          id: 'sms',\n          label: 'SMS Notifications',\n          description: 'Get important updates via text message'\n        }, {\n          id: 'marketing',\n          label: 'Marketing Emails',\n          description: 'Receive promotional offers and new product announcements'\n        }].map(setting => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900\",\n              children: setting.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: setting.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"relative inline-flex items-center cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              className: \"sr-only peer\",\n              defaultChecked: setting.id === 'email'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-light-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-light-orange-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, setting.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n  const renderSecurityTab = () => /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900\",\n      children: \"Security Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border border-gray-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900 mb-2\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-4\",\n            children: \"Last changed 30 days ago\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            children: \"Change Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border border-gray-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900 mb-2\",\n            children: \"Two-Factor Authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-4\",\n            children: \"Add an extra layer of security to your account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            children: \"Enable 2FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border border-gray-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-gray-900 mb-2\",\n            children: \"Login Sessions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600 mb-4\",\n            children: \"Manage your active login sessions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            children: \"View Sessions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'profile':\n        return renderProfileTab();\n      case 'orders':\n        return renderOrdersTab();\n      case 'wishlist':\n        return renderWishlistTab();\n      case 'addresses':\n        return renderAddressesTab();\n      case 'payments':\n        return renderPaymentMethodsTab();\n      case 'notifications':\n        return renderNotificationsTab();\n      case 'security':\n        return renderSecurityTab();\n      default:\n        return renderProfileTab();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"My Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your account settings and preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"space-y-2\",\n              children: tabs.map(tab => {\n                const Icon = tab.icon;\n                return /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab(tab.id),\n                  className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${activeTab === tab.id ? 'bg-light-orange-100 text-light-orange-700 font-medium' : 'text-gray-600 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: tab.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this)]\n                }, tab.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-3\",\n          children: renderTabContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountPage, \"VfCIzXQzAkVPqRQl2p8Cp53iIRc=\", false, function () {\n  return [useUser];\n});\n_c = AccountPage;\nexport default AccountPage;\nvar _c;\n$RefreshReg$(_c, \"AccountPage\");", "map": {"version": 3, "names": ["React", "useState", "motion", "UserIcon", "CogIcon", "ShoppingBagIcon", "HeartIcon", "MapPinIcon", "CreditCardIcon", "BellIcon", "ShieldCheckIcon", "PencilIcon", "PlusIcon", "useUser", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsxDEV", "_jsxDEV", "AccountPage", "_s", "user", "updateProfile", "isLoading", "activeTab", "setActiveTab", "isEditing", "setIsEditing", "formData", "setFormData", "firstName", "lastName", "email", "phone", "tabs", "id", "name", "icon", "handleInputChange", "e", "value", "target", "prev", "handleSaveProfile", "result", "success", "error", "handleCancelEdit", "renderProfileTab", "div", "initial", "opacity", "y", "animate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "profilePicture", "src", "alt", "Date", "createdAt", "toLocaleDateString", "label", "onChange", "disabled", "required", "type", "placeholder", "loading", "renderOrdersTab", "renderWishlistTab", "renderAddressesTab", "renderPaymentMethodsTab", "renderNotificationsTab", "description", "map", "setting", "defaultChecked", "renderSecurityTab", "renderTabContent", "position", "tab", "Icon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/AccountPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  UserIcon,\n  CogIcon,\n  ShoppingBagIcon,\n  HeartIcon,\n  MapPinIcon,\n  CreditCardIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  PencilIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst AccountPage = () => {\n  const { user, updateProfile, isLoading } = useUser();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: user?.firstName || '',\n    lastName: user?.lastName || '',\n    email: user?.email || '',\n    phone: user?.phone || ''\n  });\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: UserIcon },\n    { id: 'orders', name: 'Orders', icon: ShoppingBagIcon },\n    { id: 'wishlist', name: 'Wishlist', icon: HeartIcon },\n    { id: 'addresses', name: 'Addresses', icon: MapPinIcon },\n    { id: 'payments', name: 'Payment Methods', icon: CreditCardIcon },\n    { id: 'notifications', name: 'Notifications', icon: BellIcon },\n    { id: 'security', name: 'Security', icon: ShieldCheckIcon }\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSaveProfile = async () => {\n    const result = await updateProfile(formData);\n    if (result.success) {\n      toast.success('Profile updated successfully!');\n      setIsEditing(false);\n    } else {\n      toast.error(result.error);\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setFormData({\n      firstName: user?.firstName || '',\n      lastName: user?.lastName || '',\n      email: user?.email || '',\n      phone: user?.phone || ''\n    });\n    setIsEditing(false);\n  };\n\n  const renderProfileTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Profile Information</h2>\n        {!isEditing && (\n          <Button\n            onClick={() => setIsEditing(true)}\n            variant=\"outline\"\n            icon={PencilIcon}\n          >\n            Edit Profile\n          </Button>\n        )}\n      </div>\n\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        {/* Profile Picture */}\n        <div className=\"flex items-center space-x-6 mb-8\">\n          <div className=\"relative\">\n            {user?.profilePicture ? (\n              <img\n                src={user.profilePicture}\n                alt=\"Profile\"\n                className=\"w-24 h-24 rounded-full object-cover\"\n              />\n            ) : (\n              <div className=\"w-24 h-24 bg-light-orange-100 rounded-full flex items-center justify-center\">\n                <UserIcon className=\"w-12 h-12 text-light-orange-600\" />\n              </div>\n            )}\n            {isEditing && (\n              <button className=\"absolute bottom-0 right-0 bg-light-orange-500 text-white rounded-full p-2 hover:bg-light-orange-600 transition-colors\">\n                <PencilIcon className=\"w-4 h-4\" />\n              </button>\n            )}\n          </div>\n          <div>\n            <h3 className=\"text-xl font-semibold text-gray-900\">\n              {user?.firstName} {user?.lastName}\n            </h3>\n            <p className=\"text-gray-600\">{user?.email}</p>\n            <p className=\"text-sm text-gray-500\">\n              Member since {new Date(user?.createdAt).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n\n        {/* Profile Form */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Input\n            label=\"First Name\"\n            name=\"firstName\"\n            value={formData.firstName}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Last Name\"\n            name=\"lastName\"\n            value={formData.lastName}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Email Address\"\n            type=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            required\n          />\n          <Input\n            label=\"Phone Number\"\n            type=\"tel\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleInputChange}\n            disabled={!isEditing}\n            placeholder=\"+****************\"\n          />\n        </div>\n\n        {isEditing && (\n          <div className=\"flex space-x-4 mt-8\">\n            <Button\n              onClick={handleSaveProfile}\n              loading={isLoading}\n            >\n              Save Changes\n            </Button>\n            <Button\n              onClick={handleCancelEdit}\n              variant=\"outline\"\n            >\n              Cancel\n            </Button>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n\n  const renderOrdersTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Order History</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <ShoppingBagIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No orders yet</h3>\n          <p className=\"text-gray-600 mb-6\">Start shopping to see your orders here.</p>\n          <Button>Start Shopping</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderWishlistTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Wishlist</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <HeartIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Your wishlist is empty</h3>\n          <p className=\"text-gray-600 mb-6\">Save items you love for later.</p>\n          <Button>Browse Products</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderAddressesTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Addresses</h2>\n        <Button icon={PlusIcon}>Add Address</Button>\n      </div>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <MapPinIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No addresses saved</h3>\n          <p className=\"text-gray-600 mb-6\">Add your shipping and billing addresses.</p>\n          <Button icon={PlusIcon}>Add Your First Address</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderPaymentMethodsTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Payment Methods</h2>\n        <Button icon={PlusIcon}>Add Payment Method</Button>\n      </div>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"text-center py-12\">\n          <CreditCardIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No payment methods</h3>\n          <p className=\"text-gray-600 mb-6\">Add your credit cards and payment methods.</p>\n          <Button icon={PlusIcon}>Add Payment Method</Button>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderNotificationsTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Notification Preferences</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"space-y-6\">\n          {[\n            { id: 'email', label: 'Email Notifications', description: 'Receive order updates and promotions via email' },\n            { id: 'sms', label: 'SMS Notifications', description: 'Get important updates via text message' },\n            { id: 'marketing', label: 'Marketing Emails', description: 'Receive promotional offers and new product announcements' }\n          ].map((setting) => (\n            <div key={setting.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">{setting.label}</h4>\n                <p className=\"text-sm text-gray-600\">{setting.description}</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input type=\"checkbox\" className=\"sr-only peer\" defaultChecked={setting.id === 'email'} />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-light-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-light-orange-500\"></div>\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderSecurityTab = () => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      <h2 className=\"text-2xl font-bold text-gray-900\">Security Settings</h2>\n      <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n        <div className=\"space-y-6\">\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Password</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Last changed 30 days ago</p>\n            <Button variant=\"outline\">Change Password</Button>\n          </div>\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Two-Factor Authentication</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Add an extra layer of security to your account</p>\n            <Button variant=\"outline\">Enable 2FA</Button>\n          </div>\n          <div className=\"p-4 border border-gray-200 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">Login Sessions</h4>\n            <p className=\"text-sm text-gray-600 mb-4\">Manage your active login sessions</p>\n            <Button variant=\"outline\">View Sessions</Button>\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'profile': return renderProfileTab();\n      case 'orders': return renderOrdersTab();\n      case 'wishlist': return renderWishlistTab();\n      case 'addresses': return renderAddressesTab();\n      case 'payments': return renderPaymentMethodsTab();\n      case 'notifications': return renderNotificationsTab();\n      case 'security': return renderSecurityTab();\n      default: return renderProfileTab();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      \n      {/* Header */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">My Account</h1>\n          <p className=\"text-gray-600\">Manage your account settings and preferences</p>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6\">\n              <nav className=\"space-y-2\">\n                {tabs.map((tab) => {\n                  const Icon = tab.icon;\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-light-orange-100 text-light-orange-700 font-medium'\n                          : 'text-gray-600 hover:bg-gray-100'\n                      }`}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                      <span>{tab.name}</span>\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"lg:col-span-3\">\n            {renderTabContent()}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,OAAO,EACPC,eAAe,EACfC,SAAS,EACTC,UAAU,EACVC,cAAc,EACdC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,QAAQ,QACH,6BAA6B;AACpC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC,aAAa;IAAEC;EAAU,CAAC,GAAGX,OAAO,CAAC,CAAC;EACpD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IACvC8B,SAAS,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,KAAI,EAAE;IAChCC,QAAQ,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,QAAQ,KAAI,EAAE;IAC9BC,KAAK,EAAE,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,EAAE;IACxBC,KAAK,EAAE,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,KAAK,KAAI;EACxB,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAEnC;EAAS,CAAC,EAClD;IAAEiC,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEjC;EAAgB,CAAC,EACvD;IAAE+B,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEhC;EAAU,CAAC,EACrD;IAAE8B,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE/B;EAAW,CAAC,EACxD;IAAE6B,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE9B;EAAe,CAAC,EACjE;IAAE4B,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE7B;EAAS,CAAC,EAC9D;IAAE2B,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE5B;EAAgB,CAAC,CAC5D;EAED,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEH,IAAI;MAAEI;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCZ,WAAW,CAACa,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACN,IAAI,GAAGI;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMC,MAAM,GAAG,MAAMtB,aAAa,CAACM,QAAQ,CAAC;IAC5C,IAAIgB,MAAM,CAACC,OAAO,EAAE;MAClB9B,KAAK,CAAC8B,OAAO,CAAC,+BAA+B,CAAC;MAC9ClB,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,MAAM;MACLZ,KAAK,CAAC+B,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlB,WAAW,CAAC;MACVC,SAAS,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,KAAI,EAAE;MAChCC,QAAQ,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,QAAQ,KAAI,EAAE;MAC9BC,KAAK,EAAE,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,EAAE;MACxBC,KAAK,EAAE,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,KAAK,KAAI;IACxB,CAAC,CAAC;IACFN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMqB,gBAAgB,GAAGA,CAAA,kBACvB9B,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAKoC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrC,OAAA;QAAIoC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACxE,CAACjC,SAAS,iBACTR,OAAA,CAACL,MAAM;QACL+C,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,IAAI,CAAE;QAClCkC,OAAO,EAAC,SAAS;QACjBxB,IAAI,EAAE3B,UAAW;QAAA6C,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBAEjDrC,OAAA;QAAKoC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CrC,OAAA;UAAKoC,SAAS,EAAC,UAAU;UAAAC,QAAA,GACtBlC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyC,cAAc,gBACnB5C,OAAA;YACE6C,GAAG,EAAE1C,IAAI,CAACyC,cAAe;YACzBE,GAAG,EAAC,SAAS;YACbV,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,gBAEFzC,OAAA;YAAKoC,SAAS,EAAC,6EAA6E;YAAAC,QAAA,eAC1FrC,OAAA,CAAChB,QAAQ;cAACoD,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CACN,EACAjC,SAAS,iBACRR,OAAA;YAAQoC,SAAS,EAAC,uHAAuH;YAAAC,QAAA,eACvIrC,OAAA,CAACR,UAAU;cAAC4C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAIoC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAChDlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,EAAC,GAAC,EAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,QAAQ;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACLzC,OAAA;YAAGoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW;UAAK;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CzC,OAAA;YAAGoC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,eACtB,EAAC,IAAIU,IAAI,CAAC5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDrC,OAAA,CAACJ,KAAK;UACJsD,KAAK,EAAC,YAAY;UAClBhC,IAAI,EAAC,WAAW;UAChBI,KAAK,EAAEZ,QAAQ,CAACE,SAAU;UAC1BuC,QAAQ,EAAE/B,iBAAkB;UAC5BgC,QAAQ,EAAE,CAAC5C,SAAU;UACrB6C,QAAQ;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFzC,OAAA,CAACJ,KAAK;UACJsD,KAAK,EAAC,WAAW;UACjBhC,IAAI,EAAC,UAAU;UACfI,KAAK,EAAEZ,QAAQ,CAACG,QAAS;UACzBsC,QAAQ,EAAE/B,iBAAkB;UAC5BgC,QAAQ,EAAE,CAAC5C,SAAU;UACrB6C,QAAQ;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFzC,OAAA,CAACJ,KAAK;UACJsD,KAAK,EAAC,eAAe;UACrBI,IAAI,EAAC,OAAO;UACZpC,IAAI,EAAC,OAAO;UACZI,KAAK,EAAEZ,QAAQ,CAACI,KAAM;UACtBqC,QAAQ,EAAE/B,iBAAkB;UAC5BgC,QAAQ,EAAE,CAAC5C,SAAU;UACrB6C,QAAQ;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFzC,OAAA,CAACJ,KAAK;UACJsD,KAAK,EAAC,cAAc;UACpBI,IAAI,EAAC,KAAK;UACVpC,IAAI,EAAC,OAAO;UACZI,KAAK,EAAEZ,QAAQ,CAACK,KAAM;UACtBoC,QAAQ,EAAE/B,iBAAkB;UAC5BgC,QAAQ,EAAE,CAAC5C,SAAU;UACrB+C,WAAW,EAAC;QAAmB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELjC,SAAS,iBACRR,OAAA;QAAKoC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCrC,OAAA,CAACL,MAAM;UACL+C,OAAO,EAAEjB,iBAAkB;UAC3B+B,OAAO,EAAEnD,SAAU;UAAAgC,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA,CAACL,MAAM;UACL+C,OAAO,EAAEb,gBAAiB;UAC1Bc,OAAO,EAAC,SAAS;UAAAN,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMgB,eAAe,GAAGA,CAAA,kBACtBzD,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAIoC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnEzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrC,OAAA,CAACd,eAAe;UAACkD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEzC,OAAA;UAAIoC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEzC,OAAA;UAAGoC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7EzC,OAAA,CAACL,MAAM;UAAA0C,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMiB,iBAAiB,GAAGA,CAAA,kBACxB1D,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAIoC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9DzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrC,OAAA,CAACb,SAAS;UAACiD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DzC,OAAA;UAAIoC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFzC,OAAA;UAAGoC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpEzC,OAAA,CAACL,MAAM;UAAA0C,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMkB,kBAAkB,GAAGA,CAAA,kBACzB3D,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAKoC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrC,OAAA;QAAIoC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DzC,OAAA,CAACL,MAAM;QAACwB,IAAI,EAAE1B,QAAS;QAAA4C,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,eACNzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrC,OAAA,CAACZ,UAAU;UAACgD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DzC,OAAA;UAAIoC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EzC,OAAA;UAAGoC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9EzC,OAAA,CAACL,MAAM;UAACwB,IAAI,EAAE1B,QAAS;UAAA4C,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMmB,uBAAuB,GAAGA,CAAA,kBAC9B5D,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAKoC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrC,OAAA;QAAIoC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEzC,OAAA,CAACL,MAAM;QAACwB,IAAI,EAAE1B,QAAS;QAAA4C,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eACNzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrC,OAAA,CAACX,cAAc;UAAC+C,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEzC,OAAA;UAAIoC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EzC,OAAA;UAAGoC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChFzC,OAAA,CAACL,MAAM;UAACwB,IAAI,EAAE1B,QAAS;UAAA4C,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMoB,sBAAsB,GAAGA,CAAA,kBAC7B7D,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAIoC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9EzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CACC;UAAEpB,EAAE,EAAE,OAAO;UAAEiC,KAAK,EAAE,qBAAqB;UAAEY,WAAW,EAAE;QAAiD,CAAC,EAC5G;UAAE7C,EAAE,EAAE,KAAK;UAAEiC,KAAK,EAAE,mBAAmB;UAAEY,WAAW,EAAE;QAAyC,CAAC,EAChG;UAAE7C,EAAE,EAAE,WAAW;UAAEiC,KAAK,EAAE,kBAAkB;UAAEY,WAAW,EAAE;QAA2D,CAAC,CACxH,CAACC,GAAG,CAAEC,OAAO,iBACZhE,OAAA;UAAsBoC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACvGrC,OAAA;YAAAqC,QAAA,gBACErC,OAAA;cAAIoC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE2B,OAAO,CAACd;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DzC,OAAA;cAAGoC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE2B,OAAO,CAACF;YAAW;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNzC,OAAA;YAAOoC,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBACjErC,OAAA;cAAOsD,IAAI,EAAC,UAAU;cAAClB,SAAS,EAAC,cAAc;cAAC6B,cAAc,EAAED,OAAO,CAAC/C,EAAE,KAAK;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FzC,OAAA;cAAKoC,SAAS,EAAC;YAAyY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1Z,CAAC;QAAA,GARAuB,OAAO,CAAC/C,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,kBACxBlE,OAAA,CAACjB,MAAM,CAACgD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAErBrC,OAAA;MAAIoC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvEzC,OAAA;MAAKoC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDrC,OAAA;QAAKoC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrC,OAAA;UAAKoC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrC,OAAA;YAAIoC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DzC,OAAA;YAAGoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtEzC,OAAA,CAACL,MAAM;YAACgD,OAAO,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNzC,OAAA;UAAKoC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrC,OAAA;YAAIoC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EzC,OAAA;YAAGoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5FzC,OAAA,CAACL,MAAM;YAACgD,OAAO,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNzC,OAAA;UAAKoC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrC,OAAA;YAAIoC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEzC,OAAA;YAAGoC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EzC,OAAA,CAACL,MAAM;YAACgD,OAAO,EAAC,SAAS;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACb;EAED,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ7D,SAAS;MACf,KAAK,SAAS;QAAE,OAAOwB,gBAAgB,CAAC,CAAC;MACzC,KAAK,QAAQ;QAAE,OAAO2B,eAAe,CAAC,CAAC;MACvC,KAAK,UAAU;QAAE,OAAOC,iBAAiB,CAAC,CAAC;MAC3C,KAAK,WAAW;QAAE,OAAOC,kBAAkB,CAAC,CAAC;MAC7C,KAAK,UAAU;QAAE,OAAOC,uBAAuB,CAAC,CAAC;MACjD,KAAK,eAAe;QAAE,OAAOC,sBAAsB,CAAC,CAAC;MACrD,KAAK,UAAU;QAAE,OAAOK,iBAAiB,CAAC,CAAC;MAC3C;QAAS,OAAOpC,gBAAgB,CAAC,CAAC;IACpC;EACF,CAAC;EAED,oBACE9B,OAAA;IAAKoC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCrC,OAAA,CAACF,OAAO;MAACsE,QAAQ,EAAC;IAAW;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhCzC,OAAA;MAAKoC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCrC,OAAA;QAAKoC,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DrC,OAAA;UAAIoC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEzC,OAAA;UAAGoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzC,OAAA;MAAKoC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DrC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDrC,OAAA;UAAKoC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrC,OAAA;YAAKoC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDrC,OAAA;cAAKoC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBrB,IAAI,CAAC+C,GAAG,CAAEM,GAAG,IAAK;gBACjB,MAAMC,IAAI,GAAGD,GAAG,CAAClD,IAAI;gBACrB,oBACEnB,OAAA;kBAEE0C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC8D,GAAG,CAACpD,EAAE,CAAE;kBACpCmB,SAAS,EAAE,uFACT9B,SAAS,KAAK+D,GAAG,CAACpD,EAAE,GAChB,uDAAuD,GACvD,iCAAiC,EACpC;kBAAAoB,QAAA,gBAEHrC,OAAA,CAACsE,IAAI;oBAAClC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5BzC,OAAA;oBAAAqC,QAAA,EAAOgC,GAAG,CAACnD;kBAAI;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GATlB4B,GAAG,CAACpD,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUL,CAAC;cAEb,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B8B,gBAAgB,CAAC;QAAC;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAhWID,WAAW;EAAA,QAC4BP,OAAO;AAAA;AAAA6E,EAAA,GAD9CtE,WAAW;AAkWjB,eAAeA,WAAW;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}