{"ast": null, "code": "const animationMaps = new WeakMap();\nconst animationMapKey = function (name) {\n  let pseudoElement = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"\";\n  return \"\".concat(name, \":\").concat(pseudoElement);\n};\nfunction getAnimationMap(element) {\n  const map = animationMaps.get(element) || new Map();\n  animationMaps.set(element, map);\n  return map;\n}\nexport { animationMapKey, getAnimationMap };", "map": {"version": 3, "names": ["animationMaps", "WeakMap", "animationMapKey", "name", "pseudoElement", "arguments", "length", "undefined", "concat", "getAnimationMap", "element", "map", "get", "Map", "set"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/animation/utils/active-animations.mjs"], "sourcesContent": ["const animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement = \"\") => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n\nexport { animationMapKey, getAnimationMap };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,MAAMC,eAAe,GAAG,SAAAA,CAACC,IAAI;EAAA,IAAEC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAAA,UAAAG,MAAA,CAAQL,IAAI,OAAAK,MAAA,CAAIJ,aAAa;AAAA,CAAE;AAChF,SAASK,eAAeA,CAACC,OAAO,EAAE;EAC9B,MAAMC,GAAG,GAAGX,aAAa,CAACY,GAAG,CAACF,OAAO,CAAC,IAAI,IAAIG,GAAG,CAAC,CAAC;EACnDb,aAAa,CAACc,GAAG,CAACJ,OAAO,EAAEC,GAAG,CAAC;EAC/B,OAAOA,GAAG;AACd;AAEA,SAAST,eAAe,EAAEO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}