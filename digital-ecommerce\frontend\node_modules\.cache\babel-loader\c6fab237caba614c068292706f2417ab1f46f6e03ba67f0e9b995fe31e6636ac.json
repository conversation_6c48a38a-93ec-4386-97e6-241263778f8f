{"ast": null, "code": "import { memo } from 'motion-utils';\nconst supportsScrollTimeline = /* @__PURE__ */memo(() => window.ScrollTimeline !== undefined);\nexport { supportsScrollTimeline };", "map": {"version": 3, "names": ["memo", "supportsScrollTimeline", "window", "ScrollTimeline", "undefined"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = /* @__PURE__ */ memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AAEnC,MAAMC,sBAAsB,GAAG,eAAgBD,IAAI,CAAC,MAAME,MAAM,CAACC,cAAc,KAAKC,SAAS,CAAC;AAE9F,SAASH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}