{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\components\\\\ShoppingCart.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, createContext, useContext } from 'react';\nimport { ShoppingCartIcon, TrashIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Create Cart Context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nexport const useCart = () => {\n  _s();\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst initialCartItems = [{\n  id: 'elec-001',\n  name: 'MacBook Pro 16\" M3 Max',\n  price: 3499.99,\n  quantity: 1,\n  image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300',\n  type: 'physical'\n}, {\n  id: 'soft-001',\n  name: 'Microsoft Office 365 Personal',\n  price: 69.99,\n  quantity: 1,\n  image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300',\n  type: 'digital'\n}];\n\n// Cart Provider Component\nexport const CartProvider = ({\n  children\n}) => {\n  _s2();\n  const [cartItems, setCartItems] = useState(initialCartItems);\n  const totalPrice = cartItems.reduce((total, item) => total + item.price * item.quantity, 0);\n  const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);\n  const addToCart = (product, quantity = 1) => {\n    setCartItems(prevItems => {\n      const existingItem = prevItems.find(item => item.id === product.id);\n      if (existingItem) {\n        return prevItems.map(item => item.id === product.id ? {\n          ...item,\n          quantity: item.quantity + quantity\n        } : item);\n      } else {\n        return [...prevItems, {\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity,\n          image: product.images ? product.images[0] : product.image,\n          type: product.type || 'physical'\n        }];\n      }\n    });\n  };\n  const updateQuantity = (id, newQuantity) => {\n    if (newQuantity === 0) {\n      removeItem(id);\n      return;\n    }\n    setCartItems(prevItems => prevItems.map(item => item.id === id ? {\n      ...item,\n      quantity: newQuantity\n    } : item));\n  };\n  const removeItem = id => {\n    setCartItems(prevItems => prevItems.filter(item => item.id !== id));\n  };\n  const clearCart = () => {\n    setCartItems([]);\n  };\n  const value = {\n    cartItems,\n    totalPrice,\n    totalItems,\n    addToCart,\n    updateQuantity,\n    removeItem,\n    clearCart\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s2(CartProvider, \"4w2ojYoId5XbeiObCafkfcfOPL0=\");\n_c = CartProvider;\nconst ShoppingCart = () => {\n  _s3();\n  const {\n    cartItems,\n    totalPrice,\n    totalItems,\n    updateQuantity,\n    removeItem\n  } = useCart();\n  const [isOpen, setIsOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      onClick: () => setIsOpen(!isOpen),\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      className: \"relative p-2 text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 rounded-full transition-colors duration-300\",\n      children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), totalItems > 0 && /*#__PURE__*/_jsxDEV(motion.span, {\n        initial: {\n          scale: 0\n        },\n        animate: {\n          scale: 1\n        },\n        className: \"absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\",\n        children: totalItems\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -10,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          y: -10,\n          scale: 0.95\n        },\n        transition: {\n          duration: 0.2\n        },\n        className: \"absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                className: \"w-6 h-6 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), \"Shopping Cart\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsOpen(false),\n              className: \"text-white hover:text-light-orange-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-h-96 overflow-y-auto\",\n          children: cartItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n              className: \"w-16 h-16 text-light-orange-300 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-light-orange-600 text-lg\",\n              children: \"Your cart is empty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-light-orange-500 text-sm mt-2\",\n              children: \"Add some products to get started!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 space-y-4\",\n            children: cartItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4 p-3 bg-light-orange-50 rounded-lg border border-light-orange-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.image,\n                alt: item.name,\n                className: \"w-16 h-16 object-cover rounded-lg shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-semibold text-light-orange-800 truncate\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-light-orange-600 font-medium\",\n                  children: [\"$\", item.price.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updateQuantity(item.id, item.quantity - 1),\n                  className: \"p-1 bg-light-orange-200 text-light-orange-700 rounded-full hover:bg-light-orange-300 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(MinusIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"w-8 text-center font-semibold text-light-orange-800\",\n                  children: item.quantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updateQuantity(item.id, item.quantity + 1),\n                  className: \"p-1 bg-light-orange-200 text-light-orange-700 rounded-full hover:bg-light-orange-300 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(PlusIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeItem(item.id),\n                  className: \"p-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors ml-2\",\n                  children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), cartItems.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-t border-light-orange-200 bg-light-orange-50 rounded-b-xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-bold text-light-orange-800\",\n              children: \"Total:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold text-light-orange-700\",\n              children: [\"$\", totalPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n            children: \"Proceed to Checkout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s3(ShoppingCart, \"gCLLk29UJ8UwXlrTh7TbQVRjjwE=\", false, function () {\n  return [useCart];\n});\n_c2 = ShoppingCart;\nexport default ShoppingCart;\nvar _c, _c2;\n$RefreshReg$(_c, \"CartProvider\");\n$RefreshReg$(_c2, \"ShoppingCart\");", "map": {"version": 3, "names": ["React", "useState", "createContext", "useContext", "ShoppingCartIcon", "TrashIcon", "PlusIcon", "MinusIcon", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "CartContext", "useCart", "_s", "context", "Error", "initialCartItems", "id", "name", "price", "quantity", "image", "type", "CartProvider", "children", "_s2", "cartItems", "setCartItems", "totalPrice", "reduce", "total", "item", "totalItems", "addToCart", "product", "prevItems", "existingItem", "find", "map", "images", "updateQuantity", "newQuantity", "removeItem", "filter", "clearCart", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ShoppingCart", "_s3", "isOpen", "setIsOpen", "className", "button", "onClick", "whileHover", "scale", "whileTap", "span", "initial", "animate", "div", "opacity", "y", "exit", "transition", "duration", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "src", "alt", "toFixed", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/components/ShoppingCart.js"], "sourcesContent": ["import React, { useState, createContext, useContext } from 'react';\r\nimport { ShoppingCartIcon, TrashIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\n\r\n// Create Cart Context\r\nconst CartContext = createContext();\r\n\r\nexport const useCart = () => {\r\n  const context = useContext(CartContext);\r\n  if (!context) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nconst initialCartItems = [\r\n  {\r\n    id: 'elec-001',\r\n    name: 'MacBook Pro 16\" M3 Max',\r\n    price: 3499.99,\r\n    quantity: 1,\r\n    image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300',\r\n    type: 'physical'\r\n  },\r\n  {\r\n    id: 'soft-001',\r\n    name: 'Microsoft Office 365 Personal',\r\n    price: 69.99,\r\n    quantity: 1,\r\n    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300',\r\n    type: 'digital'\r\n  }\r\n];\r\n\r\n// Cart Provider Component\r\nexport const CartProvider = ({ children }) => {\r\n  const [cartItems, setCartItems] = useState(initialCartItems);\r\n\r\n  const totalPrice = cartItems.reduce((total, item) => total + item.price * item.quantity, 0);\r\n  const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);\r\n\r\n  const addToCart = (product, quantity = 1) => {\r\n    setCartItems(prevItems => {\r\n      const existingItem = prevItems.find(item => item.id === product.id);\r\n      if (existingItem) {\r\n        return prevItems.map(item =>\r\n          item.id === product.id\r\n            ? { ...item, quantity: item.quantity + quantity }\r\n            : item\r\n        );\r\n      } else {\r\n        return [...prevItems, {\r\n          id: product.id,\r\n          name: product.name,\r\n          price: product.price,\r\n          quantity,\r\n          image: product.images ? product.images[0] : product.image,\r\n          type: product.type || 'physical'\r\n        }];\r\n      }\r\n    });\r\n  };\r\n\r\n  const updateQuantity = (id, newQuantity) => {\r\n    if (newQuantity === 0) {\r\n      removeItem(id);\r\n      return;\r\n    }\r\n    setCartItems(prevItems =>\r\n      prevItems.map(item =>\r\n        item.id === id ? { ...item, quantity: newQuantity } : item\r\n      )\r\n    );\r\n  };\r\n\r\n  const removeItem = (id) => {\r\n    setCartItems(prevItems => prevItems.filter(item => item.id !== id));\r\n  };\r\n\r\n  const clearCart = () => {\r\n    setCartItems([]);\r\n  };\r\n\r\n  const value = {\r\n    cartItems,\r\n    totalPrice,\r\n    totalItems,\r\n    addToCart,\r\n    updateQuantity,\r\n    removeItem,\r\n    clearCart\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider value={value}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n\r\nconst ShoppingCart = () => {\r\n  const { cartItems, totalPrice, totalItems, updateQuantity, removeItem } = useCart();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Cart Icon Button */}\r\n      <motion.button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        whileHover={{ scale: 1.05 }}\r\n        whileTap={{ scale: 0.95 }}\r\n        className=\"relative p-2 text-gray-700 hover:text-light-orange-600 hover:bg-light-orange-50 rounded-full transition-colors duration-300\"\r\n      >\r\n        <ShoppingCartIcon className=\"w-6 h-6\" />\r\n        {totalItems > 0 && (\r\n          <motion.span\r\n            initial={{ scale: 0 }}\r\n            animate={{ scale: 1 }}\r\n            className=\"absolute -top-2 -right-2 bg-light-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center\"\r\n          >\r\n            {totalItems}\r\n          </motion.span>\r\n        )}\r\n      </motion.button>\r\n\r\n      {/* Cart Dropdown */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10, scale: 0.95 }}\r\n            animate={{ opacity: 1, y: 0, scale: 1 }}\r\n            exit={{ opacity: 0, y: -10, scale: 0.95 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-light-orange-100 z-50\"\r\n          >\r\n          {/* Header */}\r\n          <div className=\"bg-gradient-to-r from-light-orange-500 to-light-orange-600 px-6 py-4 rounded-t-xl\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h2 className=\"text-xl font-bold text-white flex items-center\">\r\n                <ShoppingCartIcon className=\"w-6 h-6 mr-2\" />\r\n                Shopping Cart\r\n              </h2>\r\n              <button\r\n                onClick={() => setIsOpen(false)}\r\n                className=\"text-white hover:text-light-orange-200 transition-colors\"\r\n              >\r\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Cart Items */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {cartItems.length === 0 ? (\r\n              <div className=\"p-8 text-center\">\r\n                <ShoppingCartIcon className=\"w-16 h-16 text-light-orange-300 mx-auto mb-4\" />\r\n                <p className=\"text-light-orange-600 text-lg\">Your cart is empty</p>\r\n                <p className=\"text-light-orange-500 text-sm mt-2\">Add some products to get started!</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-4 space-y-4\">\r\n                {cartItems.map(item => (\r\n                  <div key={item.id} className=\"flex items-center space-x-4 p-3 bg-light-orange-50 rounded-lg border border-light-orange-100\">\r\n                    <img\r\n                      src={item.image}\r\n                      alt={item.name}\r\n                      className=\"w-16 h-16 object-cover rounded-lg shadow-sm\"\r\n                    />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <h3 className=\"text-sm font-semibold text-light-orange-800 truncate\">\r\n                        {item.name}\r\n                      </h3>\r\n                      <p className=\"text-light-orange-600 font-medium\">\r\n                        ${item.price.toFixed(2)}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <button\r\n                        onClick={() => updateQuantity(item.id, item.quantity - 1)}\r\n                        className=\"p-1 bg-light-orange-200 text-light-orange-700 rounded-full hover:bg-light-orange-300 transition-colors\"\r\n                      >\r\n                        <MinusIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                      <span className=\"w-8 text-center font-semibold text-light-orange-800\">\r\n                        {item.quantity}\r\n                      </span>\r\n                      <button\r\n                        onClick={() => updateQuantity(item.id, item.quantity + 1)}\r\n                        className=\"p-1 bg-light-orange-200 text-light-orange-700 rounded-full hover:bg-light-orange-300 transition-colors\"\r\n                      >\r\n                        <PlusIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => removeItem(item.id)}\r\n                        className=\"p-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors ml-2\"\r\n                      >\r\n                        <TrashIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          {cartItems.length > 0 && (\r\n            <div className=\"p-6 border-t border-light-orange-200 bg-light-orange-50 rounded-b-xl\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <span className=\"text-lg font-bold text-light-orange-800\">Total:</span>\r\n                <span className=\"text-2xl font-bold text-light-orange-700\">\r\n                  ${totalPrice.toFixed(2)}\r\n                </span>\r\n              </div>\r\n              <button className=\"w-full bg-gradient-to-r from-light-orange-500 to-light-orange-600 text-white py-3 px-6 rounded-lg hover:from-light-orange-600 hover:to-light-orange-700 transition-all duration-200 font-semibold shadow-md hover:shadow-lg transform hover:-translate-y-0.5\">\r\n                Proceed to Checkout\r\n              </button>\r\n            </div>\r\n          )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShoppingCart;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAClE,SAASC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,6BAA6B;AAC9F,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGV,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMW,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGZ,UAAU,CAACS,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,MAAMI,gBAAgB,GAAG,CACvB;EACEC,EAAE,EAAE,UAAU;EACdC,IAAI,EAAE,wBAAwB;EAC9BC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE,oEAAoE;EAC3EC,IAAI,EAAE;AACR,CAAC,EACD;EACEL,EAAE,EAAE,UAAU;EACdC,IAAI,EAAE,+BAA+B;EACrCC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE,oEAAoE;EAC3EC,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAACgB,gBAAgB,CAAC;EAE5D,MAAMY,UAAU,GAAGF,SAAS,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACZ,KAAK,GAAGY,IAAI,CAACX,QAAQ,EAAE,CAAC,CAAC;EAC3F,MAAMY,UAAU,GAAGN,SAAS,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACX,QAAQ,EAAE,CAAC,CAAC;EAE9E,MAAMa,SAAS,GAAGA,CAACC,OAAO,EAAEd,QAAQ,GAAG,CAAC,KAAK;IAC3CO,YAAY,CAACQ,SAAS,IAAI;MACxB,MAAMC,YAAY,GAAGD,SAAS,CAACE,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACd,EAAE,KAAKiB,OAAO,CAACjB,EAAE,CAAC;MACnE,IAAImB,YAAY,EAAE;QAChB,OAAOD,SAAS,CAACG,GAAG,CAACP,IAAI,IACvBA,IAAI,CAACd,EAAE,KAAKiB,OAAO,CAACjB,EAAE,GAClB;UAAE,GAAGc,IAAI;UAAEX,QAAQ,EAAEW,IAAI,CAACX,QAAQ,GAAGA;QAAS,CAAC,GAC/CW,IACN,CAAC;MACH,CAAC,MAAM;QACL,OAAO,CAAC,GAAGI,SAAS,EAAE;UACpBlB,EAAE,EAAEiB,OAAO,CAACjB,EAAE;UACdC,IAAI,EAAEgB,OAAO,CAAChB,IAAI;UAClBC,KAAK,EAAEe,OAAO,CAACf,KAAK;UACpBC,QAAQ;UACRC,KAAK,EAAEa,OAAO,CAACK,MAAM,GAAGL,OAAO,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGL,OAAO,CAACb,KAAK;UACzDC,IAAI,EAAEY,OAAO,CAACZ,IAAI,IAAI;QACxB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,cAAc,GAAGA,CAACvB,EAAE,EAAEwB,WAAW,KAAK;IAC1C,IAAIA,WAAW,KAAK,CAAC,EAAE;MACrBC,UAAU,CAACzB,EAAE,CAAC;MACd;IACF;IACAU,YAAY,CAACQ,SAAS,IACpBA,SAAS,CAACG,GAAG,CAACP,IAAI,IAChBA,IAAI,CAACd,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGc,IAAI;MAAEX,QAAQ,EAAEqB;IAAY,CAAC,GAAGV,IACxD,CACF,CAAC;EACH,CAAC;EAED,MAAMW,UAAU,GAAIzB,EAAE,IAAK;IACzBU,YAAY,CAACQ,SAAS,IAAIA,SAAS,CAACQ,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACd,EAAE,KAAKA,EAAE,CAAC,CAAC;EACrE,CAAC;EAED,MAAM2B,SAAS,GAAGA,CAAA,KAAM;IACtBjB,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMkB,KAAK,GAAG;IACZnB,SAAS;IACTE,UAAU;IACVI,UAAU;IACVC,SAAS;IACTO,cAAc;IACdE,UAAU;IACVE;EACF,CAAC;EAED,oBACElC,OAAA,CAACC,WAAW,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArB,QAAA,EAChCA;EAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzB,GAAA,CA/DWF,YAAY;AAAA4B,EAAA,GAAZ5B,YAAY;AAiEzB,MAAM6B,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAM;IAAE3B,SAAS;IAAEE,UAAU;IAAEI,UAAU;IAAEQ,cAAc;IAAEE;EAAW,CAAC,GAAG9B,OAAO,CAAC,CAAC;EACnF,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAE3C,oBACEU,OAAA;IAAK8C,SAAS,EAAC,UAAU;IAAAhC,QAAA,gBAEvBd,OAAA,CAACH,MAAM,CAACkD,MAAM;MACZC,OAAO,EAAEA,CAAA,KAAMH,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCK,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAE;MAC5BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAK,CAAE;MAC1BJ,SAAS,EAAC,6HAA6H;MAAAhC,QAAA,gBAEvId,OAAA,CAACP,gBAAgB;QAACqD,SAAS,EAAC;MAAS;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACvClB,UAAU,GAAG,CAAC,iBACbtB,OAAA,CAACH,MAAM,CAACuD,IAAI;QACVC,OAAO,EAAE;UAAEH,KAAK,EAAE;QAAE,CAAE;QACtBI,OAAO,EAAE;UAAEJ,KAAK,EAAE;QAAE,CAAE;QACtBJ,SAAS,EAAC,iIAAiI;QAAAhC,QAAA,EAE1IQ;MAAU;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAGhBxC,OAAA,CAACF,eAAe;MAAAgB,QAAA,EACb8B,MAAM,iBACL5C,OAAA,CAACH,MAAM,CAAC0D,GAAG;QACTF,OAAO,EAAE;UAAEG,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEP,KAAK,EAAE;QAAK,CAAE;QAC7CI,OAAO,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEP,KAAK,EAAE;QAAE,CAAE;QACxCQ,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEP,KAAK,EAAE;QAAK,CAAE;QAC1CS,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9Bd,SAAS,EAAC,+FAA+F;QAAAhC,QAAA,gBAG3Gd,OAAA;UAAK8C,SAAS,EAAC,mFAAmF;UAAAhC,QAAA,eAChGd,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAhC,QAAA,gBAChDd,OAAA;cAAI8C,SAAS,EAAC,gDAAgD;cAAAhC,QAAA,gBAC5Dd,OAAA,CAACP,gBAAgB;gBAACqD,SAAS,EAAC;cAAc;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxC,OAAA;cACEgD,OAAO,EAAEA,CAAA,KAAMH,SAAS,CAAC,KAAK,CAAE;cAChCC,SAAS,EAAC,0DAA0D;cAAAhC,QAAA,eAEpEd,OAAA;gBAAK8C,SAAS,EAAC,SAAS;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAjD,QAAA,eAC5Ed,OAAA;kBAAMgE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxC,OAAA;UAAK8C,SAAS,EAAC,0BAA0B;UAAAhC,QAAA,EACtCE,SAAS,CAACoD,MAAM,KAAK,CAAC,gBACrBpE,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAhC,QAAA,gBAC9Bd,OAAA,CAACP,gBAAgB;cAACqD,SAAS,EAAC;YAA8C;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7ExC,OAAA;cAAG8C,SAAS,EAAC,+BAA+B;cAAAhC,QAAA,EAAC;YAAkB;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnExC,OAAA;cAAG8C,SAAS,EAAC,oCAAoC;cAAAhC,QAAA,EAAC;YAAiC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,gBAENxC,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAhC,QAAA,EAC3BE,SAAS,CAACY,GAAG,CAACP,IAAI,iBACjBrB,OAAA;cAAmB8C,SAAS,EAAC,8FAA8F;cAAAhC,QAAA,gBACzHd,OAAA;gBACEqE,GAAG,EAAEhD,IAAI,CAACV,KAAM;gBAChB2D,GAAG,EAAEjD,IAAI,CAACb,IAAK;gBACfsC,SAAS,EAAC;cAA6C;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACFxC,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAAhC,QAAA,gBAC7Bd,OAAA;kBAAI8C,SAAS,EAAC,sDAAsD;kBAAAhC,QAAA,EACjEO,IAAI,CAACb;gBAAI;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLxC,OAAA;kBAAG8C,SAAS,EAAC,mCAAmC;kBAAAhC,QAAA,GAAC,GAC9C,EAACO,IAAI,CAACZ,KAAK,CAAC8D,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxC,OAAA;gBAAK8C,SAAS,EAAC,6BAA6B;gBAAAhC,QAAA,gBAC1Cd,OAAA;kBACEgD,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAACT,IAAI,CAACd,EAAE,EAAEc,IAAI,CAACX,QAAQ,GAAG,CAAC,CAAE;kBAC1DoC,SAAS,EAAC,wGAAwG;kBAAAhC,QAAA,eAElHd,OAAA,CAACJ,SAAS;oBAACkD,SAAS,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACTxC,OAAA;kBAAM8C,SAAS,EAAC,qDAAqD;kBAAAhC,QAAA,EAClEO,IAAI,CAACX;gBAAQ;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACPxC,OAAA;kBACEgD,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAACT,IAAI,CAACd,EAAE,EAAEc,IAAI,CAACX,QAAQ,GAAG,CAAC,CAAE;kBAC1DoC,SAAS,EAAC,wGAAwG;kBAAAhC,QAAA,eAElHd,OAAA,CAACL,QAAQ;oBAACmD,SAAS,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACTxC,OAAA;kBACEgD,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACX,IAAI,CAACd,EAAE,CAAE;kBACnCuC,SAAS,EAAC,kFAAkF;kBAAAhC,QAAA,eAE5Fd,OAAA,CAACN,SAAS;oBAACoD,SAAS,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GApCEnB,IAAI,CAACd,EAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqCZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLxB,SAAS,CAACoD,MAAM,GAAG,CAAC,iBACnBpE,OAAA;UAAK8C,SAAS,EAAC,sEAAsE;UAAAhC,QAAA,gBACnFd,OAAA;YAAK8C,SAAS,EAAC,wCAAwC;YAAAhC,QAAA,gBACrDd,OAAA;cAAM8C,SAAS,EAAC,yCAAyC;cAAAhC,QAAA,EAAC;YAAM;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvExC,OAAA;cAAM8C,SAAS,EAAC,0CAA0C;cAAAhC,QAAA,GAAC,GACxD,EAACI,UAAU,CAACqD,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNxC,OAAA;YAAQ8C,SAAS,EAAC,8PAA8P;YAAAhC,QAAA,EAAC;UAEjR;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACG,GAAA,CA9HID,YAAY;EAAA,QAC0DxC,OAAO;AAAA;AAAAsE,GAAA,GAD7E9B,YAAY;AAgIlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}