{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import{Link}from'react-router-dom';import{ArrowDownTrayIcon as CloudDownloadIcon,ShieldCheckIcon,ClockIcon,StarIcon,ComputerDesktopIcon,CheckCircleIcon,InformationCircleIcon}from'@heroicons/react/24/outline';import{StarIcon as StarIconSolid}from'@heroicons/react/24/solid';import{getDigitalProducts}from'../data/products';import{useCart}from'../components/ShoppingCart';import toast,{Toaster}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DigitalProductsPage=()=>{const[selectedCategory,setSelectedCategory]=useState('all');const[selectedPlatform,setSelectedPlatform]=useState('all');const{addToCart}=useCart();const digitalProducts=getDigitalProducts();const handleAddToCart=product=>{addToCart(product);toast.success(\"\".concat(product.name,\" added to cart!\"),{duration:3000,position:'top-right'});};const digitalCategories=[{id:'all',name:'All Digital Products',icon:'💿'},{id:'software',name:'Software & Licenses',icon:'💻'},{id:'gaming',name:'Gaming',icon:'🎮'}];const platforms=[{id:'all',name:'All Platforms'},{id:'Windows',name:'Windows'},{id:'macOS',name:'macOS'},{id:'Steam',name:'Steam'},{id:'Xbox Console',name:'Xbox'},{id:'PlayStation',name:'PlayStation'}];const filteredProducts=digitalProducts.filter(product=>{const categoryMatch=selectedCategory==='all'||product.category===selectedCategory;const platformMatch=selectedPlatform==='all'||product.platforms&&product.platforms.includes(selectedPlatform)||product.platform===selectedPlatform;return categoryMatch&&platformMatch;});const DigitalProductCard=_ref=>{let{product}=_ref;return/*#__PURE__*/_jsxs(motion.div,{whileHover:{y:-5},className:\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:product.images[0],alt:product.name,className:\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",children:product.badge||'Digital'})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-3 h-3 mr-1\"}),\"Instant\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-2\",children:product.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex\",children:[...Array(5)].map((_,i)=>i<Math.floor(product.rating)?/*#__PURE__*/_jsx(StarIconSolid,{className:\"w-4 h-4 text-yellow-400\"},i):/*#__PURE__*/_jsx(StarIcon,{className:\"w-4 h-4 text-gray-300\"},i))}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-600 ml-2\",children:[product.rating,\" (\",product.reviews,\")\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-4\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-2xl font-bold text-light-orange-600\",children:[\"$\",product.price]}),product.originalPrice&&product.originalPrice>product.price&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg text-gray-500 line-through\",children:[\"$\",product.originalPrice]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 space-y-2\",children:[product.platforms&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ComputerDesktopIcon,{className:\"w-4 h-4 text-gray-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:product.platforms.join(', ')})]}),product.licenseType&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-4 h-4 text-green-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-green-600\",children:product.licenseType})]}),product.validityPeriod&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"w-4 h-4 text-blue-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-blue-600\",children:product.validityPeriod})]})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>handleAddToCart(product),className:\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Get Instantly\"})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Toaster,{position:\"top-right\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 py-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl lg:text-5xl font-bold text-white mb-6\",children:\"Digital Products\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-blue-100 max-w-2xl mx-auto mb-8\",children:\"Instant access to software licenses, games, and digital content. Download immediately after purchase with lifetime support.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(CloudDownloadIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"Instant Delivery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"Get your license keys and download links immediately\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"100% Genuine\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"All licenses are authentic and verified\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"w-8 h-8 text-white mx-auto mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-2\",children:\"Lifetime Support\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"Get help whenever you need it\"})]})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white border-b\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-4 justify-center\",children:digitalCategories.map(category=>/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>setSelectedCategory(category.id),className:\"flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all \".concat(selectedCategory===category.id?'bg-blue-500 text-white shadow-lg':'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg\",children:category.icon}),/*#__PURE__*/_jsx(\"span\",{children:category.name})]},category.id))})})}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:w-64 flex-shrink-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Filters\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-gray-900 mb-3\",children:\"Platform\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:platforms.map(platform=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedPlatform(platform.id),className:\"w-full text-left px-3 py-2 rounded-lg transition-colors \".concat(selectedPlatform===platform.id?'bg-blue-100 text-blue-700':'text-gray-600 hover:bg-gray-100'),children:platform.name},platform.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(InformationCircleIcon,{className:\"w-5 h-5 text-blue-600 mr-2\"}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium text-blue-900\",children:\"Digital Delivery\"})]}),/*#__PURE__*/_jsxs(\"ul\",{className:\"text-sm text-blue-700 space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Instant email delivery\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 No shipping required\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 24/7 download access\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2022 Secure activation\"})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Showing \",filteredProducts.length,\" digital products\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8\",children:filteredProducts.map((product,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3,delay:index*0.1},children:/*#__PURE__*/_jsx(DigitalProductCard,{product:product})},product.id))}),filteredProducts.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-400 mb-4\",children:/*#__PURE__*/_jsx(ComputerDesktopIcon,{className:\"w-16 h-16 mx-auto\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-gray-900 mb-2\",children:\"No digital products found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Try adjusting your filters to see more results.\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 to-purple-600 py-16\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-white mb-4\",children:\"Need Help Choosing?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-blue-100 mb-8\",children:\"Our experts are here to help you find the perfect software solution\"}),/*#__PURE__*/_jsx(Link,{to:\"/contact\",className:\"inline-flex items-center bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\",children:\"Contact Support\"})]})})]});};export default DigitalProductsPage;", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "ArrowDownTrayIcon", "CloudDownloadIcon", "ShieldCheckIcon", "ClockIcon", "StarIcon", "ComputerDesktopIcon", "CheckCircleIcon", "InformationCircleIcon", "StarIconSolid", "getDigitalProducts", "useCart", "toast", "Toaster", "jsx", "_jsx", "jsxs", "_jsxs", "DigitalProductsPage", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedPlatform", "setSelectedPlatform", "addToCart", "digitalProducts", "handleAddToCart", "product", "success", "concat", "name", "duration", "position", "digitalCategories", "id", "icon", "platforms", "filteredProducts", "filter", "categoryMatch", "category", "platformMatch", "includes", "platform", "DigitalProductCard", "_ref", "div", "whileHover", "y", "className", "children", "src", "images", "alt", "badge", "Array", "map", "_", "i", "Math", "floor", "rating", "reviews", "price", "originalPrice", "join", "licenseType", "validityPeriod", "button", "scale", "whileTap", "onClick", "initial", "opacity", "animate", "length", "index", "transition", "delay", "to"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/DigitalProductsPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport {\n  ArrowDownTrayIcon as CloudDownloadIcon,\n  ShieldCheckIcon,\n  ClockIcon,\n  StarIcon,\n  ComputerDesktopIcon,\n  CheckCircleIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\nimport { getDigitalProducts } from '../data/products';\nimport { useCart } from '../components/ShoppingCart';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst DigitalProductsPage = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedPlatform, setSelectedPlatform] = useState('all');\n  const { addToCart } = useCart();\n\n  const digitalProducts = getDigitalProducts();\n\n  const handleAddToCart = (product) => {\n    addToCart(product);\n    toast.success(`${product.name} added to cart!`, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  };\n  \n  const digitalCategories = [\n    { id: 'all', name: 'All Digital Products', icon: '💿' },\n    { id: 'software', name: 'Software & Licenses', icon: '💻' },\n    { id: 'gaming', name: 'Gaming', icon: '🎮' }\n  ];\n\n  const platforms = [\n    { id: 'all', name: 'All Platforms' },\n    { id: 'Windows', name: 'Windows' },\n    { id: 'macOS', name: 'macOS' },\n    { id: 'Steam', name: 'Steam' },\n    { id: 'Xbox Console', name: 'Xbox' },\n    { id: 'PlayStation', name: 'PlayStation' }\n  ];\n\n  const filteredProducts = digitalProducts.filter(product => {\n    const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;\n    const platformMatch = selectedPlatform === 'all' || \n      (product.platforms && product.platforms.includes(selectedPlatform)) ||\n      product.platform === selectedPlatform;\n    return categoryMatch && platformMatch;\n  });\n\n  const DigitalProductCard = ({ product }) => (\n    <motion.div\n      whileHover={{ y: -5 }}\n      className=\"bg-white rounded-2xl shadow-lg overflow-hidden group cursor-pointer\"\n    >\n      <div className=\"relative\">\n        <img\n          src={product.images[0]}\n          alt={product.name}\n          className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n        />\n        <div className=\"absolute top-4 left-4\">\n          <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n            {product.badge || 'Digital'}\n          </span>\n        </div>\n        <div className=\"absolute top-4 right-4\">\n          <span className=\"bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center\">\n            <ClockIcon className=\"w-3 h-3 mr-1\" />\n            Instant\n          </span>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{product.name}</h3>\n        \n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex\">\n            {[...Array(5)].map((_, i) => (\n              i < Math.floor(product.rating) ? (\n                <StarIconSolid key={i} className=\"w-4 h-4 text-yellow-400\" />\n              ) : (\n                <StarIcon key={i} className=\"w-4 h-4 text-gray-300\" />\n              )\n            ))}\n          </div>\n          <span className=\"text-sm text-gray-600 ml-2\">\n            {product.rating} ({product.reviews})\n          </span>\n        </div>\n\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <span className=\"text-2xl font-bold text-light-orange-600\">\n            ${product.price}\n          </span>\n          {product.originalPrice && product.originalPrice > product.price && (\n            <span className=\"text-lg text-gray-500 line-through\">\n              ${product.originalPrice}\n            </span>\n          )}\n        </div>\n\n        {/* Platform/License Info */}\n        <div className=\"mb-4 space-y-2\">\n          {product.platforms && (\n            <div className=\"flex items-center space-x-2\">\n              <ComputerDesktopIcon className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-sm text-gray-600\">\n                {product.platforms.join(', ')}\n              </span>\n            </div>\n          )}\n          {product.licenseType && (\n            <div className=\"flex items-center space-x-2\">\n              <ShieldCheckIcon className=\"w-4 h-4 text-green-500\" />\n              <span className=\"text-sm text-green-600\">{product.licenseType}</span>\n            </div>\n          )}\n          {product.validityPeriod && (\n            <div className=\"flex items-center space-x-2\">\n              <ClockIcon className=\"w-4 h-4 text-blue-500\" />\n              <span className=\"text-sm text-blue-600\">{product.validityPeriod}</span>\n            </div>\n          )}\n        </div>\n\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={() => handleAddToCart(product)}\n          className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center justify-center space-x-2\"\n        >\n          <CloudDownloadIcon className=\"w-5 h-5\" />\n          <span>Get Instantly</span>\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Toaster position=\"top-right\" />\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-6\">\n              Digital Products\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-2xl mx-auto mb-8\">\n              Instant access to software licenses, games, and digital content. \n              Download immediately after purchase with lifetime support.\n            </p>\n            \n            {/* Key Benefits */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\">\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <CloudDownloadIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Instant Delivery</h3>\n                <p className=\"text-blue-100 text-sm\">Get your license keys and download links immediately</p>\n              </div>\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <ShieldCheckIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">100% Genuine</h3>\n                <p className=\"text-blue-100 text-sm\">All licenses are authentic and verified</p>\n              </div>\n              <div className=\"bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6\">\n                <CheckCircleIcon className=\"w-8 h-8 text-white mx-auto mb-3\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Lifetime Support</h3>\n                <p className=\"text-blue-100 text-sm\">Get help whenever you need it</p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Category Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4 justify-center\">\n            {digitalCategories.map((category) => (\n              <motion.button\n                key={category.id}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${\n                  selectedCategory === category.id\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700'\n                }`}\n              >\n                <span className=\"text-lg\">{category.icon}</span>\n                <span>{category.name}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-64 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Filters</h3>\n              \n              {/* Platform Filter */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium text-gray-900 mb-3\">Platform</h4>\n                <div className=\"space-y-2\">\n                  {platforms.map(platform => (\n                    <button\n                      key={platform.id}\n                      onClick={() => setSelectedPlatform(platform.id)}\n                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${\n                        selectedPlatform === platform.id\n                          ? 'bg-blue-100 text-blue-700'\n                          : 'text-gray-600 hover:bg-gray-100'\n                      }`}\n                    >\n                      {platform.name}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Digital Product Info */}\n              <div className=\"bg-blue-50 rounded-lg p-4\">\n                <div className=\"flex items-center mb-2\">\n                  <InformationCircleIcon className=\"w-5 h-5 text-blue-600 mr-2\" />\n                  <h4 className=\"font-medium text-blue-900\">Digital Delivery</h4>\n                </div>\n                <ul className=\"text-sm text-blue-700 space-y-1\">\n                  <li>• Instant email delivery</li>\n                  <li>• No shipping required</li>\n                  <li>• 24/7 download access</li>\n                  <li>• Secure activation</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Products Grid */}\n          <div className=\"flex-1\">\n            <div className=\"mb-6\">\n              <p className=\"text-gray-600\">\n                Showing {filteredProducts.length} digital products\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8\">\n              {filteredProducts.map((product, index) => (\n                <motion.div\n                  key={product.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                >\n                  <DigitalProductCard product={product} />\n                </motion.div>\n              ))}\n            </div>\n\n            {filteredProducts.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <ComputerDesktopIcon className=\"w-16 h-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No digital products found</h3>\n                <p className=\"text-gray-600\">Try adjusting your filters to see more results.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 py-16\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">\n            Need Help Choosing?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Our experts are here to help you find the perfect software solution\n          </p>\n          <Link\n            to=\"/contact\"\n            className=\"inline-flex items-center bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\"\n          >\n            Contact Support\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DigitalProductsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,iBAAiB,GAAI,CAAAC,iBAAiB,CACtCC,eAAe,CACfC,SAAS,CACTC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,qBAAqB,KAChB,6BAA6B,CACpC,OAASH,QAAQ,GAAI,CAAAI,aAAa,KAAQ,2BAA2B,CACrE,OAASC,kBAAkB,KAAQ,kBAAkB,CACrD,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,KAAK,EAAIC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAEyB,SAAU,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAE/B,KAAM,CAAAa,eAAe,CAAGd,kBAAkB,CAAC,CAAC,CAE5C,KAAM,CAAAe,eAAe,CAAIC,OAAO,EAAK,CACnCH,SAAS,CAACG,OAAO,CAAC,CAClBd,KAAK,CAACe,OAAO,IAAAC,MAAA,CAAIF,OAAO,CAACG,IAAI,oBAAmB,CAC9CC,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,WACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAG,CACxB,CAAEC,EAAE,CAAE,KAAK,CAAEJ,IAAI,CAAE,sBAAsB,CAAEK,IAAI,CAAE,IAAK,CAAC,CACvD,CAAED,EAAE,CAAE,UAAU,CAAEJ,IAAI,CAAE,qBAAqB,CAAEK,IAAI,CAAE,IAAK,CAAC,CAC3D,CAAED,EAAE,CAAE,QAAQ,CAAEJ,IAAI,CAAE,QAAQ,CAAEK,IAAI,CAAE,IAAK,CAAC,CAC7C,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEF,EAAE,CAAE,KAAK,CAAEJ,IAAI,CAAE,eAAgB,CAAC,CACpC,CAAEI,EAAE,CAAE,SAAS,CAAEJ,IAAI,CAAE,SAAU,CAAC,CAClC,CAAEI,EAAE,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAQ,CAAC,CAC9B,CAAEI,EAAE,CAAE,OAAO,CAAEJ,IAAI,CAAE,OAAQ,CAAC,CAC9B,CAAEI,EAAE,CAAE,cAAc,CAAEJ,IAAI,CAAE,MAAO,CAAC,CACpC,CAAEI,EAAE,CAAE,aAAa,CAAEJ,IAAI,CAAE,aAAc,CAAC,CAC3C,CAED,KAAM,CAAAO,gBAAgB,CAAGZ,eAAe,CAACa,MAAM,CAACX,OAAO,EAAI,CACzD,KAAM,CAAAY,aAAa,CAAGnB,gBAAgB,GAAK,KAAK,EAAIO,OAAO,CAACa,QAAQ,GAAKpB,gBAAgB,CACzF,KAAM,CAAAqB,aAAa,CAAGnB,gBAAgB,GAAK,KAAK,EAC7CK,OAAO,CAACS,SAAS,EAAIT,OAAO,CAACS,SAAS,CAACM,QAAQ,CAACpB,gBAAgB,CAAE,EACnEK,OAAO,CAACgB,QAAQ,GAAKrB,gBAAgB,CACvC,MAAO,CAAAiB,aAAa,EAAIE,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAG,kBAAkB,CAAGC,IAAA,MAAC,CAAElB,OAAQ,CAAC,CAAAkB,IAAA,oBACrC3B,KAAA,CAAClB,MAAM,CAAC8C,GAAG,EACTC,UAAU,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBC,SAAS,CAAC,qEAAqE,CAAAC,QAAA,eAE/EhC,KAAA,QAAK+B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBlC,IAAA,QACEmC,GAAG,CAAExB,OAAO,CAACyB,MAAM,CAAC,CAAC,CAAE,CACvBC,GAAG,CAAE1B,OAAO,CAACG,IAAK,CAClBmB,SAAS,CAAC,kFAAkF,CAC7F,CAAC,cACFjC,IAAA,QAAKiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpClC,IAAA,SAAMiC,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAClFvB,OAAO,CAAC2B,KAAK,EAAI,SAAS,CACvB,CAAC,CACJ,CAAC,cACNtC,IAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrChC,KAAA,SAAM+B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,eACjGlC,IAAA,CAACX,SAAS,EAAC4C,SAAS,CAAC,cAAc,CAAE,CAAC,UAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,cAEN/B,KAAA,QAAK+B,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBlC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEvB,OAAO,CAACG,IAAI,CAAK,CAAC,cAE5EZ,KAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClC,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClB,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAEC,CAAC,GACtBA,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACjC,OAAO,CAACkC,MAAM,CAAC,cAC5B7C,IAAA,CAACN,aAAa,EAASuC,SAAS,CAAC,yBAAyB,EAAtCS,CAAwC,CAAC,cAE7D1C,IAAA,CAACV,QAAQ,EAAS2C,SAAS,CAAC,uBAAuB,EAApCS,CAAsC,CAExD,CAAC,CACC,CAAC,cACNxC,KAAA,SAAM+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACzCvB,OAAO,CAACkC,MAAM,CAAC,IAAE,CAAClC,OAAO,CAACmC,OAAO,CAAC,GACrC,EAAM,CAAC,EACJ,CAAC,cAEN5C,KAAA,QAAK+B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/ChC,KAAA,SAAM+B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAAC,GACxD,CAACvB,OAAO,CAACoC,KAAK,EACX,CAAC,CACNpC,OAAO,CAACqC,aAAa,EAAIrC,OAAO,CAACqC,aAAa,CAAGrC,OAAO,CAACoC,KAAK,eAC7D7C,KAAA,SAAM+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACvB,OAAO,CAACqC,aAAa,EACnB,CACP,EACE,CAAC,cAGN9C,KAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC5BvB,OAAO,CAACS,SAAS,eAChBlB,KAAA,QAAK+B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClC,IAAA,CAACT,mBAAmB,EAAC0C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACzDjC,IAAA,SAAMiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCvB,OAAO,CAACS,SAAS,CAAC6B,IAAI,CAAC,IAAI,CAAC,CACzB,CAAC,EACJ,CACN,CACAtC,OAAO,CAACuC,WAAW,eAClBhD,KAAA,QAAK+B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClC,IAAA,CAACZ,eAAe,EAAC6C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtDjC,IAAA,SAAMiC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEvB,OAAO,CAACuC,WAAW,CAAO,CAAC,EAClE,CACN,CACAvC,OAAO,CAACwC,cAAc,eACrBjD,KAAA,QAAK+B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClC,IAAA,CAACX,SAAS,EAAC4C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC/CjC,IAAA,SAAMiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEvB,OAAO,CAACwC,cAAc,CAAO,CAAC,EACpE,CACN,EACE,CAAC,cAENjD,KAAA,CAAClB,MAAM,CAACoE,MAAM,EACZrB,UAAU,CAAE,CAAEsB,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAM7C,eAAe,CAACC,OAAO,CAAE,CACxCsB,SAAS,CAAC,yMAAyM,CAAAC,QAAA,eAEnNlC,IAAA,CAACb,iBAAiB,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,cACzCjC,IAAA,SAAAkC,QAAA,CAAM,eAAa,CAAM,CAAC,EACb,CAAC,EACb,CAAC,EACI,CAAC,EACd,CAED,mBACEhC,KAAA,QAAK+B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtClC,IAAA,CAACF,OAAO,EAACkB,QAAQ,CAAC,WAAW,CAAE,CAAC,cAEhChB,IAAA,QAAKiC,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9ElC,IAAA,QAAKiC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDhC,KAAA,CAAClB,MAAM,CAAC8C,GAAG,EACT0B,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEzB,CAAC,CAAE,EAAG,CAAE,CAC/B0B,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEzB,CAAC,CAAE,CAAE,CAAE,CAC9BC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAEvBlC,IAAA,OAAIiC,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAAC,kBAE/D,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,6HAG5D,CAAG,CAAC,cAGJhC,KAAA,QAAK+B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DhC,KAAA,QAAK+B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrElC,IAAA,CAACb,iBAAiB,EAAC8C,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACjEjC,IAAA,OAAIiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3ElC,IAAA,MAAGiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sDAAoD,CAAG,CAAC,EAC1F,CAAC,cACNhC,KAAA,QAAK+B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrElC,IAAA,CAACZ,eAAe,EAAC6C,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC/DjC,IAAA,OAAIiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACvElC,IAAA,MAAGiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yCAAuC,CAAG,CAAC,EAC7E,CAAC,cACNhC,KAAA,QAAK+B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrElC,IAAA,CAACR,eAAe,EAACyC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC/DjC,IAAA,OAAIiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3ElC,IAAA,MAAGiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EACnE,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,CACH,CAAC,cAGNlC,IAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChClC,IAAA,QAAKiC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DlC,IAAA,QAAKiC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CACjDjB,iBAAiB,CAACuB,GAAG,CAAEhB,QAAQ,eAC9BtB,KAAA,CAAClB,MAAM,CAACoE,MAAM,EAEZrB,UAAU,CAAE,CAAEsB,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMlD,mBAAmB,CAACmB,QAAQ,CAACN,EAAE,CAAE,CAChDe,SAAS,kFAAApB,MAAA,CACPT,gBAAgB,GAAKoB,QAAQ,CAACN,EAAE,CAC5B,kCAAkC,CAClC,iEAAiE,CACpE,CAAAgB,QAAA,eAEHlC,IAAA,SAAMiC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAEV,QAAQ,CAACL,IAAI,CAAO,CAAC,cAChDnB,IAAA,SAAAkC,QAAA,CAAOV,QAAQ,CAACV,IAAI,CAAO,CAAC,GAXvBU,QAAQ,CAACN,EAYD,CAChB,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENlB,IAAA,QAAKiC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DhC,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE9ClC,IAAA,QAAKiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpChC,KAAA,QAAK+B,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DlC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAGrEhC,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlC,IAAA,OAAIiC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC5DlC,IAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBd,SAAS,CAACoB,GAAG,CAACb,QAAQ,eACrB3B,IAAA,WAEEuD,OAAO,CAAEA,CAAA,GAAMhD,mBAAmB,CAACoB,QAAQ,CAACT,EAAE,CAAE,CAChDe,SAAS,4DAAApB,MAAA,CACPP,gBAAgB,GAAKqB,QAAQ,CAACT,EAAE,CAC5B,2BAA2B,CAC3B,iCAAiC,CACpC,CAAAgB,QAAA,CAEFP,QAAQ,CAACb,IAAI,EARTa,QAAQ,CAACT,EASR,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNhB,KAAA,QAAK+B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxChC,KAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClC,IAAA,CAACP,qBAAqB,EAACwC,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAChEjC,IAAA,OAAIiC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,EAC5D,CAAC,cACNhC,KAAA,OAAI+B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC7ClC,IAAA,OAAAkC,QAAA,CAAI,+BAAwB,CAAI,CAAC,cACjClC,IAAA,OAAAkC,QAAA,CAAI,6BAAsB,CAAI,CAAC,cAC/BlC,IAAA,OAAAkC,QAAA,CAAI,6BAAsB,CAAI,CAAC,cAC/BlC,IAAA,OAAAkC,QAAA,CAAI,0BAAmB,CAAI,CAAC,EAC1B,CAAC,EACF,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,KAAA,QAAK+B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBlC,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBhC,KAAA,MAAG+B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,UACnB,CAACb,gBAAgB,CAACsC,MAAM,CAAC,mBACnC,EAAG,CAAC,CACD,CAAC,cAEN3D,IAAA,QAAKiC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEb,gBAAgB,CAACmB,GAAG,CAAC,CAAC7B,OAAO,CAAEiD,KAAK,gBACnC5D,IAAA,CAAChB,MAAM,CAAC8C,GAAG,EAET0B,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEzB,CAAC,CAAE,EAAG,CAAE,CAC/B0B,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEzB,CAAC,CAAE,CAAE,CAAE,CAC9B6B,UAAU,CAAE,CAAE9C,QAAQ,CAAE,GAAG,CAAE+C,KAAK,CAAEF,KAAK,CAAG,GAAI,CAAE,CAAA1B,QAAA,cAElDlC,IAAA,CAAC4B,kBAAkB,EAACjB,OAAO,CAAEA,OAAQ,CAAE,CAAC,EALnCA,OAAO,CAACO,EAMH,CACb,CAAC,CACC,CAAC,CAELG,gBAAgB,CAACsC,MAAM,GAAK,CAAC,eAC5BzD,KAAA,QAAK+B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClC,IAAA,QAAKiC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjClC,IAAA,CAACT,mBAAmB,EAAC0C,SAAS,CAAC,mBAAmB,CAAE,CAAC,CAClD,CAAC,cACNjC,IAAA,OAAIiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACvFlC,IAAA,MAAGiC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAGNlC,IAAA,QAAKiC,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjEhC,KAAA,QAAK+B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjElC,IAAA,OAAIiC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,qBAEnD,CAAI,CAAC,cACLlC,IAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qEAE1C,CAAG,CAAC,cACJlC,IAAA,CAACf,IAAI,EACH8E,EAAE,CAAC,UAAU,CACb9B,SAAS,CAAC,uHAAuH,CAAAC,QAAA,CAClI,iBAED,CAAM,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}