{"ast": null, "code": "import React from'react';import{motion}from'framer-motion';import{Link}from'react-router-dom';import{ArrowLeftIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import Button from'../components/Button';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PlaceholderPage=_ref=>{let{title,description,icon:Icon=ExclamationTriangleIcon}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex items-center justify-center\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.6},className:\"text-center max-w-md mx-auto px-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8\",children:[/*#__PURE__*/_jsx(Icon,{className:\"w-16 h-16 text-light-orange-500 mx-auto mb-6\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-gray-900 mb-4\",children:title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-8 leading-relaxed\",children:description||\"The \".concat(title.toLowerCase(),\" page is coming soon. We're working hard to bring you this feature.\")}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",children:/*#__PURE__*/_jsx(Button,{fullWidth:true,icon:ArrowLeftIcon,children:\"Back to Home\"})}),/*#__PURE__*/_jsx(Link,{to:\"/contact\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline\",fullWidth:true,children:\"Contact Support\"})})]})]})})});};// Specific placeholder pages\nexport const HelpPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Help Center\",description:\"Our comprehensive help center is being developed. For immediate assistance, please contact our support team.\"});export const ReturnsPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Returns & Exchanges\",description:\"Learn about our hassle-free return policy and how to exchange your items. This page is currently under construction.\"});export const ShippingPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Shipping Information\",description:\"Find detailed information about our shipping options, delivery times, and tracking. Coming soon!\"});export const TrackOrderPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Track Your Order\",description:\"Track your order status and delivery progress. This feature is being developed and will be available soon.\"});export const PrivacyPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Privacy Policy\",description:\"Your privacy is important to us. Our detailed privacy policy is being finalized and will be available soon.\"});export const TermsPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Terms of Service\",description:\"Review our terms and conditions for using our platform. This page is currently being updated.\"});export const CookiesPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Cookie Policy\",description:\"Learn about how we use cookies to improve your browsing experience. Policy details coming soon.\"});export const OrdersPage=()=>/*#__PURE__*/_jsx(PlaceholderPage,{title:\"Order History\",description:\"View your past orders and track current purchases. This feature is being developed.\"});export default PlaceholderPage;", "map": {"version": 3, "names": ["React", "motion", "Link", "ArrowLeftIcon", "ExclamationTriangleIcon", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "PlaceholderPage", "_ref", "title", "description", "icon", "Icon", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "concat", "toLowerCase", "to", "fullWidth", "variant", "HelpPage", "ReturnsPage", "ShippingPage", "TrackOrderPage", "PrivacyPage", "TermsPage", "CookiesPage", "OrdersPage"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/PlaceholderPage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport Button from '../components/Button';\n\nconst PlaceholderPage = ({ title, description, icon: Icon = ExclamationTriangleIcon }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"text-center max-w-md mx-auto px-4\"\n      >\n        <div className=\"bg-white rounded-2xl shadow-lg p-8\">\n          <Icon className=\"w-16 h-16 text-light-orange-500 mx-auto mb-6\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">{title}</h1>\n          <p className=\"text-gray-600 mb-8 leading-relaxed\">\n            {description || `The ${title.toLowerCase()} page is coming soon. We're working hard to bring you this feature.`}\n          </p>\n          <div className=\"space-y-4\">\n            <Link to=\"/\">\n              <Button fullWidth icon={ArrowLeftIcon}>\n                Back to Home\n              </Button>\n            </Link>\n            <Link to=\"/contact\">\n              <Button variant=\"outline\" fullWidth>\n                Contact Support\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\n// Specific placeholder pages\nexport const HelpPage = () => (\n  <PlaceholderPage \n    title=\"Help Center\" \n    description=\"Our comprehensive help center is being developed. For immediate assistance, please contact our support team.\"\n  />\n);\n\nexport const ReturnsPage = () => (\n  <PlaceholderPage \n    title=\"Returns & Exchanges\" \n    description=\"Learn about our hassle-free return policy and how to exchange your items. This page is currently under construction.\"\n  />\n);\n\nexport const ShippingPage = () => (\n  <PlaceholderPage \n    title=\"Shipping Information\" \n    description=\"Find detailed information about our shipping options, delivery times, and tracking. Coming soon!\"\n  />\n);\n\nexport const TrackOrderPage = () => (\n  <PlaceholderPage \n    title=\"Track Your Order\" \n    description=\"Track your order status and delivery progress. This feature is being developed and will be available soon.\"\n  />\n);\n\nexport const PrivacyPage = () => (\n  <PlaceholderPage \n    title=\"Privacy Policy\" \n    description=\"Your privacy is important to us. Our detailed privacy policy is being finalized and will be available soon.\"\n  />\n);\n\nexport const TermsPage = () => (\n  <PlaceholderPage \n    title=\"Terms of Service\" \n    description=\"Review our terms and conditions for using our platform. This page is currently being updated.\"\n  />\n);\n\nexport const CookiesPage = () => (\n  <PlaceholderPage \n    title=\"Cookie Policy\" \n    description=\"Learn about how we use cookies to improve your browsing experience. Policy details coming soon.\"\n  />\n);\n\nexport const OrdersPage = () => (\n  <PlaceholderPage \n    title=\"Order History\" \n    description=\"View your past orders and track current purchases. This feature is being developed.\"\n  />\n);\n\nexport default PlaceholderPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,aAAa,CAAEC,uBAAuB,KAAQ,6BAA6B,CACpF,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAkE,IAAjE,CAAEC,KAAK,CAAEC,WAAW,CAAEC,IAAI,CAAEC,IAAI,CAAGX,uBAAwB,CAAC,CAAAO,IAAA,CACnF,mBACEJ,IAAA,QAAKS,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvEV,IAAA,CAACN,MAAM,CAACiB,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7CR,KAAA,QAAKO,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDV,IAAA,CAACQ,IAAI,EAACC,SAAS,CAAC,8CAA8C,CAAE,CAAC,cACjET,IAAA,OAAIS,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAEL,KAAK,CAAK,CAAC,cAClEL,IAAA,MAAGS,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAC9CJ,WAAW,SAAAY,MAAA,CAAWb,KAAK,CAACc,WAAW,CAAC,CAAC,uEAAqE,CAC9G,CAAC,cACJjB,KAAA,QAAKO,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBV,IAAA,CAACL,IAAI,EAACyB,EAAE,CAAC,GAAG,CAAAV,QAAA,cACVV,IAAA,CAACF,MAAM,EAACuB,SAAS,MAACd,IAAI,CAAEX,aAAc,CAAAc,QAAA,CAAC,cAEvC,CAAQ,CAAC,CACL,CAAC,cACPV,IAAA,CAACL,IAAI,EAACyB,EAAE,CAAC,UAAU,CAAAV,QAAA,cACjBV,IAAA,CAACF,MAAM,EAACwB,OAAO,CAAC,SAAS,CAACD,SAAS,MAAAX,QAAA,CAAC,iBAEpC,CAAQ,CAAC,CACL,CAAC,EACJ,CAAC,EACH,CAAC,CACI,CAAC,CACV,CAAC,CAEV,CAAC,CAED;AACA,MAAO,MAAM,CAAAa,QAAQ,CAAGA,CAAA,gBACtBvB,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAC,8GAA8G,CAC3H,CACF,CAED,MAAO,MAAM,CAAAkB,WAAW,CAAGA,CAAA,gBACzBxB,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,qBAAqB,CAC3BC,WAAW,CAAC,sHAAsH,CACnI,CACF,CAED,MAAO,MAAM,CAAAmB,YAAY,CAAGA,CAAA,gBAC1BzB,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,sBAAsB,CAC5BC,WAAW,CAAC,kGAAkG,CAC/G,CACF,CAED,MAAO,MAAM,CAAAoB,cAAc,CAAGA,CAAA,gBAC5B1B,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,kBAAkB,CACxBC,WAAW,CAAC,4GAA4G,CACzH,CACF,CAED,MAAO,MAAM,CAAAqB,WAAW,CAAGA,CAAA,gBACzB3B,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,gBAAgB,CACtBC,WAAW,CAAC,6GAA6G,CAC1H,CACF,CAED,MAAO,MAAM,CAAAsB,SAAS,CAAGA,CAAA,gBACvB5B,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,kBAAkB,CACxBC,WAAW,CAAC,+FAA+F,CAC5G,CACF,CAED,MAAO,MAAM,CAAAuB,WAAW,CAAGA,CAAA,gBACzB7B,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,eAAe,CACrBC,WAAW,CAAC,iGAAiG,CAC9G,CACF,CAED,MAAO,MAAM,CAAAwB,UAAU,CAAGA,CAAA,gBACxB9B,IAAA,CAACG,eAAe,EACdE,KAAK,CAAC,eAAe,CACrBC,WAAW,CAAC,qFAAqF,CAClG,CACF,CAED,cAAe,CAAAH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}