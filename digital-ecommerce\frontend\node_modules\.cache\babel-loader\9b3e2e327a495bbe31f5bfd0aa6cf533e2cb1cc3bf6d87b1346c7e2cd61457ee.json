{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\My projects\\\\ecomerce\\\\digital-ecommerce\\\\frontend\\\\src\\\\pages\\\\RegisterPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { EyeIcon, EyeSlashIcon, UserPlusIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegisterPage = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    agreeToTerms: false,\n    marketingEmails: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const {\n    register,\n    isLoading\n  } = useUser();\n  const navigate = useNavigate();\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Calculate password strength\n    if (name === 'password') {\n      calculatePasswordStrength(value);\n    }\n  };\n  const calculatePasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    setPasswordStrength(strength);\n  };\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1:\n        return {\n          text: 'Very Weak',\n          color: 'text-red-500'\n        };\n      case 2:\n        return {\n          text: 'Weak',\n          color: 'text-orange-500'\n        };\n      case 3:\n        return {\n          text: 'Fair',\n          color: 'text-yellow-500'\n        };\n      case 4:\n        return {\n          text: 'Good',\n          color: 'text-blue-500'\n        };\n      case 5:\n        return {\n          text: 'Strong',\n          color: 'text-green-500'\n        };\n      default:\n        return {\n          text: '',\n          color: ''\n        };\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    const result = await register(formData);\n    if (result.success) {\n      toast.success(result.message);\n      navigate('/login', {\n        state: {\n          message: 'Account created successfully! Please sign in.'\n        }\n      });\n    } else {\n      toast.error(result.error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            delay: 0.2,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: \"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(UserPlusIcon, {\n            className: \"h-8 w-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-3xl font-bold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: \"Join us and start your shopping journey\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-8 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"First Name\",\n              name: \"firstName\",\n              value: formData.firstName,\n              onChange: handleInputChange,\n              error: errors.firstName,\n              placeholder: \"John\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              label: \"Last Name\",\n              name: \"lastName\",\n              value: formData.lastName,\n              onChange: handleInputChange,\n              error: errors.lastName,\n              placeholder: \"Doe\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email Address\",\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            error: errors.email,\n            placeholder: \"<EMAIL>\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Phone Number\",\n            type: \"tel\",\n            name: \"phone\",\n            value: formData.phone,\n            onChange: handleInputChange,\n            error: errors.phone,\n            placeholder: \"+****************\",\n            helperText: \"Optional - for order updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Password\",\n              type: showPassword ? 'text' : 'password',\n              name: \"password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              error: errors.password,\n              placeholder: \"Create a strong password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowPassword(!showPassword),\n              className: \"absolute right-3 top-9 text-gray-400 hover:text-gray-600\",\n              children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `h-2 rounded-full transition-all duration-300 ${passwordStrength <= 2 ? 'bg-red-500' : passwordStrength === 3 ? 'bg-yellow-500' : passwordStrength === 4 ? 'bg-blue-500' : 'bg-green-500'}`,\n                    style: {\n                      width: `${passwordStrength / 5 * 100}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs font-medium ${getPasswordStrengthText().color}`,\n                  children: getPasswordStrengthText().text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Confirm Password\",\n              type: showConfirmPassword ? 'text' : 'password',\n              name: \"confirmPassword\",\n              value: formData.confirmPassword,\n              onChange: handleInputChange,\n              error: errors.confirmPassword,\n              placeholder: \"Confirm your password\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowConfirmPassword(!showConfirmPassword),\n              className: \"absolute right-3 top-9 text-gray-400 hover:text-gray-600\",\n              children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), formData.confirmPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-10 top-9\",\n              children: formData.password === formData.confirmPassword ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                className: \"h-5 w-5 text-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(XCircleIcon, {\n                className: \"h-5 w-5 text-red-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"agreeToTerms\",\n                checked: formData.agreeToTerms,\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded mt-0.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600\",\n                children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/terms\",\n                  className: \"text-light-orange-600 hover:text-light-orange-500 font-medium\",\n                  children: \"Terms of Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/privacy\",\n                  className: \"text-light-orange-600 hover:text-light-orange-500 font-medium\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), errors.agreeToTerms && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-600\",\n              children: errors.agreeToTerms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"marketingEmails\",\n                checked: formData.marketingEmails,\n                onChange: handleInputChange,\n                className: \"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600\",\n                children: \"I'd like to receive marketing emails about new products and offers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            loading: isLoading,\n            fullWidth: true,\n            size: \"large\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"font-medium text-light-orange-600 hover:text-light-orange-500\",\n              children: \"Sign in here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"4Jx5/2F3juUTRU7guCwBoPZ5CP8=\", false, function () {\n  return [useUser, useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "motion", "EyeIcon", "EyeSlashIcon", "UserPlusIcon", "CheckCircleIcon", "XCircleIcon", "useUser", "<PERSON><PERSON>", "Input", "toast", "Toaster", "jsxDEV", "_jsxDEV", "RegisterPage", "_s", "formData", "setFormData", "firstName", "lastName", "email", "phone", "password", "confirmPassword", "agreeToTerms", "marketingEmails", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "errors", "setErrors", "passwordStrength", "setPasswordStrength", "register", "isLoading", "navigate", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "calculatePasswordStrength", "strength", "length", "test", "getPasswordStrengthText", "text", "color", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "result", "success", "message", "state", "error", "className", "children", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "scale", "delay", "stiffness", "onSubmit", "label", "onChange", "placeholder", "required", "helperText", "onClick", "style", "width", "to", "loading", "fullWidth", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/My projects/ecomerce/digital-ecommerce/frontend/src/pages/RegisterPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { \n  EyeIcon, \n  EyeSlashIcon, \n  UserPlusIcon,\n  CheckCircleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\nimport { useUser } from '../contexts/UserContext';\nimport Button from '../components/Button';\nimport Input from '../components/Input';\nimport toast, { Toaster } from 'react-hot-toast';\n\nconst RegisterPage = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    agreeToTerms: false,\n    marketingEmails: false\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  \n  const { register, isLoading } = useUser();\n  const navigate = useNavigate();\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n\n    // Calculate password strength\n    if (name === 'password') {\n      calculatePasswordStrength(value);\n    }\n  };\n\n  const calculatePasswordStrength = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    setPasswordStrength(strength);\n  };\n\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return { text: 'Very Weak', color: 'text-red-500' };\n      case 2: return { text: 'Weak', color: 'text-orange-500' };\n      case 3: return { text: 'Fair', color: 'text-yellow-500' };\n      case 4: return { text: 'Good', color: 'text-blue-500' };\n      case 5: return { text: 'Strong', color: 'text-green-500' };\n      default: return { text: '', color: '' };\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required';\n    }\n    \n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required';\n    }\n    \n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    \n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters';\n    }\n    \n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    \n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    const result = await register(formData);\n    \n    if (result.success) {\n      toast.success(result.message);\n      navigate('/login', { \n        state: { message: 'Account created successfully! Please sign in.' }\n      });\n    } else {\n      toast.error(result.error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-light-orange-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <Toaster position=\"top-right\" />\n      \n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"max-w-md w-full space-y-8\"\n      >\n        {/* Header */}\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"mx-auto h-16 w-16 bg-gradient-to-r from-light-orange-500 to-light-orange-600 rounded-full flex items-center justify-center shadow-lg\"\n          >\n            <UserPlusIcon className=\"h-8 w-8 text-white\" />\n          </motion.div>\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Join us and start your shopping journey\n          </p>\n        </div>\n\n        {/* Registration Form */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n          className=\"bg-white rounded-2xl shadow-xl p-8 space-y-6\"\n        >\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Name Fields */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <Input\n                label=\"First Name\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                error={errors.firstName}\n                placeholder=\"John\"\n                required\n              />\n              <Input\n                label=\"Last Name\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                error={errors.lastName}\n                placeholder=\"Doe\"\n                required\n              />\n            </div>\n\n            {/* Email Field */}\n            <Input\n              label=\"Email Address\"\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              error={errors.email}\n              placeholder=\"<EMAIL>\"\n              required\n            />\n\n            {/* Phone Field */}\n            <Input\n              label=\"Phone Number\"\n              type=\"tel\"\n              name=\"phone\"\n              value={formData.phone}\n              onChange={handleInputChange}\n              error={errors.phone}\n              placeholder=\"+****************\"\n              helperText=\"Optional - for order updates\"\n            />\n\n            {/* Password Field */}\n            <div className=\"relative\">\n              <Input\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                placeholder=\"Create a strong password\"\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5\" />\n                )}\n              </button>\n              \n              {/* Password Strength Indicator */}\n              {formData.password && (\n                <div className=\"mt-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-300 ${\n                          passwordStrength <= 2 ? 'bg-red-500' :\n                          passwordStrength === 3 ? 'bg-yellow-500' :\n                          passwordStrength === 4 ? 'bg-blue-500' : 'bg-green-500'\n                        }`}\n                        style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                      />\n                    </div>\n                    <span className={`text-xs font-medium ${getPasswordStrengthText().color}`}>\n                      {getPasswordStrengthText().text}\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Confirm Password Field */}\n            <div className=\"relative\">\n              <Input\n                label=\"Confirm Password\"\n                type={showConfirmPassword ? 'text' : 'password'}\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                error={errors.confirmPassword}\n                placeholder=\"Confirm your password\"\n                required\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                className=\"absolute right-3 top-9 text-gray-400 hover:text-gray-600\"\n              >\n                {showConfirmPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5\" />\n                )}\n              </button>\n              \n              {/* Password Match Indicator */}\n              {formData.confirmPassword && (\n                <div className=\"absolute right-10 top-9\">\n                  {formData.password === formData.confirmPassword ? (\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />\n                  ) : (\n                    <XCircleIcon className=\"h-5 w-5 text-red-500\" />\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Checkboxes */}\n            <div className=\"space-y-4\">\n              <label className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  name=\"agreeToTerms\"\n                  checked={formData.agreeToTerms}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded mt-0.5\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  I agree to the{' '}\n                  <Link to=\"/terms\" className=\"text-light-orange-600 hover:text-light-orange-500 font-medium\">\n                    Terms of Service\n                  </Link>{' '}\n                  and{' '}\n                  <Link to=\"/privacy\" className=\"text-light-orange-600 hover:text-light-orange-500 font-medium\">\n                    Privacy Policy\n                  </Link>\n                </span>\n              </label>\n              {errors.agreeToTerms && (\n                <p className=\"text-sm text-red-600\">{errors.agreeToTerms}</p>\n              )}\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  name=\"marketingEmails\"\n                  checked={formData.marketingEmails}\n                  onChange={handleInputChange}\n                  className=\"h-4 w-4 text-light-orange-600 focus:ring-light-orange-500 border-gray-300 rounded\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  I'd like to receive marketing emails about new products and offers\n                </span>\n              </label>\n            </div>\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              loading={isLoading}\n              fullWidth\n              size=\"large\"\n            >\n              Create Account\n            </Button>\n          </form>\n\n          {/* Sign In Link */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Already have an account?{' '}\n              <Link\n                to=\"/login\"\n                className=\"font-medium text-light-orange-600 hover:text-light-orange-500\"\n              >\n                Sign in here\n              </Link>\n            </p>\n          </div>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,WAAW,QACN,6BAA6B;AACpC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAM;IAAEoC,QAAQ;IAAEC;EAAU,CAAC,GAAG5B,OAAO,CAAC,CAAC;EACzC,MAAM6B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAE9B,MAAMqC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/C1B,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBR,SAAS,CAACa,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACL,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAC9C;;IAEA;IACA,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvBM,yBAAyB,CAACL,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAMK,yBAAyB,GAAIvB,QAAQ,IAAK;IAC9C,IAAIwB,QAAQ,GAAG,CAAC;IAChB,IAAIxB,QAAQ,CAACyB,MAAM,IAAI,CAAC,EAAED,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACE,IAAI,CAAC1B,QAAQ,CAAC,EAAEwB,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACE,IAAI,CAAC1B,QAAQ,CAAC,EAAEwB,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACE,IAAI,CAAC1B,QAAQ,CAAC,EAAEwB,QAAQ,EAAE;IACtC,IAAI,cAAc,CAACE,IAAI,CAAC1B,QAAQ,CAAC,EAAEwB,QAAQ,EAAE;IAC7Cb,mBAAmB,CAACa,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAMG,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQjB,gBAAgB;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO;UAAEkB,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAe,CAAC;MAC3D,KAAK,CAAC;QAAE,OAAO;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAkB,CAAC;MACzD,KAAK,CAAC;QAAE,OAAO;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAkB,CAAC;MACzD,KAAK,CAAC;QAAE,OAAO;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAgB,CAAC;MACvD,KAAK,CAAC;QAAE,OAAO;UAAED,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAiB,CAAC;MAC1D;QAAS,OAAO;UAAED,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAG,CAAC;IACzC;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrC,QAAQ,CAACE,SAAS,CAACoC,IAAI,CAAC,CAAC,EAAE;MAC9BD,SAAS,CAACnC,SAAS,GAAG,wBAAwB;IAChD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACmC,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAClC,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE;MACnBiC,SAAS,CAACjC,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC4B,IAAI,CAAChC,QAAQ,CAACI,KAAK,CAAC,EAAE;MAC/CiC,SAAS,CAACjC,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACJ,QAAQ,CAACM,QAAQ,EAAE;MACtB+B,SAAS,CAAC/B,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIN,QAAQ,CAACM,QAAQ,CAACyB,MAAM,GAAG,CAAC,EAAE;MACvCM,SAAS,CAAC/B,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAI,CAACN,QAAQ,CAACO,eAAe,EAAE;MAC7B8B,SAAS,CAAC9B,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIP,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;MACzD8B,SAAS,CAAC9B,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAI,CAACP,QAAQ,CAACQ,YAAY,EAAE;MAC1B6B,SAAS,CAAC7B,YAAY,GAAG,4CAA4C;IACvE;IAEAO,SAAS,CAACsB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACN,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOnB,CAAC,IAAK;IAChCA,CAAC,CAACoB,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErB,MAAMO,MAAM,GAAG,MAAMzB,QAAQ,CAAClB,QAAQ,CAAC;IAEvC,IAAI2C,MAAM,CAACC,OAAO,EAAE;MAClBlD,KAAK,CAACkD,OAAO,CAACD,MAAM,CAACE,OAAO,CAAC;MAC7BzB,QAAQ,CAAC,QAAQ,EAAE;QACjB0B,KAAK,EAAE;UAAED,OAAO,EAAE;QAAgD;MACpE,CAAC,CAAC;IACJ,CAAC,MAAM;MACLnD,KAAK,CAACqD,KAAK,CAACJ,MAAM,CAACI,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,oBACElD,OAAA;IAAKmD,SAAS,EAAC,0HAA0H;IAAAC,QAAA,gBACvIpD,OAAA,CAACF,OAAO;MAACuD,QAAQ,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhCzD,OAAA,CAACZ,MAAM,CAACsE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9Bb,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAGrCpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA,CAACZ,MAAM,CAACsE,GAAG;UACTC,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,OAAO,EAAE;YAAEG,KAAK,EAAE;UAAE,CAAE;UACtBF,UAAU,EAAE;YAAEG,KAAK,EAAE,GAAG;YAAEtC,IAAI,EAAE,QAAQ;YAAEuC,SAAS,EAAE;UAAI,CAAE;UAC3DhB,SAAS,EAAC,sIAAsI;UAAAC,QAAA,eAEhJpD,OAAA,CAACT,YAAY;YAAC4D,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACbzD,OAAA;UAAImD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzD,OAAA;UAAGmD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNzD,OAAA,CAACZ,MAAM,CAACsE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC3Bf,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAExDpD,OAAA;UAAMoE,QAAQ,EAAExB,YAAa;UAACO,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjDpD,OAAA;YAAKmD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCpD,OAAA,CAACJ,KAAK;cACJyE,KAAK,EAAC,YAAY;cAClB3C,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAExB,QAAQ,CAACE,SAAU;cAC1BiE,QAAQ,EAAE9C,iBAAkB;cAC5B0B,KAAK,EAAEjC,MAAM,CAACZ,SAAU;cACxBkE,WAAW,EAAC,MAAM;cAClBC,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFzD,OAAA,CAACJ,KAAK;cACJyE,KAAK,EAAC,WAAW;cACjB3C,IAAI,EAAC,UAAU;cACfC,KAAK,EAAExB,QAAQ,CAACG,QAAS;cACzBgE,QAAQ,EAAE9C,iBAAkB;cAC5B0B,KAAK,EAAEjC,MAAM,CAACX,QAAS;cACvBiE,WAAW,EAAC,KAAK;cACjBC,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA,CAACJ,KAAK;YACJyE,KAAK,EAAC,eAAe;YACrBzC,IAAI,EAAC,OAAO;YACZF,IAAI,EAAC,OAAO;YACZC,KAAK,EAAExB,QAAQ,CAACI,KAAM;YACtB+D,QAAQ,EAAE9C,iBAAkB;YAC5B0B,KAAK,EAAEjC,MAAM,CAACV,KAAM;YACpBgE,WAAW,EAAC,kBAAkB;YAC9BC,QAAQ;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGFzD,OAAA,CAACJ,KAAK;YACJyE,KAAK,EAAC,cAAc;YACpBzC,IAAI,EAAC,KAAK;YACVF,IAAI,EAAC,OAAO;YACZC,KAAK,EAAExB,QAAQ,CAACK,KAAM;YACtB8D,QAAQ,EAAE9C,iBAAkB;YAC5B0B,KAAK,EAAEjC,MAAM,CAACT,KAAM;YACpB+D,WAAW,EAAC,mBAAmB;YAC/BE,UAAU,EAAC;UAA8B;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAGFzD,OAAA;YAAKmD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBpD,OAAA,CAACJ,KAAK;cACJyE,KAAK,EAAC,UAAU;cAChBzC,IAAI,EAAEf,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCa,IAAI,EAAC,UAAU;cACfC,KAAK,EAAExB,QAAQ,CAACM,QAAS;cACzB6D,QAAQ,EAAE9C,iBAAkB;cAC5B0B,KAAK,EAAEjC,MAAM,CAACR,QAAS;cACvB8D,WAAW,EAAC,0BAA0B;cACtCC,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFzD,OAAA;cACE4B,IAAI,EAAC,QAAQ;cACb8C,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CsC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAEnEvC,YAAY,gBACXb,OAAA,CAACV,YAAY;gBAAC6D,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpCzD,OAAA,CAACX,OAAO;gBAAC8D,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EAGRtD,QAAQ,CAACM,QAAQ,iBAChBT,OAAA;cAAKmD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBpD,OAAA;gBAAKmD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpD,OAAA;kBAAKmD,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDpD,OAAA;oBACEmD,SAAS,EAAE,gDACThC,gBAAgB,IAAI,CAAC,GAAG,YAAY,GACpCA,gBAAgB,KAAK,CAAC,GAAG,eAAe,GACxCA,gBAAgB,KAAK,CAAC,GAAG,aAAa,GAAG,cAAc,EACtD;oBACHwD,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAIzD,gBAAgB,GAAG,CAAC,GAAI,GAAG;oBAAI;kBAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNzD,OAAA;kBAAMmD,SAAS,EAAE,uBAAuBf,uBAAuB,CAAC,CAAC,CAACE,KAAK,EAAG;kBAAAc,QAAA,EACvEhB,uBAAuB,CAAC,CAAC,CAACC;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKmD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBpD,OAAA,CAACJ,KAAK;cACJyE,KAAK,EAAC,kBAAkB;cACxBzC,IAAI,EAAEb,mBAAmB,GAAG,MAAM,GAAG,UAAW;cAChDW,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAExB,QAAQ,CAACO,eAAgB;cAChC4D,QAAQ,EAAE9C,iBAAkB;cAC5B0B,KAAK,EAAEjC,MAAM,CAACP,eAAgB;cAC9B6D,WAAW,EAAC,uBAAuB;cACnCC,QAAQ;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFzD,OAAA;cACE4B,IAAI,EAAC,QAAQ;cACb8C,OAAO,EAAEA,CAAA,KAAM1D,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAC5DoC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAEnErC,mBAAmB,gBAClBf,OAAA,CAACV,YAAY;gBAAC6D,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpCzD,OAAA,CAACX,OAAO;gBAAC8D,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EAGRtD,QAAQ,CAACO,eAAe,iBACvBV,OAAA;cAAKmD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCjD,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,gBAC7CV,OAAA,CAACR,eAAe;gBAAC2D,SAAS,EAAC;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtDzD,OAAA,CAACP,WAAW;gBAAC0D,SAAS,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAChD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKmD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpD,OAAA;cAAOmD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACjCpD,OAAA;gBACE4B,IAAI,EAAC,UAAU;gBACfF,IAAI,EAAC,cAAc;gBACnBG,OAAO,EAAE1B,QAAQ,CAACQ,YAAa;gBAC/B2D,QAAQ,EAAE9C,iBAAkB;gBAC5B2B,SAAS,EAAC;cAA0F;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,eACFzD,OAAA;gBAAMmD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,gBAC7B,EAAC,GAAG,eAClBpD,OAAA,CAACd,IAAI;kBAAC2F,EAAE,EAAC,QAAQ;kBAAC1B,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAAC;gBAE5F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACPzD,OAAA,CAACd,IAAI;kBAAC2F,EAAE,EAAC,UAAU;kBAAC1B,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAAC;gBAE9F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EACPxC,MAAM,CAACN,YAAY,iBAClBX,OAAA;cAAGmD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAEnC,MAAM,CAACN;YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7D,eAEDzD,OAAA;cAAOmD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCpD,OAAA;gBACE4B,IAAI,EAAC,UAAU;gBACfF,IAAI,EAAC,iBAAiB;gBACtBG,OAAO,EAAE1B,QAAQ,CAACS,eAAgB;gBAClC0D,QAAQ,EAAE9C,iBAAkB;gBAC5B2B,SAAS,EAAC;cAAmF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC,eACFzD,OAAA;gBAAMmD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNzD,OAAA,CAACL,MAAM;YACLiC,IAAI,EAAC,QAAQ;YACbkD,OAAO,EAAExD,SAAU;YACnByD,SAAS;YACTC,IAAI,EAAC,OAAO;YAAA5B,QAAA,EACb;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGPzD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA;YAAGmD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,0BACX,EAAC,GAAG,eAC5BpD,OAAA,CAACd,IAAI;cACH2F,EAAE,EAAC,QAAQ;cACX1B,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACvD,EAAA,CAvVID,YAAY;EAAA,QAgBgBP,OAAO,EACtBP,WAAW;AAAA;AAAA8F,EAAA,GAjBxBhF,YAAY;AAyVlB,eAAeA,YAAY;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}